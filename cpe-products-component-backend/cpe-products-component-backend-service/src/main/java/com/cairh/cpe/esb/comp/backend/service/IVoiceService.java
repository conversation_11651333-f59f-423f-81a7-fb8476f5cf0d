package com.cairh.cpe.esb.comp.backend.service;


import com.cairh.cpe.component.common.form.request.BackendText2VoiceReq;
import com.cairh.cpe.component.common.form.request.BackendVoice2TextReq;
import com.cairh.cpe.component.common.form.response.BackendText2VoiceResp;
import com.cairh.cpe.component.common.form.response.BackenfVoice2TextResp;

/**
 * <AUTHOR>
 * @since 2022-07-13
 */
public interface IVoiceService {

    /**
     * 文字转语音 返回文件id 与链接
     * @param request
     * @return
     */
    BackendText2VoiceResp text2Voice(BackendText2VoiceReq request);

    /**
     * 语音转文字
     * @param request
     * @return
     */
    BackenfVoice2TextResp voice2Text(BackendVoice2TextReq request);
}
