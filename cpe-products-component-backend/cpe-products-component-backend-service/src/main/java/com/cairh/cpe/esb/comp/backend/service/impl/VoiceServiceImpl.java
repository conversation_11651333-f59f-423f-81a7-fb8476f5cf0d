package com.cairh.cpe.esb.comp.backend.service.impl;

import com.cairh.cpe.common.backend.util.BaseBeanUtil;
import com.cairh.cpe.component.common.form.request.BackendText2VoiceReq;
import com.cairh.cpe.component.common.form.request.BackendVoice2TextReq;
import com.cairh.cpe.component.common.form.response.BackendText2VoiceResp;
import com.cairh.cpe.component.common.form.response.BackenfVoice2TextResp;
import com.cairh.cpe.esb.comp.backend.service.IFileManageService;
import com.cairh.cpe.esb.comp.backend.service.IVoiceService;
import com.cairh.cpe.esb.component.elect.dto.req.ElectUploadFileRequest;
import com.cairh.cpe.esb.component.elect.dto.resp.ElectUploadFileResponse;
import com.cairh.cpe.esb.component.voice.IEsbComponentVoiceDubboService;
import com.cairh.cpe.esb.component.voice.dto.req.TtsRequest;
import com.cairh.cpe.esb.component.voice.dto.req.AsrRequest;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.UUID;

/**
 * <AUTHOR>
 * @since 2022-07-13
 */
@Service
public class VoiceServiceImpl implements IVoiceService {

    @DubboReference(check = false)
    private IEsbComponentVoiceDubboService esbComponentVoiceDubboService;

    @Autowired
    private IFileManageService fileManageService;

    @Override
    public BackendText2VoiceResp text2Voice(BackendText2VoiceReq request) {
        TtsRequest text2VoiceReq = BaseBeanUtil.copyProperties(request, TtsRequest.class);
        byte[] bytes = esbComponentVoiceDubboService.tts(text2VoiceReq);
        ElectUploadFileRequest electUploadFileRequest = new ElectUploadFileRequest();
        electUploadFileRequest.setFile(bytes);
        electUploadFileRequest.setFile_name(UUID.randomUUID()+".mp3");
        ElectUploadFileResponse electUploadFileResponse = fileManageService.doElectUploadFile(electUploadFileRequest);
        return BaseBeanUtil.copyProperties(electUploadFileResponse, BackendText2VoiceResp.class);
    }

    @Override
    public BackenfVoice2TextResp voice2Text(BackendVoice2TextReq request) {
        AsrRequest voice2TextReq = BaseBeanUtil.copyProperties(request, AsrRequest.class);
        String text = esbComponentVoiceDubboService.asr(voice2TextReq);
        BackenfVoice2TextResp backenfVoice2TextResp = new BackenfVoice2TextResp();
        backenfVoice2TextResp.setVoiceText(text);
        return backenfVoice2TextResp;
    }
}