package com.cairh.cpe.esb.comp.backend.auto.assiger;

import com.cairh.cpe.component.common.constant.Constant;
import com.cairh.cpe.esb.base.rpc.IVBaseOnlineUserDubboService;
import com.cairh.cpe.esb.base.rpc.dto.resp.OnlineUserResponse;
import com.cairh.cpe.esb.comp.backend.auto.constant.AutoAssignConstant;
import com.cairh.cpe.esb.comp.backend.auto.constant.VideoConstant;
import com.cairh.cpe.esb.comp.backend.auto.queue.impl.VideoOperatorGroup;
import com.cairh.cpe.esb.comp.backend.auto.queue.impl.VideoUserGroup;
import com.cairh.cpe.esb.comp.backend.auto.service.IAutoAssignResponseService.ResponseType;
import com.cairh.cpe.component.common.service.IRedisService;
import com.cairh.cpe.esb.comp.backend.auto.service.IAutoAssignService;
import com.cairh.cpe.esb.comp.backend.auto.service.IVideoOperatorService;
import com.cairh.cpe.component.common.support.ConfigurationSupport;
import com.cairh.cpe.component.common.model.VideoOperator;
import com.cairh.cpe.component.common.model.VideoOperator.StatusDic;
import com.cairh.cpe.component.common.model.VideoUser;
import com.cairh.cpe.component.common.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 视频操作员maintainer
 *
 * <AUTHOR>
 * @since XPE-SP1-PACK6-PATCH3
 */
@Component
public class VideoOperatorMaintainer {

    private static final Logger logger = LoggerFactory.getLogger(VideoOperatorMaintainer.class);

    @Autowired
    private IVideoOperatorService videoOperatorService;
    @Autowired
    private IRedisService redisService;
    @Autowired
    private ConfigurationSupport configurationSupport;
    @Resource
    private VideoOperatorGroup videoOperatorVideoGroup;
    @Resource
    private VideoUserGroup videoUserVideoGroup;
    @Resource
    private IAutoAssignService autoAssignService;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @DubboReference(check = false, lazy = true)
    private IVBaseOnlineUserDubboService baseOnlineUserDubboService;

    /**
     * 视频坐席状态实时维护
     */
    public void statusMaintenance() {
//        if (!configurationSupport.ifAutoAllocation()) {
//            // 未开启自动分配
//            return;
//        }

        // 查询视频中的用户
        List<VideoUser> queueUsers = Optional.ofNullable(videoUserVideoGroup.queryAll()).orElse(Collections.emptyList());
        if (CollectionUtils.isEmpty(queueUsers)) {
            //更新 坐席状态
            updateVideoOperator(new HashSet<>());
            return;
        }

        Set<String> videoingOperators = new HashSet<>(); // 保存视频中的坐席
        for (VideoUser queueUser : queueUsers) {
            if (Objects.isNull(queueUser)) {
                continue;
            }
            if (StringUtils.equals(VideoConstant.STATUS_2_VIDEOING, queueUser.getStatus()) && StringUtils.isNotBlank(queueUser.getOperator_no())) {
                // 视频中
                videoingOperators.add(queueUser.getOperator_no());
            }
        }
        //更新坐席状态
        updateVideoOperator(videoingOperators);
    }

    private void updateVideoOperator(Set<String> videoingOperators) {
        // 获取redis中所有的视频坐席
        List<VideoOperator> videoOperators = Optional.ofNullable(videoOperatorVideoGroup.queryAll()).orElse(Collections.emptyList());
        Set<String> allVideoOperators = videoOperators.stream().map(VideoOperator::getStaff_no).collect(Collectors.toSet());
        //修改获取 操作员在线人数

//        List<OnlineUserResponse> onlineUserResponses = Optional.ofNullable(baseOnlineUserDubboService.onlineUserInfo()).orElse(Collections.emptyList());
        Set<String> result =Optional.ofNullable(stringRedisTemplate.opsForHash().keys(Constant.ONLINE_USER_IDENTIFIER)).orElse(Collections.EMPTY_SET);
        allVideoOperators.addAll(result);
        // 状态维护
        for (String operator_no : allVideoOperators) {
            try {
                statusMaintenance(operator_no, videoingOperators);
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
            }
        }
    }

    /**
     * 单个的视频坐席状态维护
     */
    private void statusMaintenance(String operator_no, Set<String> videoingOperators) {
        //队列状态清理
        videoOperatorService.updateWaitQueueIndex(operator_no,"");
        //判断坐席权限
        if(!configurationSupport.ifVideoOperationAuth(operator_no)) {
            //移除该坐席 加入的所有队列
            autoAssignService.operatorLeave(operator_no);
            //移除缓存中该坐席
            videoOperatorVideoGroup.remove(operator_no);
            return;
        }
        // 判断是否为离线状态
        VideoOperator videoOperator = videoOperatorService.get(operator_no);
        if (Objects.isNull(videoOperator)) {
            logger.info("视频坐席状态维护 检测无权限 operator_no={}", operator_no);
            return;
        }

//        List<OnlineUserResponse> onlineUserResponses = Optional.ofNullable(baseOnlineUserDubboService.onlineUserInfo()).orElse(Collections.emptyList());
//        Set<String> onlineEmp = onlineUserResponses.stream().map(OnlineUserResponse::getStaff_no).collect(Collectors.toSet());
        Set<String> onlineUserResponses = Optional.ofNullable(stringRedisTemplate.opsForHash().keys(Constant.ONLINE_USER_IDENTIFIER)).orElse(Collections.EMPTY_SET);
        if (!onlineUserResponses.contains(operator_no) &&
                !StatusDic.OFFLINE.status.equals(videoOperator.getStatus())) {
            logger.debug("视频坐席不在线，更新离线状态 operator_no={}", operator_no);
            videoOperatorService.updateStatus(operator_no, StatusDic.OFFLINE);
            clearAutoAnswer(operator_no);
            return;
        } else if (!onlineUserResponses.contains(operator_no)) {
            clearAutoAnswer(operator_no);
            // 已经是离线状态
            return;
        }

        Long rank = redisTemplate.opsForZSet().rank(AutoAssignConstant.QUEUE_PREFIX_WAIT, operator_no);
        if (Objects.nonNull(rank)) {
            //在等待队列中
            if(!VideoOperator.StatusDic.JOINED.status.equals(videoOperator.getStatus())) {
                logger.debug("更新视频坐席的状态为队列中 operator_no={}", operator_no);
                videoOperatorService.updateStatus(operator_no, StatusDic.JOINED);
            }
            videoOperatorService.updateWaitQueueIndex(operator_no, String.valueOf(rank));
            return;
        }

        rank = redisTemplate.opsForZSet().rank(AutoAssignConstant.QUEUE_PREFIX_CONFIRM, operator_no);
        if (Objects.nonNull(rank)) {
            logger.debug("视频坐席的状态为已分配 operator_no={}", operator_no);
            if (!StatusDic.ASSIGNED.status.equals(videoOperator.getStatus())) {
                logger.debug("更新视频坐席的状态为已分配 operator_no={}", operator_no);
                videoOperatorService.updateStatus(operator_no, StatusDic.ASSIGNED);
            }
            return;
        }
        // 如果正常视频则跳出，不是则更新坐席状态
        if (CollectionUtils.isNotEmpty(videoingOperators)){
            // 判断是否为视频中状态
            if (judegeIfVideoing(videoOperator, operator_no, videoingOperators)) {
                return;
            }
        }

        // 判断是否为已接受状态
        if (StringUtils.equals(ResponseType.ACCEPT.value, videoOperator.getResponseType())) {
            // 响应类型是接受
            String assigned_user_id = videoOperator.getAssigned_user_id();
            VideoUser queueUser = videoUserVideoGroup.query(assigned_user_id);
            if (queueUser != null && queueUser.getAssignedEmp().equals(operator_no) &&
                    (VideoConstant.STATUS_0_WAITING.equals(queueUser.getStatus())
                            || VideoConstant.STATUS_1_MATCHED.equals(queueUser.getStatus()))
                    && !StatusDic.ACCEPTED.status.equals(videoOperator.getStatus())) {
                // 当前分配的用户存在，且当前分配的用户锁分配的坐席是当前坐席，且当前分配的用户的状态为0等待中或1已分配
                // 那么更新当前视频坐席状态为已接受
                logger.debug("更新视频坐席的状态为已接受 operator_no={}", operator_no);
                videoOperatorService.updateStatus(operator_no, StatusDic.ACCEPTED);
                return;
            } else if (queueUser != null && queueUser.getAssignedEmp().equals(operator_no) &&
                    (VideoConstant.STATUS_0_WAITING.equals(queueUser.getStatus())
                            || VideoConstant.STATUS_1_MATCHED.equals(queueUser.getStatus()))) {
                // 已经是已接受的状态
                return;
            }
        }

        // 以上状态都不是，那么当前坐席为空闲状态
        if(!StatusDic.FREE.status.equals(videoOperator.getStatus())) {
            videoOperatorService.updateStatus(operator_no, StatusDic.FREE);
        }
    }

    /**
     * 清理自动接听标识
     * @param operator_no
     */
    private void clearAutoAnswer(String operator_no) {

        if (redisTemplate.hasKey(AutoAssignConstant.QUEUE_AUTO_ANSWER+ operator_no)) {
            redisTemplate.delete(AutoAssignConstant.QUEUE_AUTO_ANSWER+ operator_no);
        }
    }

    /**
     * 判断是否为视频中状态
     */
    private boolean judegeIfVideoing(VideoOperator videoOperator, String operator_no,
                                     Set<String> videoingOperators) {
        // 是否需要更新为视频中标识
        boolean needUpdateVideoing = false;
        // 是否在视频中标识
        boolean ifVideoing = false;

        if (StringUtils.equals(videoOperator.getResponseType(), ResponseType.ACCEPT.value)) {
            // 响应类型是接受
            String assigned_user_id = videoOperator.getAssigned_user_id();
            VideoUser queueUser = videoUserVideoGroup.query(assigned_user_id);
            if (queueUser != null && operator_no.equals(queueUser.getOperator_no())
                    && VideoConstant.STATUS_2_VIDEOING.equals(queueUser.getStatus())
                    && !StatusDic.VIDEOING.status.equals(videoOperator.getStatus())) {
                // 当前分配的用户存在，且当前分配的用户所分配的坐席是当前坐席，且当前分配的用户的状态为2视频中
                // 那么更新当前视频坐席状态为视频中
                needUpdateVideoing = true;
                ifVideoing = true;
            } else if (queueUser != null && queueUser.getOperator_no().equals(operator_no)
                    && VideoConstant.STATUS_2_VIDEOING.equals(queueUser.getStatus())) {
                // 已经是视频中的状态
                ifVideoing = true;
            }
        } else {
            // 自动分配下，也存在手动点击接通的场景（优先接通功能）
            // 作为备选方案，使用从视频中用户推算出的视频中坐席来判断
            if (videoingOperators.contains(operator_no)
                    && !StatusDic.VIDEOING.status.equals(videoOperator.getStatus())) {
                needUpdateVideoing = true;
                ifVideoing = true;
            } else if (videoingOperators.contains(operator_no)) {
                // 已经是视频中的状态
                ifVideoing = true;
            }
        }

        // 是否需要更新中状态
        if (needUpdateVideoing) {
            logger.debug("更新视频坐席的状态为视频中 operator_no={}", operator_no);
            videoOperatorService.updateStatus(operator_no, StatusDic.VIDEOING);
        }
        // 返回是否在视频中
        return ifVideoing;
    }
}
