package com.cairh.cpe.esb.comp.backend.controller;

import com.cairh.cpe.context.Result;
import com.cairh.cpe.component.common.form.request.BackendText2VoiceReq;
import com.cairh.cpe.component.common.form.request.BackendVoice2TextReq;
import com.cairh.cpe.component.common.form.response.BackendText2VoiceResp;
import com.cairh.cpe.component.common.form.response.BackenfVoice2TextResp;
import com.cairh.cpe.esb.comp.backend.service.IVoiceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 语音处理
 * <AUTHOR>
 * @since 2022-07-13
 */
@RequestMapping("/voice")
@RestController
public class VoiceController {

    @Autowired
    private IVoiceService voiceService;

    /**
     * 语音转文字
     *
     * @return
     */
    @RequestMapping(value = "/voice2Text", method = RequestMethod.POST)
    public Result<BackenfVoice2TextResp> voice2Text(@RequestBody BackendVoice2TextReq req) {
        return Result.success(voiceService.voice2Text(req));

    }

    /**
     * 文字转语音
     *
     * @return
     */
    @RequestMapping(value = "/text2Voice", method = RequestMethod.POST)
    public Result<BackendText2VoiceResp> text2Voice(@RequestBody BackendText2VoiceReq req) {
        return Result.success(voiceService.text2Voice(req));

    }
}