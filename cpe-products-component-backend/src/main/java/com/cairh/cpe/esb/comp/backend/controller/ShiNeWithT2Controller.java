package com.cairh.cpe.esb.comp.backend.controller;

import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.context.Result;
import com.cairh.cpe.esb.component.business.IEsbComponentBusinessDubboService;
import com.cairh.cpe.esb.component.business.dto.req.*;
import com.cairh.cpe.esb.component.business.dto.resp.*;
import com.cairh.cpe.esb.component.elect.IEsbComponentElectThirdDubboService;
import com.cairh.cpe.esb.component.elect.dto.req.ElectArchiveThirdFileRequest;
import com.cairh.cpe.esb.component.elect.dto.req.ElectDownloadThirdFileRequest;
import com.cairh.cpe.esb.component.elect.dto.resp.ElecQueryClientToThirdResp;
import com.cairh.cpe.esb.component.elect.dto.resp.ElectDownloadThirdFileResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 新意接口以及新T2接口测试
 *
 * <AUTHOR>
 * @since 2025/7/11 9:39
 */
@Slf4j
@RestController
@RequestMapping("shiNeWithT2")
public class ShiNeWithT2Controller {

    @DubboReference(check = false, lazy = true)
    private IEsbComponentElectThirdDubboService esbComponentElectThirdDubboService;
    @DubboReference(check = false, lazy = true)
    private IEsbComponentBusinessDubboService esbComponentBusinessDubboService;

    /**
     * 查询客户基本档案、业务档案
     */
    @RequestMapping(value = "queryClientArchiveThirdFile", method = RequestMethod.POST)
    public Result<ElecQueryClientToThirdResp> queryClientArchiveThirdFile(@AuthenticationPrincipal BaseUser baseUser, @RequestBody ElectArchiveThirdFileRequest request) {
        ElecQueryClientToThirdResp elecQueryClientToThirdResp = esbComponentElectThirdDubboService.queryClientArchiveThirdFile(request);
        return Result.success(elecQueryClientToThirdResp);
    }

    /**
     * 新意文件下载
     */
    @RequestMapping(value = "electDownloadThirdFile", method = RequestMethod.POST)
    public Result<ElectDownloadThirdFileResponse> electDownloadThirdFile(@AuthenticationPrincipal BaseUser baseUser, @RequestBody ElectDownloadThirdFileRequest request) {
        ElectDownloadThirdFileResponse electDownloadThirdFileResponse = esbComponentElectThirdDubboService.electDownloadThirdFile(request);
        return Result.success(electDownloadThirdFileResponse);
    }

    /**
     * 查询客户资金账户信息
     */
    @RequestMapping(value = "businessQueryClientAccount", method = RequestMethod.POST)
    public Result<BusinessQueryClientAccountResp> businessQueryClientAccount(@AuthenticationPrincipal BaseUser baseUser, @RequestBody BusinessQueryClientAccountReq request) {
        BusinessQueryClientAccountResp businessQueryClientAccountResp = esbComponentBusinessDubboService.businessQueryClientAccount(request);
        return Result.success(businessQueryClientAccountResp);
    }

    /**
     * 查询账户标签信息是否存在
     */
    @RequestMapping(value = "businessExistsClientAccountTag", method = RequestMethod.POST)
    public Result<BusinessExistsClientAccountTagResp> businessExistsClientAccountTag(@AuthenticationPrincipal BaseUser baseUser, @RequestBody BusinessExistsClientAccountTagReq request) {
        BusinessExistsClientAccountTagResp businessExistsClientAccountTagResp = esbComponentBusinessDubboService.businessExistsClientAccountTag(request);
        return Result.success(businessExistsClientAccountTagResp);
    }

    /**
     * 账户标签信息查询
     */
    @RequestMapping(value = "businessQueryAccountLabelInfo", method = RequestMethod.POST)
    public Result<BusinessQueryAccountLabelInfoResp> businessQueryAccountLabelInfo(@AuthenticationPrincipal BaseUser baseUser, @RequestBody BusinessQueryAccountLabelInfoReq request) {
        BusinessQueryAccountLabelInfoResp businessQueryAccountLabelInfoResp = esbComponentBusinessDubboService.businessQueryAccountLabelInfo(request);
        return Result.success(businessQueryAccountLabelInfoResp);
    }

    /**
     * 客户一户通号的适当性信息查询
     */
    @RequestMapping(value = "businessQueryAccountAppropriateInfo", method = RequestMethod.POST)
    public Result<BusinessQueryAccountAppropriateInfoResp> businessQueryAccountAppropriateInfo(@AuthenticationPrincipal BaseUser baseUser, @RequestBody BusinessQueryAccountAppropriateInfoReq request) {
        BusinessQueryAccountAppropriateInfoResp businessQueryAccountAppropriateInfoResp = esbComponentBusinessDubboService.businessQueryAccountAppropriateInfo(request);
        return Result.success(businessQueryAccountAppropriateInfoResp);
    }

    /**
     * 账户标签字典信息查询
     */
    @RequestMapping(value = "businessQueryAccountDictInfo", method = RequestMethod.POST)
    public Result<BusinessQueryAccountDictInfoResp> businessQueryAccountDictInfo(@AuthenticationPrincipal BaseUser baseUser, @RequestBody BusinessQueryAccountDictInfoReq request) {
        BusinessQueryAccountDictInfoResp businessQueryAccountDictInfoResp = esbComponentBusinessDubboService.businessQueryAccountDictInfo(request);
        return Result.success(businessQueryAccountDictInfoResp);
    }


}
