plugins {
    id 'war'
    id 'org.springframework.boot'
}

version ''

archivesBaseName = 'cpe-products-component-front'

dependencies {
    api(project(':cpe-esb-component-server-api'))
    api(project(':cpe-component-common'))

    api("com.cairh:cpe-auth")
    api("com.cairh:cpe-db")
    api("com.cairh:cpe-rpc")
    api("com.cairh:cpe-trace")
    api('com.cairh:cpe-esb-basedata-server-api')

    api('com.baomidou:mybatis-plus-boot-starter')
    api('org.springframework.boot:spring-boot-starter-web')
    api('org.springframework.boot:spring-boot-starter-actuator')
    api('org.springframework.boot:spring-boot-starter-data-redis')
    api('com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-discovery')
    api('com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-config')
    api('org.springframework.cloud:spring-cloud-starter-consul-discovery')
    api('org.springframework.cloud:spring-cloud-starter-consul-config')
    api('com.ctrip.framework.apollo:apollo-client')
    api('com.alibaba.spring:spring-context-support')
    api('com.cairh:cpe-job-core')
    api('com.cairh:cpe-counter-data-http')
    api("com.cairh:cpe-counter-data-kcbp")
    api('com.ttd.openplatform.fund.sdk:fund-openplatform-sdk:0.0.2-RELEASE')
    //api('com.tencentcloud.tdsql:tdsql-pg-connector-java8')
    api ('org.postgresql:postgresql')
    implementation('commons-lang:commons-lang:2.6')
}
