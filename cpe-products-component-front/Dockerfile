FROM openjdk:8
MAINTAINER sunyy <<EMAIL>>
RUN mkdir -p /data/cpe/cpe-esb
WORKDIR /data/cpe/cpe-esb

COPY build/libs/cpe-products-component-front.jar /data/cpe/cpe-esb/
COPY conf/version.txt /data/cpe/cpe-esb/conf/

ENV CPE_XMS=512m
ENV CPE_MAXRAMPERCENTAGE=70.0
RUN echo "开始制作cpe-products-component-front镜像：CPE_XMS：$CPE_XMS;CPE_MAXRAMPERCENTAGE：$CPE_MAXRAMPERCENTAGE"
ENTRYPOINT ["sh", "-c", "java -jar -Xms$CPE_XMS -XX:MaxRAMPercentage=$CPE_MAXRAMPERCENTAGE -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8 -Duser.language=zh -Duser.country=CN /data/cpe/cpe-esb/cpe-products-component-front.jar"]

