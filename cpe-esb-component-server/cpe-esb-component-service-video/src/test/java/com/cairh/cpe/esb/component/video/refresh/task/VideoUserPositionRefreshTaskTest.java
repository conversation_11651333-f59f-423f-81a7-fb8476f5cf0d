package com.cairh.cpe.esb.component.video.refresh.task;

import static org.mockito.Mockito.doReturn;

import com.alibaba.fastjson.JSON;
import com.cairh.cpe.component.common.model.VideoUser;
import com.cairh.cpe.esb.component.video.dto.req.VideoQryQueueInfoRequest;
import com.cairh.cpe.esb.component.video.dto.resp.VideoQryQueueInfoResponse;
import com.cairh.cpe.esb.component.video.queue.IQueue;
import com.cairh.cpe.esb.component.video.queue.QueueGroup;
import com.cairh.cpe.esb.component.video.queue.VideoQueue;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import com.cairh.cpe.esb.component.video.service.dubbo.EsbComponentVideoDubboServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * <AUTHOR> href="https://github.com/ooooo-youwillsee">ooooo</a>
 * @since 1.0.0
 */
@SpringBootTest(classes = ApplicationTest.class)
class VideoUserPositionRefreshTaskTest {

  @Autowired
  private VideoUserPositionRefreshTask videoUserPositionRefreshTask;

  @SpyBean
  public QueueGroup queueGroup;

  @Autowired
  private RedisTemplate<Object, Object> redisTemplate;

  @Autowired
  private EsbComponentVideoDubboServiceImpl componentVideoDubboService;
  @Autowired
  private EsbComponentVideoDubboServiceImpl esbComponentVideoDubboService;

  @BeforeEach
  void setUp() {
    VideoQueue videoQueue1 = Mockito.mock(VideoQueue.class);
    doReturn(1).when(videoQueue1).getLevel();
    doReturn(1L).when(videoQueue1).size();
    doReturn(Collections.singleton("1")).when(videoQueue1).getAllUserId();

    VideoQueue videoQueue2 = Mockito.mock(VideoQueue.class);
    doReturn(2).when(videoQueue2).getLevel();
    doReturn(1L).when(videoQueue2).size();
    doReturn(Collections.singleton("2")).when(videoQueue2).getAllUserId();

    List<IQueue> queues = new ArrayList<>();
    queues.add(videoQueue1);
    queues.add(videoQueue2);

    doReturn(queues).when(queueGroup).getAllQueue();
  }

  @Test
  void refresh() {

    videoUserPositionRefreshTask.refresh();
    //te.execute((RedisCallback) connection -> {
    //     //   connection.multi();
    //     //   connection.hSet("1".getBytes(StandardCharsets.UTF_8), "2".getBytes(StandardCharsets.UTF_8), "1".getBytes(StandardCharsets.UTF_8));
    //     //   connection.exec();
    //     //   return null;
    //     // });
    // redisTempla

  }

  @Test
  public void testQueryQueueInfo(){
    String s = "{\n" +
            "    \"unique_id\": \"20230222351520000047\",\n" +
            "    \"request_id\": \"202404031051120070000\",\n" +
            "    \"subsys_no\": \"16\",\n" +
            "    \"busin_type\": \"100058\",\n" +
            "    \"video_vender\": \"zt_zego\",\n" +
            "    \"task_id\": null,\n" +
            "    \"user_name\": \"xx即构1\",\n" +
            "    \"v\": 0.35717872623802394\n" +
            "}";
    VideoQryQueueInfoRequest videoQryQueueInfoRequest = JSON.parseObject(s,VideoQryQueueInfoRequest.class);
    VideoQryQueueInfoResponse videoQryQueueInfoResponse = componentVideoDubboService.qryQueueInfo(videoQryQueueInfoRequest);
    System.out.println(videoQryQueueInfoResponse);

  }

  @Test
  public void testQueryQueueInfo2(){
    VideoUser user = new VideoUser();
    user.setUnique_id("1620230222351520000047");
    esbComponentVideoDubboService.determinePosition(user);
  }
}