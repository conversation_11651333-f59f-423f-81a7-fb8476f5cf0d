package com.cairh.cpe.esb.component.video.core.impl;

import com.cairh.cpe.component.common.model.VideoOperator;
import com.cairh.cpe.esb.component.video.core.AbstractGenericVideoGroup;
import org.springframework.stereotype.Service;

/**
 * 视频坐席集, 基于视频集, 对视频坐席信息作出维护
 *
 * <AUTHOR>
 * @since 2023/7/17 19:45
 */
@Service
public class VideoOperatorGroup extends AbstractGenericVideoGroup<VideoOperator> {

    public VideoOperatorGroup() {
        super("video_operator");
    }
}
