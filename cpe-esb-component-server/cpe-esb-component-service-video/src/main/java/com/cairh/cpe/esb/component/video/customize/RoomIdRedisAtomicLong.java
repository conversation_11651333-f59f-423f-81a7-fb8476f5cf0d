package com.cairh.cpe.esb.component.video.customize;

import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.support.atomic.RedisAtomicLong;

/**
 * {@link RedisAtomicLong} for room_id
 *
 * <AUTHOR>
 */
public class RoomIdRedisAtomicLong extends RedisAtomicLong {

    // initial of room_id
    private static final Long ROOM_ID_INITIAL_VALUE = 1L;

    // max of room_id
    private static final Long ROOM_ID_MAX_VALUE = 500000L;


    public RoomIdRedisAtomicLong(String redisCounter, RedisConnectionFactory factory) {
        super(redisCounter, factory, ROOM_ID_INITIAL_VALUE);
    }

    @Override
    public long getAndIncrement() {
        if (super.get() >= ROOM_ID_MAX_VALUE) {
            synchronized (getClass()) {
                if (super.get() >= ROOM_ID_MAX_VALUE) {
                    super.set(ROOM_ID_INITIAL_VALUE);
                }
            }
        }

        return super.getAndIncrement();
    }
}
