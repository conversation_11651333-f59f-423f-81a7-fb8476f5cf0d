package com.cairh.cpe.esb.component.video.business;

import com.cairh.cpe.component.common.model.VideoUser;
import com.cairh.cpe.esb.component.video.constant.VideoVender;

/**
 * 视频厂商
 *
 * <AUTHOR>
 */
public interface IVideoVenderService {

    /**
     * 获取视频用户对应的厂商
     *
     * @param user 视频用户
     * @return 对应视频厂商
     */
    VideoVender getVender(VideoUser user);

    /**
     * 获取默认的厂商
     *
     * @return 默认视频厂商
     */
    VideoVender getDefaultVender();
}
