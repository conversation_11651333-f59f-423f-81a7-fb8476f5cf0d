package com.cairh.cpe.esb.component.video.customize;

import com.cairh.cpe.esb.component.video.constant.RedisConstant;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.InstantiationAwareBeanPostProcessorAdapter;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.SerializationException;
import org.springframework.stereotype.Component;

import java.beans.Introspector;
import java.util.Objects;

/**
 * redis统一前缀
 *
 * <AUTHOR>
 */
@Component
public class RedisUnifiedPrefixBeanPostProcessor extends InstantiationAwareBeanPostProcessorAdapter {

    public Object postProcessAfterInitialization(Object bean, String name) throws BeansException {
        if (bean instanceof RedisTemplate && StringUtils.equals(name, Introspector.decapitalize(RedisTemplate.class.getSimpleName()))) {
            RedisTemplate redisTemplate = (RedisTemplate) bean;

            if (Objects.isNull(redisTemplate.getKeySerializer()) || !(redisTemplate.getKeySerializer() instanceof PrefixRedisSerializer)) {
                redisTemplate.setKeySerializer(new PrefixRedisSerializer(RedisConstant.UNIFIED_PREFIX));
            }

            redisTemplate.setEnableTransactionSupport(true);
            return redisTemplate;
        }

        return super.postProcessAfterInitialization(bean, name);
    }

    private static class PrefixRedisSerializer implements RedisSerializer<String> {

        private final RedisSerializer<String> delegate = RedisSerializer.string();

        private final String prefix;

        public PrefixRedisSerializer(String prefix) {
            this.prefix = prefix;
        }

        public byte[] serialize(String key) throws SerializationException {
            return delegate.serialize(prefix + key);
        }

        public String deserialize(byte[] bytes) throws SerializationException {
            return delegate.deserialize(bytes);
        }
    }
}
