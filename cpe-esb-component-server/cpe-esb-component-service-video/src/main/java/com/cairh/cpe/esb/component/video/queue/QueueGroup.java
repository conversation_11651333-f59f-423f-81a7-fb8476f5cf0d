package com.cairh.cpe.esb.component.video.queue;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cairh.cpe.component.common.data.entity.VideoPriorityConfig;
import com.cairh.cpe.component.common.data.service.IVideoPriorityConfigService;
import com.cairh.cpe.esb.component.video.constant.VideoConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 队列集, 维护了视频队列信息
 *
 * <AUTHOR>
 */
@Service
public class QueueGroup {

    public static final String QUEUE_GROUP_IDENTITY = "queue_group";

    @Autowired
    private RedisTemplate<Object, Object> redisTemplate;


    /**
     * 获取视频队列
     *
     * @param video_level 视频优先级
     * @return 对应的视频队列
     */
    @Transactional
    public IQueue getQueue(Integer video_level) {
        if (Objects.isNull(video_level) ) {
            video_level = VideoConstant.DEFAULT_VIDEO_LEVEL;
        }


        String video_level_key = String.valueOf(video_level);
        if (BooleanUtil.isFalse(redisTemplate.boundHashOps(QUEUE_GROUP_IDENTITY).hasKey(video_level_key))) {
            do {
                //redisTemplate.watch(QUEUE_GROUP_IDENTITY);
                if (BooleanUtil.isFalse(redisTemplate.boundHashOps(QUEUE_GROUP_IDENTITY).hasKey(video_level_key))) {
                    redisTemplate.opsForHash().put(QUEUE_GROUP_IDENTITY, video_level_key, new VideoQueue(video_level));
                }
            } while (CollectionUtil.isEmpty(redisTemplate.exec()));
            //redisTemplate.unwatch();
        }

        return (IQueue) redisTemplate.opsForHash().get(QUEUE_GROUP_IDENTITY, video_level_key);
    }

    /**
     * 获取所有队列
     *
     * @return 所有队列, 无则返回{@code null}
     */
    public List<IQueue> getAllQueue() {
        List<Object> queues = redisTemplate.opsForHash().values(QUEUE_GROUP_IDENTITY);

        if (CollectionUtil.isNotEmpty(queues)) {
            return queues.stream().map(IQueue.class::cast).collect(Collectors.toList());
        }

        return null;
    }

    /**
     * 对于user_id是否存在于队列
     *
     * @param user_id 用户ID
     * @return 如果存在返回对应队列, 否则返回{@code null}
     */
    public IQueue exist(String user_id) {
        List<IQueue> queues = getAllQueue();

        if (CollectionUtil.isEmpty(queues)) {
            return null;
        }

        return queues.stream().filter(queue -> CollectionUtil.contains(queue.getAllUserId(), user_id)).findFirst().orElse(null);
    }


}
