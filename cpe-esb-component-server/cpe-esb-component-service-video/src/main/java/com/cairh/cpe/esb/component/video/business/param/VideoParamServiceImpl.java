package com.cairh.cpe.esb.component.video.business.param;

import com.cairh.cpe.component.common.model.VideoUser;
import com.cairh.cpe.component.common.utils.StringUtils;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.esb.component.core.util.StringUtil;
import com.cairh.cpe.esb.component.video.business.IVenderVideoParamService;
import com.cairh.cpe.esb.component.video.business.IVideoParamService;
import com.cairh.cpe.esb.component.video.business.IVideoVenderService;
import com.cairh.cpe.esb.component.video.business.factory.impl.VideoParamServiceFactory;
import com.cairh.cpe.esb.component.video.constant.VideoConstant;
import com.cairh.cpe.esb.component.video.constant.VideoParamType;
import com.cairh.cpe.esb.component.video.constant.VideoVender;
import com.cairh.cpe.esb.component.video.core.IVideoGroup;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 视频联通参数
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class VideoParamServiceImpl implements IVideoParamService {

    @Autowired
    private IVideoVenderService videoVenderService;

    @Autowired
    private VideoParamServiceFactory videoParamServiceFactory;

    @Autowired
    private IVideoGroup<VideoUser> videoGroup;

    @Override
    public Map<String, Object> getParam(VideoUser user, VideoParamType type) {
        VideoVender vender = videoVenderService.getVender(user);

        IVenderVideoParamService venderVideoParamService = videoParamServiceFactory.getService(vender);

        Map<String, Object> video_param;
        log.debug("获取视频接听参数：{}",vender.name);
        try {
            if (type.equals(VideoParamType.COMMON)) {
                video_param = venderVideoParamService.getCommonVideoParam(user, StringUtils.equals(vender.name,VideoVender.ZHENGTONG.name)?true:false);
            } else {
                video_param = venderVideoParamService.getSpecialVideoParam(user, StringUtils.equals(vender.name,VideoVender.ZHENGTONG.name)?true:false);
            }

            // 补充跳转地址
            video_param.put(VideoConstant.PAGE_ADDR, user.getPage_addr());
            // 补充接听操作员编号
            video_param.put(VideoConstant.OPERATOR_NO, user.getOperator_no());
            // 补充接听操作员姓名
            video_param.put(VideoConstant.OPERATOR_NAME, user.getOperator_name());
        } catch (Exception e) {
            throw new BizException("get video param error, user_id: " + user.getUnique_id(), e);
        }

        return video_param;
    }
}
