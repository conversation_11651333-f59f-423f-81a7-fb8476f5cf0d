package com.cairh.cpe.esb.component.video.anychatsdk.core;

import com.cairh.cpe.esb.component.video.anychatsdk.AnyChatConstant;
import com.cairh.cpe.esb.component.video.anychatsdk.AnyChatVerifyUserOutParam;
import com.cairh.cpe.esb.component.video.anychatsdk.v6_3.AnyChatServerEvent;
import com.cairh.cpe.esb.component.video.util.DateUtil;
import com.cairh.cpe.esb.component.video.util.StringUtils;
import javassist.ClassClassPath;
import javassist.ClassPool;
import javassist.CtClass;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

import java.io.File;
import java.io.IOException;
import java.net.URL;

public abstract class AnyChatServerEventHandlerAdapter implements AnyChatServerEvent {

    public static final String PREFIX_EMP = "emp";

    public static final String PREFIX_USER = "user";

    /** anychatserversdk全限定名 */
    public static final String SDK_QUALIFIED_NAME = "com.bairuitech.anychat.AnyChatServerSDK";

    public static Boolean init_flag = false;

    protected AbstractAnyChatServerSDK anychat;

    private Logger logger = LoggerFactory.getLogger(getClass());

    @Override
    public int OnAnyChatObjectEventCallBack(
            int dwObjectType,
            int dwObjectId,
            int dwEventType,
            int dwParam1,
            int dwParam2,
            int dwParam3,
            int dwParam4,
            String lpStrParam) {
        throw new UnsupportedOperationException();
    }

    // 用户准备进入房间验证，如果允许用户进入房间，则必须返回0，则否返回出错代码
    @Override
    public int OnAnyChatPrepareEnterRoomCallBack(
            int dwUserId, int dwRoomId, String szRoomName, String szPassword) {
        logger.info(
                "[dwUserId={}][dwRoomId={}][szRoomName={}][szPassword={}]",
                dwUserId,
                dwRoomId,
                szRoomName,
                szPassword);
        return 0;
    }

    @Override
    public void OnAnyChatRecvUserTextMsgCallBack(
            int dwRoomId,
            int dwSrcUserId,
            int dwTarUserId,
            int bSecret,
            String szTextMessage,
            int dwLen) {
        logger.info(
                "[dwRoomId={}][dwSrcUserId={}][dwTarUserId={}][bSecret={}][szTextMessage={}][dwLen={}]",
                dwRoomId,
                dwSrcUserId,
                dwTarUserId,
                bSecret,
                szTextMessage,
                dwLen);
    }

    @Override
    public void OnAnyChatSDKFilterData(int dwUserId, byte[] lpBuf, int dwLen) {
        logger.info("[dwUserId={}][lpBuf={}][dwLen={}]", dwUserId, new String(lpBuf), dwLen);
    }

    @Override
    public void OnAnyChatServerAppMessageExCallBack(int dwNotifyMessage, int wParam, int lParam) {
        logger.info("[dwNotifyMessage={}][wParam={}][lParam={}]", dwNotifyMessage, wParam, lParam);
    }

    /**
     * 服务器录像（扩展）回调函数，由中心录像服务器触发
     * 参考：http://bbs.anychat.cn/forum.php?mod=viewthread&tid=20&extra=page%3D1
     */
    @Override
    public void OnAnyChatServerRecordExCallBack(
            int dwUserId,
            String szRecordFileName,
            int dwElapse,
            int dwFlags,
            int dwParam,
            String lpUserStr,
            int dwRecordServerId) {
        logger.info(
                "[dwUserId={}][szRecordFileName={}][dwElapse={}][dwFlags={}][dwParam={}][lpUserStr={}][dwRecordServerId={}]",
                dwUserId,
                szRecordFileName,
                dwElapse,
                dwFlags,
                dwParam,
                lpUserStr,
                dwRecordServerId);
    }

    @Override
    public void OnAnyChatTimerEventCallBack() {
        logger.info("OnTimerEventCallBack");
    }

    @Override
    public void OnAnyChatTransBuffer(int dwUserId, byte[] lpBuf, int dwLen) {
        logger.info("[dwUserId={}][lpBuf={}][dwLen={}]", dwUserId, new String(lpBuf), dwLen);
    }

    @Override
    public void OnAnyChatTransBufferEx(
            int dwUserId, byte[] lpBuf, int dwLen, int wParam, int lParam, int dwTaskId) {
        logger.info(
                "[dwUserId={}][lpBuf={}][dwLen={}][wParam={}][lParam={}][dwTaskId={}]",
                dwUserId,
                new String(lpBuf),
                dwLen,
                wParam,
                lParam,
                dwTaskId);
    }

    @Override
    public void OnAnyChatTransFile(
            int dwUserId,
            String szFileName,
            String szTempFilePath,
            int dwFileLength,
            int wParam,
            int lParam,
            int dwTaskId) {
        logger.info(
                "[dwUserId={}][szFileName={}][szTempFilePath={}][dwFileLength={}][wParam={}][lParam={}][dwTaskId={}]",
                dwUserId,
                szFileName,
                szTempFilePath,
                dwFileLength,
                wParam,
                lParam,
                dwTaskId);
    }

    @Override
    public void OnAnyChatUserEnterRoomActionCallBack(int dwUserId, int dwRoomId) {
        logger.info("[dwUserId={}][dwRoomId={}]", dwUserId, dwRoomId);
    }

    /** 用户信息控制回调，客户端调用API：BRAC_UserInfoControl会触发该回调 */
    @Override
    public int OnAnyChatUserInfoCtrlCallBack(
            int dwSendUserId, int dwUserId, int dwCtrlCode, int wParam, int lParam, String lpStrValue) {
        logger.info(
                "[dwSendUserId={}][dwUserId={}][dwCtrlCode={}][wParam={}][lParam={}][lpStrValue={}]",
                dwSendUserId,
                dwUserId,
                dwCtrlCode,
                wParam,
                lParam,
                lpStrValue);
        return 0;
    }

    @Override
    public void OnAnyChatUserLeaveRoomActionCallBack(int dwUserId, int dwRoomId) {
        logger.info("[dwUserId={}][dwRoomId={}]", dwUserId, dwRoomId);
    }

    @Override
    public void OnAnyChatUserLoginActionCallBack(
            int dwUserId, String szUserName, int dwLevel, String szIpAddr) {
        logger.info(
                "[dwUserId={}][szUserName={}][dwLevel={}][szIpAddr={}]",
                dwUserId,
                szUserName,
                dwLevel,
                szIpAddr);
    }

    @Override
    public void OnAnyChatUserLogoutActionExCallBack(int dwUserId, int dwErrorCode) {
        logger.info("[dwUserId={}][dwErrorCode={}]", dwUserId, dwErrorCode);
    }

    @Override
    public int OnAnyChatVerifyUserCallBack(
            String szUserName, String szPassword, AnyChatVerifyUserOutParam outParam) {
        logger.info("[szUserName={}][szPassword={}][outParam={}]", szUserName, szPassword, outParam);
        Integer anychat_user_id = null;
        if (StringUtils.indexOf(szUserName, PREFIX_USER) > -1) {
            anychat_user_id = Integer.valueOf(szUserName.substring(szUserName.indexOf(PREFIX_USER) + 4));
            outParam.SetUserId(anychat_user_id);
            outParam.SetUserLevel(AnyChatConstant.LEVEL_USER);
            outParam.SetNickName(szUserName);
            logger.info(
                    DateUtil.getCurrentTime(DateUtil.DATE_TIME_FORMAT)
                            + " OnVerifyUserCallBack: userid:"
                            + anychat_user_id
                            + " username: "
                            + szUserName);
            return 0;
        } else if (StringUtils.indexOf(szUserName, PREFIX_EMP) > -1) {
            anychat_user_id = Integer.valueOf(szUserName.substring(szUserName.indexOf(PREFIX_EMP) + 3));
            outParam.SetUserId(anychat_user_id);
            outParam.SetUserLevel(AnyChatConstant.LEVEL_EMP);
            outParam.SetNickName(szUserName);
            logger.info(
                    DateUtil.getCurrentTime(DateUtil.DATE_TIME_FORMAT)
                            + " OnVerifyUserCallBack: userid:"
                            + anychat_user_id
                            + " username: "
                            + szUserName);
            return 0;
        } else {
            return 1;
        }
    }

    /** 视频呼叫事件回调，客户端调用API：BRAC_VideoCallControl会触发该回调 */
    @Override
    public int OnAnyChatVideoCallEventCallBack(
            int dwEventType,
            int dwSrcUserId,
            int dwTarUserId,
            int dwErrorCode,
            int dwFlags,
            int dwParam,
            String lpUserStr) {
        logger.info(
                "[dwEventType={}][dwSrcUserId={}][dwTarUserId={}][dwErrorCode={}][dwFlags={}][dwParam={}][lpUserStr={}]",
                dwEventType,
                dwSrcUserId,
                dwTarUserId,
                dwErrorCode,
                dwFlags,
                dwParam,
                lpUserStr);
        return 0;
    }

    protected abstract String getAnyChatVersion();

    public void init() {
        if (init_flag) {
            return;
        }
        try {
            String version = getAnyChatVersion();
            logger.info("配置的anychat版本[version={}]", version);
            String parent_sdk_name = "com.bairuitech.anychat." + version + ".AnyChatServerSDK";
            ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
            String path = classLoader.getResource("").getPath();
            Class<?> clazz = classLoader.loadClass(parent_sdk_name);

            ClassPool pool = ClassPool.getDefault();
            pool.insertClassPath(new ClassClassPath(clazz));
            CtClass super_class = pool.get(parent_sdk_name);
            CtClass anychatsdk = pool.makeClass(SDK_QUALIFIED_NAME);
            anychatsdk.setSuperclass(super_class);
            anychatsdk.writeFile(path);
            logger.info("AnyChat class文件生成成功[Qualified Name={}]", SDK_QUALIFIED_NAME + ".class");

            // 加载anychat lib
            loadAnyChatLibs(version);

            anychat = (AbstractAnyChatServerSDK) Class.forName(SDK_QUALIFIED_NAME).newInstance();
            anychat.SetServerEvent(this);
            anychat.InitSDK(0);
            anychat.RegisterVerifyUserClass(new AnyChatVerifyUserOutParam());

            String real_version = anychat.GetSDKVersion();
            version = version.replace("_", ".").toUpperCase();
            if (StringUtils.contains(real_version, version)) {
                logger.info(" Welcome use AnyChat! ({})", real_version);
            } else {
                logger.warn("lib目录下的so文件版本过低[配置版本={}],[so版本={}]", version, real_version);
            }
            init_flag = true;
        } catch (UnsatisfiedLinkError e) {
            String message = e.getMessage();
            if (StringUtils.contains(message, "already loaded in another classloader")) {
                logger.error("anychat so文件已被加载[message={}]", e.getMessage(), e);
            } else {
                logger.error(e.getMessage(), e);
            }
        } catch (NoSuchMethodError e) {
            logger.error("anychat so文件版本与配置版本不一致，so文件版本高于配置版本[message={}]", e.getMessage(), e);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }

    private void loadAnyChatLib(String version, String libName) throws IOException {
        // 获取lib文件路径
        String relativePath = "anychat" + File.separator + version + File.separator + libName;
        Resource resource = new ClassPathResource(relativePath);
        if (!resource.exists()) {
            String message = StringUtils.format("resource doses not exist [resource={}]", resource);
            throw new RuntimeException(message);
        }

        // 加载anychat lib
        URL url = resource.getURL();
        String path = url.getPath();
        logger.info("加载anychat lib文件[version={}][path={}]", version, path);
        System.load(path);
    }

    protected void loadAnyChatLibs(String version) throws IOException {
        if (StringUtils.isBlank(version)) {
            throw new RuntimeException("com.bairuitech.anychat version is blank");
        }

        // 解析操作系统
        String osName = System.getProperty("os.name");
        if (StringUtils.isBlank(osName)) {
            throw new RuntimeException("os.name environment property is blank");
        }
        // linux下anychat so不支持手工加载
        if (StringUtils.indexOf(osName.toLowerCase(), "win") == -1) {
            System.loadLibrary("anychatserver4java");
            return;
        }

        // 注意：有先后顺序
        loadAnyChatLib(version, "AnyChatServerSDK.dll");
        loadAnyChatLib(version, "anychatserver4java.dll");
    }
}
