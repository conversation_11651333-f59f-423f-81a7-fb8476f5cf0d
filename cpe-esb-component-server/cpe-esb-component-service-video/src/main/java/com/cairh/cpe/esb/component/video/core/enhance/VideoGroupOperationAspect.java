package com.cairh.cpe.esb.component.video.core.enhance;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ReflectUtil;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 视频集操作前缀补充
 *
 * <AUTHOR>
 */
@Aspect
@Component
public class VideoGroupOperationAspect {

    @SuppressWarnings("unchecked")
    @Around("execution(public * com.cairh.cpe.esb.component.video.core.IVideoGroup.*(..))")
    public Object videoGroupPrefixInject(ProceedingJoinPoint joinPoint) throws Throwable {
        Object[] args = joinPoint.getArgs();

        if (ArrayUtil.isNotEmpty(args)) {
            String video_group_prefix = (String) ReflectUtil.getFieldValue(joinPoint.getTarget(), "videoGroupPrefix");

            if (StringUtils.isNotBlank(video_group_prefix)) {
                Object arg_id = ArrayUtil.get(args, 0);
                if (arg_id instanceof String) {
                    arg_id = video_group_prefix + arg_id;
                } else {
                    arg_id = ((List<String>) arg_id).stream().map(id -> video_group_prefix + id).collect(Collectors.toList());
                }

                args[0] = arg_id;
            }
        }

        return joinPoint.proceed(args);
    }
}
