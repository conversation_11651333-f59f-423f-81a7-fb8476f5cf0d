package com.cairh.cpe.esb.component.video.refresh.task;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.cairh.cpe.component.common.model.VideoUser;
import com.cairh.cpe.esb.component.video.constant.VideoConstant;
import com.cairh.cpe.esb.component.video.core.IVideoGroup;
import com.cairh.cpe.esb.component.video.queue.IQueue;
import com.cairh.cpe.esb.component.video.queue.QueueGroup;
import com.cairh.cpe.mem.redis.util.MemLockUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;


/**
 * 视频用户位置 - RefreshTask
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class VideoUserPositionRefreshTask {

    @Autowired
    private QueueGroup queueGroup;

    @Autowired
    private RedisTemplate<Object, Object> redisTemplate;

    @Autowired
    private IVideoGroup<VideoUser> videoGroup;
    @Autowired
    private MemLockUtil redissonLockUtil;

    /**
     * 视频用户位置 - refresh - 3秒一次
     */
    @Scheduled(initialDelay = 3
            * 1000, fixedDelayString = "#{@compositePropertySources.getProperty(T(com.cairh.cpe.esb.component.video.constant.VideoPropertyConstant).USER_POSITION_REFRESH_INTERVAL, '3000')}")
    public void refresh() {
        redissonLockUtil.lockExecConsumer(getClass().getSimpleName(), "", x -> {
            log.debug("VideoUserPositionRefresh lock success");
            SpringUtil.getBean(VideoUserPositionRefreshTask.class).doRefresh();
        });
        log.debug("VideoUserPositionRefresh unlock success");
    }

    /**
     * 视频用户位置 - refresh(仅队列中)
     */
    @Transactional
    public void doRefresh() {
        List<IQueue> queues = queueGroup.getAllQueue();

        if (CollectionUtil.isEmpty(queues)) {
            log.debug("no queue exist, VideoUserPositionRefresh skip refresh");
            return;
        }

        //总排队用户数
        long total = queues.stream().mapToLong(IQueue::size).sum();

        //按队列优先级
        List<Set<String>> userIdList = CollectionUtil.sort(queues, Comparator.comparingInt(IQueue::getLevel)).stream().map(IQueue::getAllUserId).collect(Collectors.toList());

        try {
            AtomicLong position = new AtomicLong();
            userIdList.forEach(userIds -> {
                if (CollectionUtil.isNotEmpty(userIds)) {
                    userIds.forEach(userId -> {
                        videoGroup.update(userId, VideoConstant.QUEUE_POSITION, position.incrementAndGet());
                        videoGroup.update(userId, VideoConstant.QUEUE_COUNT, total);
                    });
                }
            });
        } catch (Exception e) {
            log.error("VideoUserPositionRefresh error", e);
        }

    }
}
