package com.cairh.cpe.esb.component.video.service;

import com.cairh.cpe.component.common.data.entity.VideoRecord;
import com.cairh.cpe.component.common.model.VideoUser;
import org.springframework.lang.NonNull;

/**
 * builder for {@link VideoRecord}
 *
 * <AUTHOR>
 */
public interface IVideoRecordBuilder {

    /**
     * 入队
     *
     * @param user 视频用户, 基于joinQueue事件
     * @return {@link VideoRecord}
     */
    VideoRecord joinQueueBuild(@NonNull VideoUser user);

    /**
     * 接听视频
     *
     * @param user 视频用户, 基于answerUserVideo事件
     * @return {@link VideoRecord}
     */
    VideoRecord answerVideoBuild(@NonNull VideoUser user);

    /**
     * 获取连接信息
     *
     * @param user 视频用户, 基于qryQueueInfo事件
     * @return {@link VideoRecord}
     */
    VideoRecord getConnInfoBuild(@NonNull VideoUser user);

    /**
     * 跟随状态变化
     *
     * @param user 视频用户, 基于updateUserStatus事件
     * @return {@link VideoRecord}
     */
    VideoRecord updateStatusBuild(@NonNull VideoUser user);


}
