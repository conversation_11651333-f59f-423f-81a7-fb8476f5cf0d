package com.cairh.cpe.esb.component.video.constant;

/**
 * 系统参数配置常量类
 *
 * <AUTHOR>
 */
public class VideoConfigConstant {

    /**
     * 前台地址
     */
    public static final String CONFIG_CONN_OOS_WEBADDRESS = "oos.webaddr";

    /**
     * 后台地址
     */
    public static final String CONFIG_CONN_OSS_WEBADDRESS = "oss.webaddr";

    /**
     * 券商
     */
    public static final String CONFIG_SEC_SECURITY_ALIAS = "security.alias";

    /**
     * anychat所在服务器ftp地址
     */
    public static final String CONFIG_VIDEO_ANYCHAT_FTP_ADDR = "config.video.anychat.ftp.addr";

    /**
     * anychat所在服务器ftp登录用户名
     */
    public static final String CONFIG_VIDEO_ANYCHAT_FTP_NAME = "config.video.anychat.ftp.name";

    /**
     * anychat所在服务器ftp登录密码
     */
    public static final String CONFIG_VIDEO_ANYCHAT_FTP_PASSWD = "config.video.anychat.ftp.passwd";

    /**
     * anychat所在服务器ftp端口，默认21
     */
    public static final String CONFIG_VIDEO_ANYCHAT_FTP_PORT = "config.video.anychat.ftp.port";

    /**
     * anychat所在服务器ftp根路径
     */
    public static final String CONFIG_VIDEO_ANYCHAT_FTP_ROOT = "config.video.anychat.ftp.root";

    /**
     * 视频服务器anychat模式
     */
    public static final String CONFIG_VIDEO_ANYCHAT_MODE = "config.video.anychat.mode";

    /**
     * anychat所在服务器video-anychat视频服务地址端口
     */
    public static final String CONFIG_VIDEO_ANYCHAT_REMOTE_ADDR = "config.video.anychat.remote.addr";

    /**
     * 是否自动排队自动分配队列
     */
    public static final String CONFIG_VIDEO_AUTOASSIGN_AUTO_QUEUE = "config.video.autoAssign.auto.queue";

    /**
     * 是否启动自动分配
     */
    public static final String CONFIG_VIDEO_AUTOASSIGN_FLAG = "config.video.autoAssign.flag";

    /**
     * 在审复核时是否分配任务
     */
    public static final String CONFIG_VIDEO_AUTOASSIGN_IGNORE_WHEN_AUDIT = "config.video.autoAssign.when.audit";

    /**
     * 自动分配最大忽略任务次数
     */
    public static final String CONFIG_VIDEO_AUTOASSIGN_MAX_TIMEOUT_COUNT = "config.video.autoAssign.max.timeout.count";

    /**
     * 坐席接受自动分配超时时间
     */
    public static final String CONFIG_VIDEO_AUTOASSIGN_TIMEOUT = "config.video.autoAssign.timeout";

    /**
     * 统计没有工作量的坐席
     */
    public static final String CONFIG_VIDEO_COUNT_NO_WORKLOAD = "config.video.count.no.workload";

    /**
     * 云屋服务器地址
     */
    public static final String CONFIG_VIDEO_CRVIDEO_SERVER_ADDRESS = "config.video.crVideo.server.address";

    /**
     * 云屋账号名
     */
    public static final String CONFIG_VIDEO_CRVIDEO_USERNAME = "config.video.crVideo.userName";

    /**
     * 云屋授权密码
     */
    public static final String CONFIG_VIDEO_CRVIDEO_USERPSWD = "config.video.crVideo.userPswd";

    /**
     * 视频列表展示所有营业部的视频（忽略允许营业部控制）
     */
    public static final String CONFIG_VIDEO_LIST_ALL_BRANCH = "config.video.list.all.branch";

    /**
     * 视频列表自动刷新间隔
     */
    public static final String CONFIG_VIDEO_LIST_AUTO_REFRESH_INTERVAL = "config.video.list.auto.refresh.interval";

    /**
     * 视频日志级别
     */
    public static final String CONFIG_VIDEO_LOG_LEVEL = "config.video.log.level";

    /**
     * 视频队列监控页面是否不需要登陆
     */
    public static final String CONFIG_VIDEO_NEED_LOGIN_VIDEO_QUEUE_MONITOR = "need.login.video.queue.monitor";

    /**
     * 是否单一视频提供商
     */
    public static final String CONFIG_VIDEO_ONLY_ONE_VENDER = "config.video.only.one.vende";

    /**
     * 是否显示实际位置
     */
    public static final String CONFIG_VIDEO_QUEUE_SHOW_REAL_POSITION = "config.video.queue.show.real.position";

    /**
     * 腾迅云accountType
     */
    public static final String CONFIG_VIDEO_TENCENT_ACCOUNT_TYPE = "config.video.tencent.account.type";

    /**
     * 腾讯云私钥文件路径
     */
    public static final String CONFIG_VIDEO_TENCENT_PRIVATE_KEY_PATH = "config.video.tencent.cloud.private.key.path";

    /**
     * 腾讯企点在线操作员列表
     */
    public static final String CONFIG_VIDEO_TENCENT_QD_ONLINEEMP = "config.video.tencent.qd.onlineEmp";

    /**
     * 腾讯云sdkappid
     */
    public static final String CONFIG_VIDEO_TENCENT_SDK_APPID = "config.video.tencent.sdk.appid";

    /**
     * 视频提供商名称
     */
    public static final String CONFIG_VIDEO_VENDER = "config.video.vender";

    /**
     * NAT穿透服务端口
     */
    public static final String CONFIG_VIDEO_WANGSUO_NAT_TRAVERSAL_PORT = "config.video.wangsuo.nat.traversal.port";

    /**
     * 视频服务地址
     */
    public static final String CONFIG_VIDEO_WANGSUO_SERVER_ADDR_IN = "config.video.wangsuo.server.addr.in";

    /**
     * 视频服务地址
     */
    public static final String CONFIG_VIDEO_WANGSUO_SERVER_ADDR_OUT = "config.video.wangsuo.server.addr.out";

    /**
     * 视频服务端口
     */
    public static final String CONFIG_VIDEO_WANGSUO_SERVER_PORT = "config.video.wangsuo.server.port";

    /**
     * web服务地址
     */
    public static final String CONFIG_VIDEO_WANGSUO_WEB_SERVICE_ADDR = "config.video.wangsuo.web.addr";

    /**
     * anychat文件上传速率限制
     */
    public static final String CONFIG_VIDEO_ANYCHAT_UPLOAD_LIMIT = "config.video.anychat.upload.limit";

    /**
     * 自动分配下是否允许调用抢答接口
     */
    public static final String CONFIG_VIDEO_AUTOASSIGN_ENABLE_CAPTURE = "config.video.autoAssign.enable.capture";

    /**
     * 自动分配时弹出的提示框是否增加忽略按钮
     */
    public static final String CONFIG_VIDEO_AUTOASSIGN_ENABLE_IGNORE = "config.video.autoAssign.enable.ignore";

    /**
     * 自动分配普通营业部持有用户时间
     */
    public static final String
            CONFIG_VIDEO_AUTOASSIGN_ALLBRANCH_HOLD_USER_TIME = "config.video.autoAssign.allbranch.hold.user.time";

    /**
     * 自动排队休息时间
     */
    public static final String CONFIG_VIDEO_AUTOASSIGN_REST_TIME = "config.video.autoAssign.rest.time";

    /**
     * 是否仅在分配任务后，才响铃视频提示音
     */
    public static final String
            CONFIG_VIDEO_AUTOASSIGN_PLAY_SOUND_WHEN_ASSIGNED = "config.video.autoAssign.play.sound.when.assigned";

    /**
     * 坐席角色，所有有配置角色的操作员判断为坐席
     */
    public static final String CONFIG_VIDEO_OPERATOR_ROLE = "config.video.operator.role";

    /**
     * 视频队列位置保留时间（分钟）
     */
    public static final String CONFIG_VIDEO_QUEUE_POSITION_RETAIN_TIME = "config.video.queue.position.retain.time";

    /**
     * 是否优先分配上一次接入的坐席
     */
    public static final String
            CONFIG_VIDEO_AUTO_ASSIGN_PREVIOUS_OPERATOR = "config.video.autoAssign.assign.previous.operator";

    /**
     * 是否开启视频监控坐席与客户的视频验证
     */
    public static final String CONFIG_VIDEO_MONITOR_USER_VIDEO = "config.video.monitor.user.video";

    /**
     * 视频打回后是否发送短信给客户
     */
    public static final String CONFIG_VIDEO_VERIFY_ENABLE_SMS = "config.video.verify.enable.sms";

    /**
     * 发送短信内容长度限制
     */
    public static final String SMS_THRESHOLD_CONTENT_COMPARE_LENGTH = "sms.threshold.content.compare.length";

    /**
     * 自动分配进入抢答时间
     */
    public static final String
            CONFIG_VIDEO_AUTOASSIGN_ENTER_CAPTURE_TIME = "config.video.autoAssign.enter.capture.time";

    /**
     * anychat文件上传回调方式
     */
    public static final String
            CONFIG_VIDEO_ANYCHAT_UPLOAD_FILE_CALLBACK_WAY = "config.video.anychat.upload.file.callback.way";

    /**
     * anychat云端模式下载文件方式
     */
    public static final String CONFIG_VIDEO_ANYCHAT_DOWNLOAD_FILE_WAY = "config.video.anychat.download.file.way";

    /**
     * 最大排队数
     */
    public static final String CONFIG_VIDEO_QUEUE_MAX_QUEUE_NUM = "config.video.queue.max.queue.num";

    /**
     * 即构视频分配的appid
     */
    public static final String CONFIG_VIDEO_ZEGO_APP_ID = "config.video.zego.app.id";

    /**
     * 即构视频token请求接口地址
     */
    public static final String CONFIG_VIDEO_ZEGO_TOKEN_URL = "config.video.zego.token.url";

    /**
     * 即构视频服务地址
     */
    public static final String CONFIG_VIDEO_ZEGO_VIDEO_ADDRESS = "config.video.zego.video.address";

    /**
     * 即构视频服务是否使用https连接
     */
    public static final String CONFIG_VIDEO_ZEGO_USE_HTTPS = "config.video.zego.use.https";

    /**
     * 即构视频分配的sign_key
     */
    public static final String CONFIG_VIDEO_ZEGO_SIGN_KEY = "comp.video.zego.sign.key";
    /**
     * 即构视频挂载配置
     */
    public static final String CONFIG_VIDEO_ZEGO_VIDEO_FILE_URL = "comp.video.zego.video.url";
    /**
     * 即构视频环境
     */
    public static final String CONFIG_VIDEO_ZEGO_ENV = "config.video.zego.env";


    /**
     * 证通云-即构视频服务是否使用https连接
     */
    public static final String CONFIG_VIDEO_ZHENGTONG_USE_HTTPS = "config.video.zhengtong.use.https";

    /**
     * 证通云-即构视频分配的sign_key
     */
    public static final String CONFIG_VIDEO_ZHENGTONG_SIGN_KEY = "comp_video_zhengtong_h5_sign_key";

    /**
     * 证通云-即构视频环境
     */
    public static final String CONFIG_VIDEO_ZHENGTONG_ENV = "config.video.zhengtong.env";





    /**
     * 双向视频沙箱在客户端坐席画面上显示的坐席信息
     */
    public static final String CONFIG_VIDEO_TWO_WAY_SANDBOX_OP_INFO = "config.video.two.way.sandbox.op.info";

    /**
     * 科大讯飞分配的app_id
     */
    public static final String CONFIG_VIDEO_AUDIOTEXT_IFLYTEK_APP_ID = "config.video.audiotext.iflytek.app.id";

    /**
     * 科大讯飞分配的密钥
     */
    public static final String CONFIG_VIDEO_AUDIOTEXT_IFLYTEK_SECRET = "config.video.audiotext.iflytek.secret";

    /**
     * 科大讯飞语音识别接口地址
     */
    public static final String CONFIG_VIDEO_AUDIOTEXT_IFLYTEK_ASR_API_URL = "config.video.audiotext.iflytek.asr.api.url";

    /**
     * 科大讯飞发音人
     */
    public static final String CONFIG_VIDEO_AUDIOTEXT_IFLYTEK_VOICE_NAME = "config.video.audiotext.iflytek.voice.name";

    /**
     * 科大讯飞结果轮询超时时间
     */
    public static final String
            CONFIG_VIDEO_AUDIOTEXT_IFLYTEK_POLLING_TIMEOUT = "config.video.audiotext.iflytek.polling.timeout";

    /**
     * 语音播报转换结果有效期
     */
    public static final String
            CONFIG_VIDEO_BROADCAST_VALID_DATE_PERIOD = "config.video.broadcast.valid.date.period";

    /**
     * 双向视频沙箱客户视频窗口是否显示头像框
     */
    public static final String
            CONFIG_VIDEO_TWO_WAY_CLIENT_PHOTO_FRAME = "config.video.two.way.client.photo.frame";

    /**
     * 视频文件编码格式转换超时时间
     */
    public static final String
            CONFIG_VIDEO_CONVERT_VIDEO_FORMAT_TIMEOUT = "config.video.convert.video.format.timeout";

    /**
     * 视频打回项是否支持备注
     */
    public static final String
            CONFIG_VIDEO_REPULSE_HAS_REMARK = "config.video.repulse.has.remark";

    /**
     * 视频打回项是否展示其他
     */
    public static final String
            CONFIG_VIDEO_SHOW_REPULSE_ITEM_OTHERS = "config.video.show.repulse.item.others";

    /**
     * 即构h5私有云视频token请求接口地址
     */
    public static final String
            CONFIG_VIDEO_ZEGO_H5_PRIVATE_CLOUD_TOKEN_URL = "config.video.zego.h5.private.cloud.token.url";

    /**
     * 即构视频录制服务地址
     */
    public static final String
            CONFIG_VIDEO_ZEGO_RECORD_SERVER_ADDRESS = "config.video.zego.record.server.address";

    /**
     * h5私有云token实现方式
     */
    public static final String
            CONFIG_VIDEO_ZEGO_H5_PRIVATE_CLOUD_TOKEN_WAY = "config.video.zego.h5.private.cloud.token.way";

    /**
     * h5私有云分配的appSign
     */
    public static final String
            CONFIG_VIDEO_ZEGO_H5_PRIVATE_CLOUD_appSign = "config.video.zego.h5.private.cloud.appSign";

    /**
     * 坐席活跃判定超时时间
     */
    public static final String
            CONFIG_VIDEO_MONITOR_OPERATOR_ACTIVE_TIMEOUT = "config.video.monitor.operator.active.timeout";

    /**
     * 是否开启视频坐席浏览器H5视频权限检测
     */
    public static final String
            CONFIG_VIDEO_OPERATOR_LOGIN_CHECK_WEBRTC = "config.video.operator.login.check.webrtc";

    /**
     * 是否开启视频坐席浏览器型号检测
     */
    public static final String
            CONFIG_VIDEO_OPERATOR_LOGIN_CHECK_BROWSER = "config.video.operator.login.check.browser";

    /**
     * 允许的视频坐席浏览器型号
     */
    public static final String
            CONFIG_VIDEO_OPERATOR_LOGIN_CHECK_ENABLE_BROWSER = "config.video.operator.login.check.enable.browser";

    /**
     * 蚂蚁房间服务器
     */
    public static final String CONFIG_VIDEO_ANT_MTC_SERVER_URL = "config.video.ant.mtc.server.url";

    /**
     * 蚂蚁分配bizName
     */
    public static final String CONFIG_VIDEO_ANT_BIZ_NAME = "config.video.ant.biz.name";

    /**
     * 蚂蚁分配subBiz
     */
    public static final String CONFIG_VIDEO_ANT_SUB_BIZ = "config.video.ant.sub.biz";

    /**
     * 蚂蚁允许最大断网时间
     */
    public static final String CONFIG_VIDEO_ANT_NETWORK_CHECK_TIMEOUT = "config.video.ant.network.check.timeout";

    /**
     * 蚂蚁分配公钥
     */
    public static final String CONFIG_VIDEO_ANT_PUBLIC_KEY = "config.video.ant.public.key";

    /**
     * 蚂蚁上传视频挂载地址前缀
     */
    public static final String CONFIG_VIDEO_ANT_UPLOAD_ADDRESS_PREFIX = "config.video.ant.upload.address.prefix";

    /**
     * 视频工作量统计是否包含坐席状态条件
     */
    public static final String
            CONFIG_VIDEO_WORKLOAD_HAS_OPUSER_STATUS = "config.video.workload.has.opuser.status";

    /**
     * AnyChat前端连接密码
     */
    public static final String
            CONFIG_VIDEO_ANYCHAT_FRONT_CONN_PASSWORD = "config.video.anychat.front.conn.password";

    /**
     * 视频排队心跳超时时间
     */
    public static final String CONFIG_VIDEO_QUEUE_HEARTBEAT_TIMEOUT = "config.video.queue.heartbeat.timeout";

    /**
     * 坐席接听客户之后 到 客户端知道客户已被接听，这个过程之间的超时时间
     */
    public static final String CONFIG_VIDEO_CLIENT_KNOW_USER_BE_ANSWERED_TIMEOUT
            = "config.video.client.know.user.be.answered.timeout";

    /**
     * AnyChat版本
     */
    public static final String ANYCHAT_VERSION = "anychat.version";

    /**
     * AnyChat处理文件上传事件名称
     */
    public static final String CONFIG_VIDEO_ANYCHAT_DEAL_UPLOAD_EVENT_NAME
            = "config.video.anychat.deal.upload.event.name";

    /**
     * 新意服务地址
     */
    public static final String CONFIG_VIDEO_SHINE_SERVICE_ADDRESS = "config.video.shine.service.address";

    /**
     * 新意APP_ID
     */
    public static final String CONFIG_VIDEO_SHINE_APP_ID = "config.video.shine.app.id";

    /**
     * 新意APP_KEY
     */
    public static final String CONFIG_VIDEO_SHINE_APP_KEY = "config.video.shine.app.key";

    /**
     * 新意APP_SECRET
     */
    public static final String CONFIG_VIDEO_SHINE_APP_SECRET = "config.video.shine.app.secret";

    /**
     * （单向视频）腾讯云SDK Appid
     */
    public static final String CONFIG_VIDEO_TENCENT_ONE_WAY_SDK_APPID = "config.video.tencent.one.way.sdk.appid";

    /**
     * （单向视频）腾讯云私钥
     */
    public static final String
            CONFIG_VIDEO_TENCENT_ONE_WAY_PASSWORD_PRIVATE_KEY = "config.video.tencent.one.way.password.private.key";

    /**
     * （单向视频）腾讯云API密钥-SecretId
     */
    public static final String
            CONFIG_VIDEO_TENCENT_ONE_WAY_PASSWORD_API_SECRET_ID = "config.video.tencent.one.way.password.api.secret.id";

    /**
     * （单向视频）腾讯云API密钥-SecretKey
     */
    public static final String
            CONFIG_VIDEO_TENCENT_ONE_WAY_PASSWORD_API_SECRET_KEY = "config.video.tencent.one.way.password.api.secret.key";

    /**
     * 即构H5视频本地录制
     */
    public static final String CONFIG_VIDEO_ZEGO_H5_RECROD_LOCAL = "config.video.zego.h5.record.local";

    /**
     * 视频播放是否允许手动改变进度条
     */
    public static final String CONFIG_VIDEO_PLAY_ENABLE_MANUAL_CHANGE_PROGRESS
            = "config.video.play.enable.manual.change.progress";

    /**
     * 中台服务地址
     */
    public static final String SISAP_WEBSERVICE_URL = "sisap.webservice.url";

    /**
     * 搜狗数字人服务地址
     */
    public static final String CONFIG_VIDEO_SOUGOU_SZR_SERVICE_ADDRESS = "config.video.sougou.szr.service.address";

    /**
     * 搜狗数字人ACESS_KEY
     */
    public static final String CONFIG_VIDEO_SOUGOU_SZR_ACCESS_KEY = "config.video.sougou.szr.access.key";

    /**
     * 搜狗数字人SECRET_KEY
     */
    public static final String CONFIG_VIDEO_SOUGOU_SZR_SECRET_KEY = "config.video.sougou.szr.secret.key";

    /**
     * 开户断点跟踪推送开关
     */
    public static final String CONFIG_BREAK_TRACK_KAFKA_BUTTON = "config.video.break.track.kafka.button";

    /**
     * 开户断点跟踪用户等待时间
     */
    public static final String CONFIG_BREAK_TRACK_WAIT_TIME = "config.video.break.track.wait.time";

    /**
     * 开户断点跟踪kafka topic
     */
    public static final String CONFIG_BREAK_TRACK_KAFKA_TOPIC = "config.video.break.track.kafka.topic";

    /**
     * 工作日工作时间段
     */
    public static final String CONFIG_VIDEO_WORK_TRADING_TIME = "config.video.work.trading.time";

    public static final String VIDEO_PARAM_FROM_CACHE = "video_param_from_cache";
}
