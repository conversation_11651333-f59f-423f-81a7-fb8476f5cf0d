package com.cairh.cpe.esb.component.video.core.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ReflectUtil;
import com.cairh.cpe.component.common.model.VideoUser;
import com.cairh.cpe.esb.component.video.core.AbstractGenericVideoGroup;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 视频用户集, 基于视频集, 对视频用户信息作出维护
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class VideoUserGroup extends AbstractGenericVideoGroup<VideoUser> {
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    private static final String prefix = "{cpe_esb_video}";
    private static final String VIDEO_USER = "video_user";
    public VideoUserGroup() {
        super(VIDEO_USER);
    }

    public VideoUserGroup(String videoGroupPrefix) {
        super(videoGroupPrefix);
    }

    @Override
    @Transactional
    public void insert(String id, VideoUser video) {
        Map<String, Object> videoMap = BeanUtil.beanToMap(video);
        try {
            redisTemplate.opsForHash().putAll(id, videoMap);
            redisTemplate.opsForSet().add(videoGroupPrefix, StringUtils.removeStart(id, videoGroupPrefix));
        } catch (Exception e) {
            log.error("video group insert error, id: " + id, e);
            throw e;
        }
    }
//
//    @Override
//    @Transactional
//    public void update(String id, Map<String, Object> info) {
//        if (exist(id)) {
//            try {
//                redisTemplate.opsForHash().putAll(prefix + VIDEO_USER + id, info);
//            } catch (Exception e) {
//                log.error("video group update error, id: " + id, e);
//                throw e;
//            }
//        }
//    }
//
//    @Override
//    public void update(String id, String key, Object value) {
//        redisTemplate.opsForHash().put(prefix + VIDEO_USER + id, key, value);
//    }
//
//    @Override
//    public VideoUser query(String id) {
//        Map<Object, Object> videoMap = redisTemplate.opsForHash().entries(prefix + VIDEO_USER + id);
//
//        if (CollectionUtil.isNotEmpty(videoMap)) {
//            VideoUser video = ReflectUtil.newInstanceIfPossible(VideoUser.class);
//            return BeanUtil.fillBeanWithMap(videoMap, video, CopyOptions.create().setIgnoreError(false));
//        }
//
//        return null;
//    }
//
//    @Override
//    public List<VideoUser> query(List<String> ids) {
//        return super.query(ids);
//    }
//
//    @Override
//    public List<VideoUser> queryAll() {
//        return super.queryAll();
//    }
//
//    @Override
//    public Map<String, VideoUser> queryAllMap() {
//        Set<Object> videoKeySet = redisTemplate.opsForSet().members(prefix + videoGroupPrefix);
//
//        if (CollectionUtil.isNotEmpty(videoKeySet)) {
//            List<String> videoKeys = videoKeySet.stream().map(String::valueOf).collect(Collectors.toList());
//
//            Map<String, VideoUser> result = new LinkedHashMap<>();
//            videoKeys.forEach(key -> result.put(key, this.query(key)));
//            return result;
//        }
//
//        return null;
//    }
//
//    @Override
//    public boolean exist(String id) {
//        return BooleanUtil.isTrue(redisTemplate.hasKey(prefix + VIDEO_USER + id));
//    }
//
//    @Override
//    @Transactional
//    public void remove(String id) {
//        try {
//            redisTemplate.delete(prefix + VIDEO_USER +  id);
//            redisTemplate.opsForSet().remove(prefix + videoGroupPrefix, StringUtils.removeStart(id, videoGroupPrefix));
//        } catch (Exception e) {
//            log.error("video group remove error, id: " + id, e);
//            throw e;
//        }
//    }

}
