package com.cairh.cpe.esb.component.video.refresh.task;

import cn.hutool.core.collection.CollectionUtil;
import com.cairh.cpe.component.common.model.VideoUser;
import com.cairh.cpe.esb.component.video.constant.VideoConstant;
import com.cairh.cpe.esb.component.video.core.IVideoGroup;
import com.cairh.cpe.esb.component.video.queue.IQueue;
import com.cairh.cpe.esb.component.video.queue.QueueGroup;
import com.cairh.cpe.mem.redis.util.MemLockUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 视频用户状态 - RefreshTask
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class VideoUserStatusRefreshTask {

    @Autowired
    private IVideoGroup<VideoUser> videoGroup;

    @Autowired
    private QueueGroup queueGroup;
    @Autowired
    private MemLockUtil redissonLockUtil;


    /**
     * 视频用户状态 - refresh - 5秒一次
     */
    @Scheduled(initialDelay = 5 * 1000, fixedDelayString = "#{@compositePropertySources.getProperty(T(com.cairh.cpe.esb.component.video.constant.VideoPropertyConstant).USER_STATUS_REFRESH_INTERVAL, '5000')}")
    public void refresh() {
        redissonLockUtil.lockExecConsumer(getClass().getSimpleName(), "", x-> {
            log.debug("VideoUserStatusRefresh lock success");
            doRefresh();
            log.debug("VideoUserStatusRefresh unlock success");
        });
    }

    /**
     * 视频用户状态 - refresh
     */
    private void doRefresh() {
        List<VideoUser> users = videoGroup.queryAll();

        if (CollectionUtil.isNotEmpty(users)) {
            users.forEach(user -> {
                IQueue queue = queueGroup.exist(user.getUnique_id());
//                log.info("status-refresh user: {} relate to queue: {}", user.getStaff_no(), queue.getName());
                if (Objects.nonNull(queue)) {
                    // 用户在队列中
                    if (!StringUtils.equals(user.getStatus(), VideoConstant.STATUS_0_WAITING)) {
                        videoGroup.update(user.getUnique_id(), Collections.singletonMap(VideoConstant.STATUS, VideoConstant.STATUS_0_WAITING));
                    }
                }
                // 其它维持原状态
            });
        }
    }
}
