package com.cairh.cpe.esb.component.video.refresh.task;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.cairh.cpe.component.common.data.service.IVideoRecordService;
import com.cairh.cpe.component.common.model.VideoUser;
import com.cairh.cpe.component.common.utils.StringUtils;
import com.cairh.cpe.core.autoconfiure.env.CompositePropertySources;
import com.cairh.cpe.esb.component.video.constant.VideoConstant;
import com.cairh.cpe.esb.component.video.constant.VideoPropertyConstant;
import com.cairh.cpe.esb.component.video.core.IVideoGroup;
import com.cairh.cpe.esb.component.video.queue.IQueue;
import com.cairh.cpe.esb.component.video.queue.QueueGroup;
import com.cairh.cpe.esb.component.video.service.IVideoRecordBuilder;
import com.cairh.cpe.mem.redis.util.MemLockUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 非活跃视频用户 - RefreshTask(信息清理)
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class UnlivelyVideoUserInfoRefreshTask {

    @Autowired
    private IVideoGroup<VideoUser> videoGroup;

    @Autowired
    private CompositePropertySources compositePropertySources;

    @Autowired
    private MemLockUtil redissonLockUtil;

    @Autowired
    private QueueGroup queueGroup;

    @Autowired
    private IVideoRecordService videoRecordService;

    @Autowired
    private IVideoRecordBuilder videoRecordBuilder;
    @Autowired
    private RedisTemplate<Object, Object> redisTemplate;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 非活跃视频用户 - refresh - 一天一次
     */
    @Scheduled(initialDelay = 1000, fixedDelayString = "#{@compositePropertySources.getProperty(T(com.cairh.cpe.esb.component.video.constant.VideoPropertyConstant).UNLIVE_USER_INFO_REFRESH_INTERVAL, '86400000')}")
    public void refresh() {
        redissonLockUtil.lockExecConsumer(getClass().getSimpleName(), "", x -> {
            log.debug("UnlivelyVideoUserInfoRefresh lock success");
            doRefresh();
            log.debug("UnlivelyVideoUserInfoRefresh unlock success");
        });
    }

    /**
     * 非活跃视频用户 - refresh
     */
    private void doRefresh() {

        List<VideoUser> users = videoGroup.queryAll();
        if (CollectionUtil.isNotEmpty(users)) {
            users.forEach(user -> {
                String uniqueId = user.getUnique_id();
                //清理用户 异常退出  判断条件：心跳时间超过十秒无响应 且状态为 排队中（status=0）
                IQueue queue = queueGroup.exist(uniqueId);
                if (Objects.nonNull(queue)) {
                    if (abnormalExit(user)) {
                        //修改 用户状态
                        videoGroup.update(uniqueId, VideoUser.STATUS, "");
                        //移除排队
                        removeQueue(user, uniqueId);
                        //清理 缓存排队位置
                        clearCache(user);
                    }
                }
                if (unLive(user)) {
                    //按顺序依次刷新(全局 -> 自身)
                    videoGroup.remove(uniqueId);
                    redisTemplate.opsForSet().remove("{cpe_esb_video}video_user", uniqueId);
                    log.debug("清理非活跃用户(业务流水号)：" + uniqueId);
                    //清理 缓存排队位置
                    clearCache(user);
                    //移除排队
                    removeQueue(user, uniqueId);
                }
            });

        }

    }

    private void clearCache(VideoUser user) {
        //清理 缓存排队位置
        String videoNum = "VIDEO_QUEUE_USER_WAIT_POSITION" + user.getUnique_id();
        if (stringRedisTemplate.hasKey(videoNum)) {
            log.info("定时任务清理排队位子缓存：{}",videoNum);
            stringRedisTemplate.delete(videoNum);
        }
    }

    private void removeQueue(VideoUser user, String uniqueId) {
        // 队列信息清掉
        // 按顺序依次刷新(队列)
        IQueue queue = queueGroup.exist(uniqueId);
        if (Objects.nonNull(queue)) {
            // 非活跃用户仍处于排队状态
            if (queue.dequeue(uniqueId)) {
                log.info("清理非活跃用户队列清理成功(队列名称)：" + queue.getName());
                // 处理用户被清理出视频队列
                user.setStatus(VideoConstant.STATUS_5_VIDEOED);
                videoRecordService.save(videoRecordBuilder.updateStatusBuild(user));
                log.debug("UnlivelyVideoUserRefresh user dequeue success, user_id: {}", uniqueId);
            } else {
                log.debug("UnlivelyVideoUserRefresh user dequeue failed, user_id: {}", uniqueId);
            }
        }
    }

    /**
     * 视频用户是否活跃
     *
     * @param
     * @return 是否活跃, true否, false是
     */
    private boolean abnormalExit(VideoUser user) {
        String uniqueId = user.getUnique_id();
        if (Objects.isNull(user) || Objects.isNull(user.getLast_status_update_time()) || user.getLast_status_update_time() <= 0 || Objects.isNull(user.getStatus()) || !StringUtils.equals("0", user.getStatus())) {
            return false;
        }
        if (Objects.isNull(user) || Objects.isNull(user.getLast_status_update_time()) || user.getLast_status_update_time() <= 0L) {
            //用户不存在,但已无凭证或状态最后变更时间尚未赋值/异常取值,统一认为用户新到尚活跃
            return false;
        }

        VideoUser newUser = videoGroup.query(uniqueId);
        if (Objects.isNull(newUser) || Objects.isNull(newUser.getLast_status_update_time()) || newUser.getLast_status_update_time() <= 0 || Objects.isNull(newUser.getStatus()) || !StringUtils.equals("0", newUser.getStatus())) {
            return false;
        }
        long o = System.currentTimeMillis();
        Long lastStatusUpdateTime = newUser.getLast_status_update_time();
        long interval = (o - lastStatusUpdateTime) / 1000;
        //状态最后变更时间超过阈值认为非活跃
        boolean flag = interval > 5;
        if (flag) {
            log.info("心跳--清理异常退出的用户:{},用户最后一次心跳时间:{}, {}",uniqueId,o ,newUser.getLast_status_update_time());
        }
        return flag;
    }


    /**
     * 视频用户是否活跃
     *
     * @param user 视频用户
     * @return 是否活跃, true否, false是
     */
    private boolean unLive(VideoUser user) {
        if (Objects.isNull(user) || Objects.isNull(user.getLast_status_update_time()) || user.getLast_status_update_time() <= 0L) {
            //用户不存在,但已无凭证或状态最后变更时间尚未赋值/异常取值,统一认为用户新到尚活跃
            return false;
        }

        long interval = System.currentTimeMillis() - user.getLast_status_update_time();
        if(interval > Long.parseLong(compositePropertySources.getProperty(VideoPropertyConstant.USER_INFO_EXPIRE_INTERVAL, String.valueOf(86400000)))) {
            log.info("非活跃--清理异常退出的用户:{},用户最后一次心跳时间:{}, {}",user.getUnique_id(),System.currentTimeMillis() ,user.getLast_status_update_time());
        }
        //状态最后变更时间超过阈值认为非活跃
        return interval > Long.parseLong(compositePropertySources.getProperty(VideoPropertyConstant.USER_INFO_EXPIRE_INTERVAL, String.valueOf(86400000)));
    }
}
