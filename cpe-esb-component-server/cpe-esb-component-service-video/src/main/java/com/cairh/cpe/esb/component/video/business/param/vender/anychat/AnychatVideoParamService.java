package com.cairh.cpe.esb.component.video.business.param.vender.anychat;

import cn.hutool.core.util.StrUtil;
import com.cairh.cpe.component.common.model.VideoOperator;
import com.cairh.cpe.component.common.model.VideoUser;
import com.cairh.cpe.core.autoconfiure.env.CompositePropertySources;
import com.cairh.cpe.esb.component.video.business.IVenderVideoParamService;
import com.cairh.cpe.esb.component.video.constant.VideoConstant;
import com.cairh.cpe.esb.component.video.constant.VideoPropertyConstant;
import com.cairh.cpe.esb.component.video.dto.resp.StartRecordResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.support.atomic.RedisAtomicLong;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * anychat 厂商-视频联通参数
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class AnychatVideoParamService implements IVenderVideoParamService {

    @Autowired
    private CompositePropertySources compositePropertySources;

    @Autowired
    @Qualifier("roomIdAutoIncrementer")
    private RedisAtomicLong roomIdAutoIncrementer;

    @Override
    public Map<String, Object> getCommonVideoParam(VideoUser user,boolean isToken) {
        Map<String, Object> result = new HashMap<>();

        // 房间号
        String roomId;
        if (StrUtil.isNotBlank(user.getRoom_id())) {
            roomId = user.getRoom_id();
        } else {
            roomId = String.valueOf(roomIdAutoIncrementer.getAndIncrement());
        }

        // 房间密码
        String roomPwd = "123456";
        // 用户登录名
        String loginName = roomId;
        // 坐席anychat id
        String remoteUserId = StringUtils.leftPad(user.getOperator_no(), 8, "20000000");
        // h5端口
        String anychatH5Port = compositePropertySources.getProperty(VideoPropertyConstant.ANYCHAT_H5_PORT);
        // h5地址
        String anychatH5Address = compositePropertySources.getProperty(VideoPropertyConstant.ANYCHAT_H5_ADDR);
        // 内网地址
        String serverAddrIn;
        // 外网地址
        String serverAddrOut;
        // 服务端口
        String serverPort;
        // 集群标志
        String groupFlag;
        // 集群id
        String appguid;

        if (user.isAnychat_customizable()) {
            log.info("视频用户自定制anychat连接参数");
            serverAddrIn = user.getServer_addr_in();
            serverAddrOut = user.getServer_addr_out();
            serverPort = user.getServer_port();
            groupFlag = user.getAnychat_group_flag();
            appguid = user.getAnychat_appguid();
        } else {
            serverAddrIn = compositePropertySources.getProperty(VideoPropertyConstant.ANYCHAT_INTRANET_ADDR);
            serverAddrOut = compositePropertySources.getProperty(VideoPropertyConstant.ANYCHAT_INTERNET_ADDR, "127.0.0.1");
            serverPort = compositePropertySources.getProperty(VideoPropertyConstant.ANYCHAT_PORT, "8906");
            groupFlag = compositePropertySources.getProperty(VideoPropertyConstant.ANYCHAT_CLUSTER_IDENTIFICATION, Boolean.FALSE.toString());
            appguid = compositePropertySources.getProperty(VideoPropertyConstant.ANYCHAT_CLUSTER_APPGUID);
        }

        result.put(VideoConstant.ROOM_ID, roomId);
        result.put(VideoConstant.ROOM_PASSWORD, roomPwd);
        result.put(VideoConstant.LOGIN_NAME, loginName);
        result.put(VideoConstant.REMOTE_USER_ID, remoteUserId);
        result.put(VideoConstant.ANYCHAT_H5_PORT, anychatH5Port);
        result.put(VideoConstant.ANYCHAT_H5_ADDRESS, anychatH5Address);
        result.put(VideoConstant.SERVER_ADDR_IN, serverAddrIn);
        result.put(VideoConstant.SERVER_ADDR_OUT, serverAddrOut);
        result.put(VideoConstant.SERVER_PORT, serverPort);
        result.put(VideoConstant.ANYCHAT_GROUP_FLAG, groupFlag);
        result.put(VideoConstant.ANYCHAT_APP_GUID, appguid);

        return result;
    }

    @Override
    public Map<String, Object> getSpecialVideoParam(VideoUser user,boolean isToken) {
        Map<String, Object> result = new HashMap<>();

        // 房间号
        String roomId;
        if (StrUtil.isNotBlank(user.getRoom_id())) {
            roomId = user.getRoom_id();
        } else {
            roomId = String.valueOf(roomIdAutoIncrementer.getAndIncrement());
        }

        // 房间密码
        String roomPwd = "123456";
        // 用户登录名
        String loginName = roomId;
        // 坐席anychat id
        String remoteUserId = StringUtils.leftPad(user.getOperator_no(), 8, "20000000");

        result.put(VideoConstant.ROOM_ID, roomId);
        result.put(VideoConstant.ROOM_PASSWORD, roomPwd);
        result.put(VideoConstant.LOGIN_NAME, loginName);
        result.put(VideoConstant.REMOTE_USER_ID, remoteUserId);

        return result;
    }

    /**
     * 获取缓存视频参数
     *
     * @param user 视频用户
     * @return 视频联通参数
     */
    @Override
    public Map<String, Object> getCachedVideoParam(VideoUser user) {
        Map<String, Object> result = new HashMap<>();

        result.put(VideoConstant.ROOM_ID, user.getRoom_id());
        result.put(VideoConstant.ROOM_PASSWORD, user.getRoom_pwd());
        result.put(VideoConstant.LOGIN_NAME, user.getLogin_name());
        result.put(VideoConstant.REMOTE_USER_ID, user.getRemote_user_id());
        result.put(VideoConstant.ANYCHAT_H5_PORT, user.getAnychat_h5_port());
        result.put(VideoConstant.ANYCHAT_H5_ADDRESS, user.getAnychat_h5_address());
        result.put(VideoConstant.SERVER_ADDR_IN, user.getServer_addr_in());
        result.put(VideoConstant.SERVER_ADDR_OUT, user.getServer_addr_out());
        result.put(VideoConstant.SERVER_PORT, user.getServer_port());
        result.put(VideoConstant.ANYCHAT_GROUP_FLAG, user.getAnychat_group_flag());
        result.put(VideoConstant.ANYCHAT_APP_GUID, user.getAnychat_appguid());

        return result;
    }

    @Override
    public StartRecordResponse startRecord(VideoOperator videoOperator, VideoUser videoUser,String waterMark) {
        return null;
    }

    @Override
    public void stopRecord(VideoOperator videoOperator, VideoUser videoUser) {

    }
}
