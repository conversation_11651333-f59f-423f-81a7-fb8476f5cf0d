package com.cairh.cpe.esb.component.video.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.component.common.data.entity.AssemblyAccept;
import com.cairh.cpe.component.common.data.mapper.AssemblyAcceptMapper;
import com.cairh.cpe.component.common.model.*;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.context.constant.ErrorCode;
import com.cairh.cpe.esb.component.video.service.IAssemblyAcceptService;
import com.cairh.cpe.esb.component.video.service.IAssemblyParamInfoService;
import com.cairh.cpe.esb.component.video.util.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.Date;

@Slf4j
@Service
public class AssemblyAcceptServiceImpl
        extends ServiceImpl<AssemblyAcceptMapper, AssemblyAccept>
        implements IAssemblyAcceptService {

    @Autowired
    private IAssemblyParamInfoService assemblyparaminfoService;

    public void updateRepContentVideoFileRecords(RepContentData sourceRepContentData, AssemblyAccept assemblyaccept) {
        RepContentData targetRepContentData = getRepContent(assemblyaccept);

        // 增量
        if (sourceRepContentData.getVideo_video_filerecords() != null
                && targetRepContentData.getVideo_video_filerecords() != null) {
            targetRepContentData.getVideo_video_filerecords()
                    .addAll(sourceRepContentData.getVideo_video_filerecords());
        }

        // 直接添加
        if (sourceRepContentData.getVideo_video_filerecords() != null
                && targetRepContentData.getVideo_video_filerecords() == null) {
            targetRepContentData.setVideo_video_filerecords(
                    sourceRepContentData.getVideo_video_filerecords());
        }

        // 增量
        if (sourceRepContentData.getVideo_picture_filerecords() != null
                && targetRepContentData.getVideo_picture_filerecords() != null) {
            targetRepContentData.getVideo_picture_filerecords()
                    .addAll(sourceRepContentData.getVideo_picture_filerecords());
        }

        // 直接添加
        if (sourceRepContentData.getVideo_picture_filerecords() != null
                && targetRepContentData.getVideo_picture_filerecords() == null) {
            targetRepContentData.setVideo_picture_filerecords(
                    sourceRepContentData.getVideo_picture_filerecords());
        }

        updateRepContent(targetRepContentData, assemblyaccept);
    }

    public void updateRepContentVideoComment(RepContentData sourceRepContentData, AssemblyAccept assemblyaccept) {
        RepContentData targetRepContentData = getRepContent(assemblyaccept);
        targetRepContentData.setVideo_comment(sourceRepContentData.getVideo_comment());
        updateRepContent(targetRepContentData, assemblyaccept);
    }

    public void updateRepContentPaper(RepContentData sourceRepContentData, AssemblyAccept assemblyaccept) {
        RepContentData targetRepContentData = getRepContent(assemblyaccept);
        targetRepContentData.setRisk_caculate(sourceRepContentData.getRisk_caculate());
        updateRepContent(targetRepContentData, assemblyaccept);
    }

    public void updateRepContentAgreement(RepContentData sourceRepContentData, AssemblyAccept assemblyaccept) {
        RepContentData targetRepContentData = getRepContent(assemblyaccept);
        targetRepContentData.setAgreement_signids(sourceRepContentData.getAgreement_signids());
        updateRepContent(targetRepContentData, assemblyaccept);
    }

    public RepContentData getRepContent(AssemblyAccept assemblyaccept) {
        RepContentData repContentData = null;

        if (StringUtils.isNotBlank(assemblyaccept.getRep_content())) {
            repContentData = JSON.parseObject(assemblyaccept.getRep_content()
                    , RepContentData.class);
        } else {
            repContentData = new RepContentData();
        }

        return repContentData;
    }

    public RepContentData getRepContent(String request_id) {
        AssemblyAccept assemblyaccept = this.validRequestId(request_id);

        RepContentData repContentData = null;

        if (StringUtils.isNotBlank(assemblyaccept.getRep_content())) {
            repContentData = JSON.parseObject(assemblyaccept.getRep_content()
                    , RepContentData.class);
        } else {
            repContentData = new RepContentData();
        }


        return repContentData;
    }

    public void updateRepContent(RepContentData repContentData, AssemblyAccept assemblyaccept) {
        assemblyaccept.setRep_content(JSON.toJSONString(repContentData));
        assemblyaccept.setModify_datetime(new Date());
        this.baseMapper.updateById(assemblyaccept);
    }

    /**
     * 业务参数上传保存，可以进行增量保存
     * @param request
     */
    @Override
    public UploadBizParamResponse uploadBizParam(UploadBizParamRequest request) {
        UploadBizParamResponse uploadBizParamResponse = new UploadBizParamResponse();

        String jsonString = JSON.toJSONString(request);

        if (StringUtils.isBlank(jsonString) || "{}".equals(jsonString)) {
            throw new BizException(ErrorCode.ERR_PARAM_IN_ERROR, "业务数据不能为空");
        }

        if (StringUtils.isBlank(request.getRequest_id())) {
            // 新增
            AssemblyAccept assemblyaccept = assemblyparaminfoService.insert(jsonString);
            uploadBizParamResponse.setRequest_id(assemblyaccept.getRequest_id());
        } else {

            // 检查requestId
            this.validRequestId(request.getRequest_id());

            // 修改
            assemblyparaminfoService.update(request.getRequest_id(), jsonString);
            uploadBizParamResponse.setRequest_id(request.getRequest_id());
        }

        return uploadBizParamResponse;
    }

    @Override
    public UploadBizParamResponse uploadUpdateBizParam(UploadBizParamRequest request) {
        Assert.notNull(request, "请求参数对象不能为空");
        Assert.hasText(request.getRequest_id(), "请求参数不能为空");

        String jsonString = JSON.toJSONString(request);
        if (StringUtils.isBlank(jsonString) || "{}".equals(jsonString)) {
            throw new BizException(ErrorCode.ERR_PARAM_IN_ERROR, "业务数据不能为空");
        }

        // 检查requestId
        this.validRequestId(request.getRequest_id());

        // 修改
        UploadBizParamResponse uploadBizParamResponse = new UploadBizParamResponse();
        assemblyparaminfoService.update(request.getRequest_id(), jsonString);
        uploadBizParamResponse.setRequest_id(request.getRequest_id());

        return uploadBizParamResponse;
    }

    /**
     * 获取上传的业务参数
     * @param requestId
     * @return
     */
    @Override
    public UploadBizParamRequest getBizParam(String requestId) {
        // 检查requestId
        this.validRequestId(requestId);
        return getBizParamData(requestId);
    }

    private UploadBizParamRequest getBizParamData(String requestId) {

        // 1. 通过request_id查询业务参数
        String fullBizContent
                = assemblyparaminfoService
                .queryFullBizContent(requestId);

        // 2. 反序列化 bizContent
        UploadBizParamRequest bizParam =
                JSON.parseObject(fullBizContent, UploadBizParamRequest.class);

        log.info("bizParam数据: {}", fullBizContent);
        return bizParam;
    }

    /**
     * 获取上传的业务参数
     * @param assemblyaccept
     * @return
     */
    @Override
    public UploadBizParamRequest getBizParam(AssemblyAccept assemblyaccept) {
        return getBizParamData(assemblyaccept.getRequest_id());
    }

    /**
     * 获取上传业务参数中的base_data字段
     * @param requestId
     * @return
     */
    @Override
    public BizParamBaseData getBizParamBaseData(String requestId) {
        UploadBizParamRequest bizParam = getBizParam(requestId);
        if (null != bizParam.getBase_data()) {
            return bizParam.getBase_data().toJavaObject(BizParamBaseData.class);
        } else {
            return null;
        }
    }

    @Override
    public BizParamBaseData getBizParamBaseData(UploadBizParamRequest bizParam) {
        if (null != bizParam.getBase_data()) {
            return bizParam.getBase_data().toJavaObject(BizParamBaseData.class);
        } else {
            return null;
        }
    }

    @Override
    public BizExParamData getBizExParamData(String requestId) {
        UploadBizParamRequest bizParam = getBizParam(requestId);
        if (null != bizParam.getEx_param_data()) {
            return bizParam.getEx_param_data().toJavaObject(BizExParamData.class);
        } else {
            return null;
        }
    }

    @Override
    public BizExParamData getBizExParamData(UploadBizParamRequest bizParam) {
        if (null != bizParam.getEx_param_data()) {
            return bizParam.getEx_param_data().toJavaObject(BizExParamData.class);
        } else {
            return null;
        }
    }

    @Override
    public String getBizRenderData(String requestId) {
        UploadBizParamRequest bizParam = getBizParam(requestId);
        return JSONUtil.getJSONString(bizParam.getRender_data());
    }

    @Override
    public String getBizRenderData(UploadBizParamRequest bizParam) {
        return JSONUtil.getJSONString(bizParam.getRender_data());
    }

    /**
     * 检查数据是否存在
     * @param requestId
     */
    public AssemblyAccept validRequestId(String requestId) {
        if (StringUtils.isBlank(requestId)) {
            throw new BizException(ErrorCode.ERR_PARAM_IN_NOT_EMPTY, "[request_id]不能为空!");
        }

        AssemblyAccept assemblyaccept = this.baseMapper.selectById(requestId);
        if (assemblyaccept == null) {
            throw new BizException("[request_id]数据不存在!");
        }

        return assemblyaccept;
    }

}
