package com.cairh.cpe.esb.component.video.business.param.vender.zego;

import cn.hutool.core.lang.Assert;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.core.autoconfiure.env.CompositePropertySources;
import com.cairh.cpe.esb.component.video.constant.VideoConfigConstant;
import com.cairh.cpe.esb.component.video.constant.VideoConstant;
import com.cairh.cpe.esb.component.video.constant.VideoPropertyConstant;
import com.cairh.cpe.esb.component.video.util.ZegouUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.LayeredConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContexts;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.net.ssl.SSLContext;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * zego service impl
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ZegoServiceImpl implements IZegoService {

    private CloseableHttpClient httpCLient4ZegoH5Cloud;

    @Autowired
    private CompositePropertySources compositePropertySources;

    public ZegoServiceImpl() {
        httpCLient4ZegoH5Cloud = HttpClients.createDefault();
    }

    @Override
    public ZegoToken getH5PrivateCloudToken(String user_id, Role role) {
        Assert.notBlank(user_id, "getH5PrivateCloudToken user_id can not be blank");
        Assert.notNull(role, "getH5PrivateCloudToken role can not be null");

        int app_id = Integer.parseInt(compositePropertySources.getProperty(VideoPropertyConstant.ZEGO_H5_APPID, "0"));
        String tokenWay = compositePropertySources.getProperty(VideoConfigConstant.CONFIG_VIDEO_ZEGO_H5_PRIVATE_CLOUD_TOKEN_WAY, "0");
        if (StringUtils.equals("0", tokenWay)) {
            String tokenUrl = compositePropertySources.getProperty(VideoConfigConstant.CONFIG_VIDEO_ZEGO_H5_PRIVATE_CLOUD_TOKEN_URL);
            if (app_id <= 0) {
                throw new BizException("getH5PrivateCloudToken config.video.zego.h5.private.cloud.app.id can not be blank");
            }
            if (StringUtils.isBlank(tokenUrl)) {
                throw new BizException("getH5PrivateCloudToken config.video.zego.h5.private.cloud.token.url can not be blank");
            }

            //获取token
            Map<String, Object> params = new HashMap<>();
            params.put("seq", 1);
            params.put("timestamp", System.currentTimeMillis() / 1000);
            params.put(VideoConstant.APP_ID, app_id);
            params.put(VideoConstant.USER_ID, user_id);
            params.put(VideoConstant.USER_NAME, user_id);
            setRoleParams(params, role);
            params.put("net_type", 2);
            params.put("device_id", user_id);
            try {
        		/*byte[] bytes = httpCLient4ZegoH5Cloud.httpPost(
        				tokenUrl, JSON.toJSONString(params), ContentType.APPLICATION_JSON, null);
        		String json = new String(bytes, StandardCharsets.UTF_8);
        		return JSON.parseObject(json, ZegoToken.class);*/
                return null;
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                throw new BizException("getH5PrivateCloudToken error");
            }
        } else {
            String appSign = compositePropertySources.getProperty(VideoConfigConstant.CONFIG_VIDEO_ZEGO_H5_PRIVATE_CLOUD_appSign);
            long time = new Date().getTime() / 1000 + 30 * 60;
            String token_expire = time + "";
            String token = ZegouUtil.getZeGouToken(app_id + "", appSign, user_id, token_expire);
            ZegoToken zegoToken = new ZegoToken();
            zegoToken.setLogin_token(token);
            zegoToken.setToken_expire(token_expire);
            return zegoToken;
        }
    }

    /**
     * 根据角色设置参数
     *
     * @param params 参数
     * @param role   视频角色
     */
    private void setRoleParams(Map<String, Object> params, Role role) {
        if (Role.USER.equals(role)) {
            params.put(VideoConstant.QUEUE_ROLE, 10);
            params.put(VideoConstant.ROOM_ROLE, 0);
        } else if (Role.OPERATOR.equals(role)) {
            params.put(VideoConstant.QUEUE_ROLE, 1);
            params.put(VideoConstant.ROOM_ROLE, 1);
        }
    }

    private Registry<ConnectionSocketFactory> createRegistry() {
        RegistryBuilder<ConnectionSocketFactory> builder = RegistryBuilder.create();
        builder.register("http", PlainConnectionSocketFactory.INSTANCE);
        SSLContext sslContext = SSLContexts.createDefault();
        LayeredConnectionSocketFactory sslSF = new SSLConnectionSocketFactory(sslContext);
        builder.register("https", sslSF);
        return builder.build();
    }
}
