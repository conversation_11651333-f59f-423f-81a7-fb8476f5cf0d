package com.cairh.cpe.esb.component.video.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.cairh.cpe.component.common.data.entity.AssemblyAccept;
import com.cairh.cpe.component.common.model.*;

public interface IAssemblyAcceptService extends IService<AssemblyAccept> {
    void updateRepContentVideoFileRecords(RepContentData sourceRepContentData, AssemblyAccept assemblyaccept);

    void updateRepContentVideoComment(RepContentData sourceRepContentData, AssemblyAccept assemblyaccept);

    void updateRepContentPaper(RepContentData sourceRepContentData, AssemblyAccept assemblyaccept);

    void updateRepContentAgreement(RepContentData sourceRepContentData, AssemblyAccept assemblyaccept);

    RepContentData getRepContent(AssemblyAccept assemblyaccept);

    RepContentData getRepContent(String request_id);

    UploadBizParamResponse uploadBizParam(UploadBizParamRequest request);

    UploadBizParamResponse uploadUpdateBizParam(UploadBizParamRequest request);

    UploadBizParamRequest getBizParam(String requestId);

    UploadBizParamRequest getBizParam(AssemblyAccept assemblyaccept);

    BizParamBaseData getBizParamBaseData(String requestId);

    BizParamBaseData getBizParamBaseData(UploadBizParamRequest bizParam);

    BizExParamData getBizExParamData(String requestId);

    BizExParamData getBizExParamData(UploadBizParamRequest bizParam);

    String getBizRenderData(String requestId);

    String getBizRenderData(UploadBizParamRequest bizParam);

    AssemblyAccept validRequestId(String requestId);
}
