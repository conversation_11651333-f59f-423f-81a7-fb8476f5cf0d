package com.cairh.cpe.esb.component.video.core;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ReflectUtil;
import com.alibaba.spring.util.ClassUtils;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.transaction.annotation.Transactional;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 视频集默认实现, 统一定义了相关操作, 它的派生只需要定义视频集的唯一标识前缀
 *
 * <AUTHOR>
 */
@Slf4j
public class AbstractGenericVideoGroup<T extends Comparable> implements IVideoGroup<T> {

    //视频集的唯一标识前缀
    protected String videoGroupPrefix;

    //视频元素类型
    private final Class<?> videoType;

    @Autowired
    private RedisTemplate<Object, Object> redisTemplate;

    public AbstractGenericVideoGroup(String videoGroupPrefix) {
        this.videoGroupPrefix = videoGroupPrefix;
        this.videoType = ClassUtils.resolveGenericType(getClass());
    }

    @Transactional
    @Override
    public void insert(String id, T video) {
        Map<String, Object> videoMap = BeanUtil.beanToMap(video);
        try {
            redisTemplate.opsForHash().putAll(id, videoMap);
            redisTemplate.opsForSet().add(videoGroupPrefix, StringUtils.removeStart(id, videoGroupPrefix));
        } catch (Exception e) {
            log.error("video group insert error, id: " + id, e);
            throw e;
        }
    }

    @Transactional
    @Override
    public void update(String id, Map<String, Object> info) {
        if (exist(id)) {
            try {
                redisTemplate.opsForHash().putAll(id, info);
            } catch (Exception e) {
                log.error("video group update error, id: " + id, e);
                throw e;
            }
        }
    }

    @Override
    public void update(String id, String key, Object value) {
        redisTemplate.opsForHash().put(id, key, value);
    }

    @Override
    public T query(String id) {
        Map<Object, Object> videoMap = redisTemplate.opsForHash().entries(id);

        if (CollectionUtil.isNotEmpty(videoMap)) {
            val video = (T) ReflectUtil.newInstanceIfPossible(videoType);
            return BeanUtil.fillBeanWithMap(videoMap, video, CopyOptions.create().setIgnoreError(false));
        }

        return null;
    }

    @Override
    public List<T> query(List<String> ids) {
        Assert.notEmpty(ids, "video group query ids must not be empty");

        return ids.stream().map(this::query).collect(Collectors.toList());
    }

    @Override
    public List<T> queryAll() {
        Map<String, T> users = queryAllMap();
        return CollectionUtil.isNotEmpty(users) ? CollectionUtil.newArrayList(users.values()) : null;
    }

    @Override
    public Map<String, T> queryAllMap() {
        Set<Object> videoKeySet = redisTemplate.opsForSet().members(videoGroupPrefix);

        if (CollectionUtil.isNotEmpty(videoKeySet)) {
            List<String> videoKeys = videoKeySet.stream().map(String::valueOf).collect(Collectors.toList());

            Map<String, T> result = new LinkedHashMap<>();
            videoKeys.forEach(key -> result.put(key, (T) getClass().cast(AopContext.currentProxy()).query(key)));
            return result;
        }

        return null;
    }

    @Override
    public boolean exist(String id) {
        return BooleanUtil.isTrue(redisTemplate.hasKey(id));
    }

    @Transactional
    @Override
    public void remove(String id) {
        try {
            redisTemplate.delete(id);
            redisTemplate.opsForSet().remove(videoGroupPrefix, StringUtils.removeStart(id, videoGroupPrefix));
        } catch (Exception e) {
            log.error("video group remove error, id: " + id, e);
            throw e;
        }
    }

    @Transactional
    @Override
    public void remove(List<String> ids) {
        try {
            redisTemplate.delete(ids);
            redisTemplate.opsForSet().remove(videoGroupPrefix, (Object[]) ids.stream().map(id -> StringUtils.removeStart(id, videoGroupPrefix)).toArray(String[]::new));
        } catch (Exception e) {
            log.error("video group remove list error, ids: " + ids, e);
            throw e;
        }
    }
}
