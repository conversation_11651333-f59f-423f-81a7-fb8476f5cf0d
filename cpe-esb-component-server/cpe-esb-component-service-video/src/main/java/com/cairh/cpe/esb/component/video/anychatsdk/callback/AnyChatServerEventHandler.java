package com.cairh.cpe.esb.component.video.anychatsdk.callback;

import com.cairh.cpe.component.common.data.entity.ServiceQualityMonitor;
import com.cairh.cpe.component.common.data.service.IServiceQualityMonitorService;
import com.cairh.cpe.component.common.model.VideoUser;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.esb.component.elect.IEsbComponentElectDubboService;
import com.cairh.cpe.esb.component.elect.dto.req.ElectDownloadFileRequest;
import com.cairh.cpe.esb.component.elect.dto.req.ElectUploadFileByUriRequest;
import com.cairh.cpe.esb.component.elect.dto.req.ElectUploadFileRequest;
import com.cairh.cpe.esb.component.elect.dto.resp.ElectUploadFileByUriResponse;
import com.cairh.cpe.esb.component.elect.dto.resp.ElectUploadFileResponse;
import com.cairh.cpe.esb.component.video.anychatsdk.core.AbstractAnyChatServerSDK;
import com.cairh.cpe.esb.component.video.anychatsdk.core.AnyChatServerEventHandlerAdapter;
import com.cairh.cpe.esb.component.video.anychatsdk.v6_3.AnyChatServerSDK;
import com.cairh.cpe.esb.component.video.core.IVideoGroup;
import com.cairh.cpe.esb.component.video.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.Arrays;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.*;

@Service
@Slf4j
public class AnyChatServerEventHandler extends AnyChatServerEventHandlerAdapter {
    private static final ThreadPoolExecutor executor = new ThreadPoolExecutor(2, 5, 5000, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(500), new ThreadPoolExecutor.CallerRunsPolicy());

    @Autowired
    private IServiceQualityMonitorService serviceQualityMonitorService;
    @DubboReference(check = false)
    private IEsbComponentElectDubboService electDubboService;
    @Autowired
    private RedisTemplate<String,String> redisTemplate;
    @Autowired
    private IVideoGroup<VideoUser> videoGroup;
    @Override
    public void OnAnyChatUserLeaveRoomActionCallBack(int dwUserId, int dwRoomId) {
        log.info("2.anychat服务端录制结束" );
        log.info("用户房间列表移除用户，用户id：[" + dwUserId + "]，房间号：[" + dwRoomId + "]");
        String roomId = StringUtils.defaultString(redisTemplate.boundValueOps("videoStreamEmpId" +dwUserId
                ).get(), "");
        log.info("--->>> roomVideoStreamEmpId：【{}】，获取缓存数据：【{}】", "videoStreamEmpId" + dwUserId, roomId);
        if (StringUtils.isNotBlank(roomId) && StringUtils.equals(roomId, String.valueOf(dwRoomId))) {
            //坐席人离开房间
            try {
                anychat.StreamRecordCtrlEx(dwUserId, 0, AnyChatServerSDK.ANYCHAT_RECORD_FLAGS_LOCALCB, dwRoomId, "", 0);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        } else {
            //未获取缓存数据roomId，可能是沙箱数据
            if (dwUserId >= 20_000_000 && dwUserId < 30_000_000) {
                //坐席人进入房间
                try {
                    Integer emp_id = Integer.valueOf(dwUserId) - 20_000_000;
                    String user_id = String.valueOf(dwRoomId - 10_000_000);
                    anychat.StreamRecordCtrlEx(dwUserId, 0, AnyChatServerSDK.ANYCHAT_RECORD_FLAGS_LOCALCB, dwRoomId, "", 0);
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
    }

    /**
     * 视频全局录制回调
     * @param dwUserId
     * @param szRecordFileName
     * @param dwElapse
     * @param dwFlags
     * @param dwParam
     * @param lpUserStr
     * @param dwRecordServerId
     */
    @Override
    public void OnAnyChatServerRecordExCallBack(int dwUserId, final String szRecordFileName, final int dwElapse, int dwFlags, final int dwParam, final String lpUserStr, int dwRecordServerId) {
        super.OnAnyChatServerRecordExCallBack(dwUserId, szRecordFileName, dwElapse,
                dwFlags, dwParam, lpUserStr, dwRecordServerId);
        log.info("3.anychat服务端录制结束 --文件下载回调" );
        log.info("监控视频文件路径={},dwUserId={}", szRecordFileName, dwUserId);
        final File file = new File(szRecordFileName);
        if (!file.exists()) {
            log.error("监控视频文件路径下没有对应的文件file={}", file);
            return;
        }
        executor.execute(() -> {
            try {
                String[] userinfo = lpUserStr.split(",");
                if (userinfo.length != 2 || StringUtils.isBlank(userinfo[0]) || StringUtils.isBlank(userinfo[0])) {
                    log.error("userinfo信息错误，userinfo={}", Arrays.asList(userinfo));
                    return;
                }
                String user_id = userinfo[0];
                String begin_date = userinfo[1];
                Date beginDate = DateUtil.parse(begin_date, DateUtil.DATE_TIME_FORMAT);
                Date endDate = DateUtil.addSeconds(beginDate, dwElapse);

                // 用户信息查询 查询redis缓存
                VideoUser videoUser = videoGroup.query(user_id);
                if (Objects.isNull(videoUser)) {
                    log.error("QueueUser不存在，user_id={}", user_id);
                    return;
                }
                String file_name = file.getName();
                String extension = getExtention(file_name);
                if (!".mp4".equals(extension)) {
                    log.error("文件扩展名不是mp4，extension={}", extension);
                    return;
                }
                // 服务质量文件 下载
                ElectUploadFileByUriRequest request = new ElectUploadFileByUriRequest();
                request.setFile_path(szRecordFileName);
                ElectUploadFileByUriResponse electUploadFileByUriResponse = electDubboService.electUploadFileByUri(request);

                //服务质量流水对象
                ServiceQualityMonitor entity = new ServiceQualityMonitor();
                entity.setFilerecord_id(electUploadFileByUriResponse.getFilerecord_id());
                entity.setUnique_id(videoUser.getUnique_id().replaceFirst("(?<=^|\\D)"+videoUser.getSubsys_no() , ""));
                entity.setSubsys_no(Integer.valueOf(videoUser.getSubsys_no()));
                entity.setCreate_datetime(new Date());
                entity.setBegin_datetime(beginDate);
                entity.setEnd_datetime(endDate);

                BaseUser baseUser = new BaseUser();
                baseUser.setUser_name(StringUtils.isNotBlank(videoUser.getOperator_name())?videoUser.getOperator_name(): "");
                baseUser.setStaff_no(StringUtils.isNotBlank(videoUser.getOperator_no())?videoUser.getOperator_no():"");

                serviceQualityMonitorService.insert(baseUser,entity);
                //清理  缓存信息   roomId+unique_id  videoStreamEmpId+dwUserId
                redisTemplate.delete("roomVideoStreamEmpId" + dwUserId);
                redisTemplate.delete("roomId" + lpUserStr);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        });
    }


    /**
     * 获取文件扩展名，如果没有扩展名，则返回.un
     * @param fileName
     * @return 如： .jpg
     */
    public static String getExtention(String fileName) {
        if (fileName == null) {
            log.error("文件名为空");
            return null;
        }
        String str = ".un";
        int pos = fileName.lastIndexOf(".");
        if (pos > -1) {
            str = fileName.substring(pos);
        }
        return str;
    }
    /**
     * 触发全局录制
     * @param dwUserId 操作员id
     * @param lpBuf
     * @param dwLen
     * @param wParam
     * @param lParam
     * @param dwTaskId
     */
    @Override
    public void OnAnyChatTransBufferEx(int dwUserId, byte[] lpBuf, int dwLen, int wParam, int lParam, int dwTaskId) {
        super.OnAnyChatTransBufferEx(dwUserId, lpBuf, dwLen, wParam, lParam, dwTaskId);
        log.info("1.anychat服务端录制开始" );
        // 解析传入的房间号
        int dwRoomId;
        String input = new String(lpBuf);
        try {
            double number = Double.parseDouble(input);
            dwRoomId = (int) number;
            log.info("解析传入的房间号dwRoomId={}", dwRoomId);
        } catch (Exception e) {
            log.error("解析传入的房间号异常 input={}", input);
            return;
        }
        try {
            log.info("从管道消息中收到的dwRoomId={}，启动质检视频录制", dwRoomId);
            anychat.SetSDKOptionInt(AnyChatServerSDK.BRAS_SO_RECORD_FILETYPE, 0);
            // 查询用户缓存信息 
            String unique_id = redisTemplate.boundValueOps("roomId" + dwUserId).get();
            String lpUserStr = unique_id + "," + DateUtil.getCurrentTime(DateUtil.DATE_TIME_FORMAT);
            anychat.StreamRecordCtrlEx(dwUserId, 1, AnyChatServerSDK.ANYCHAT_RECORD_FLAGS_VIDEO + AnyChatServerSDK.ANYCHAT_RECORD_FLAGS_AUDIO +
                    AnyChatServerSDK.ANYCHAT_RECORD_FLAGS_SERVER + AnyChatServerSDK.ANYCHAT_RECORD_FLAGS_MIXAUDIO + AnyChatServerSDK.ANYCHAT_RECORD_FLAGS_MIXVIDEO +
                    AnyChatServerSDK.ANYCHAT_RECORD_FLAGS_ABREAST + AnyChatServerSDK.ANYCHAT_RECORD_FLAGS_STEREO, dwRoomId, lpUserStr, 0);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    @Override
    public void OnAnyChatTransFile(int dwUserId, String szFileName, String szTempFilePath,
                                   int dwFileLength, int wParam, int lParam, int dwTaskId) {
//        super.OnAnyChatTransFile(dwUserId, szFileName, szTempFilePath,
//                dwFileLength, wParam, lParam, dwTaskId);
//        String empRoomId = StringUtils.defaultString(RedisClientUtil.get("videoStreamEmpId" + dwUserId), "");
//        log.info("--->>> roomVideoStreamEmpId：【{}】，获取缓存数据：【{}】", "videoStreamEmpId" + dwUserId, empRoomId);
//        String userRoomId = StringUtils.defaultString(RedisClientUtil.get("videoStreamUserId" + dwUserId), "");
//        log.info("--->>> roomVideoStreamUserId：【{}】，获取缓存数据：【{}】", "videoStreamUserId" + dwUserId, userRoomId);
//
//        if (StringUtils.isBlank(empRoomId) || StringUtils.isBlank(userRoomId)) {
//            //东北证券确认没有标准版视频，不做如下校验
//            String securityAlias = PropertiesUtils.get("security.alias");
//            if (!"dbsecu".equals(securityAlias) && !"nesc".equals(securityAlias) && !"jhzq".equals(securityAlias) && !"htzq".equals(securityAlias) && !"xbzq".equals(securityAlias)){
//                //未获取缓存数据empRoomId和userRoomId，可能是沙箱数据
//                if (dwUserId >= 20_000_000) {
//                    // 校验是否为企业版坐席
//                    int operator_no = dwUserId - 20_000_000;
//                    Operatorinfo operatorinfo =
//                            operatorinfoService.getOperatorInfo(String.valueOf(operator_no));
//                    if (operatorinfo == null) {
//                        log.info("标准版视频，不进行文件上传操作[operator_no={}]", operator_no);
//                        return;
//                    }
//                } else if (dwUserId >= 0) {
//                    // 校验是否为企业版用户
//                    int user_id = dwUserId - 10_000_000;
//                    Users user = usersService.selectUsersById(String.valueOf(user_id));
//                    if (user == null) {
//                        log.info("未查询到[user_id={}]的用户，判定为标准版视频，不进行文件上传操作", user_id);
//                        return;
//                    }
//                } else {
//                    log.error("错误的用户ID：" + dwUserId);
//                    return;
//                }
//            }
//        } else {
//            if (StringUtils.isNotBlank(empRoomId)) {
//                // 校验是否为企业版坐席
//                int operator_no = dwUserId - 20_000_000;
//                Operatorinfo operatorinfo =
//                        operatorinfoService.getOperatorInfo(String.valueOf(operator_no));
//                if (operatorinfo == null) {
//                    log.info("标准版视频，不进行文件上传操作[operator_no={}]", operator_no);
//                    return;
//                }
//            } else if (StringUtils.isNotBlank(userRoomId)) {
//                String anychat_new_connect_flag = StringUtils.defaultString(RedisClientUtil.get("roomId" + userRoomId), "");
//                log.info("--->>> 根据roomId：【{}】，获取缓存数据：【{}】", "roomId" + userRoomId, anychat_new_connect_flag);
//                String user_id = null;
//                if (StringUtils.equals(anychat_new_connect_flag, anychat_new_connect_flag)) {
//                    user_id = String.valueOf(dwUserId - 30_000_000);
//                } else {
//                    user_id = String.valueOf(dwUserId - 10_000_000);
//                }
//                // 校验是否为企业版用户
//                Users user = usersService.selectUsersById(user_id);
//                if (user == null) {
//                    log.info("未查询到[user_id={}]的用户，判定为标准版视频，不进行文件上传操作", user_id);
//                    return;
//                }
//            } else {
//                log.error("错误的用户ID：" + dwUserId);
//                return;
//            }
//        }
//
//        // 组装返回结果
//        Map<String, String> map = new HashMap<>();
//        map.put(Fields.FILE_PATH, szTempFilePath);
//        map.put(Fields.TYPE, VideoConstant.ANYCHAT_MESSAGE_TYPE);
//        map.put(Fields.TASK_ID, String.valueOf(dwTaskId));
//        map.put("wParam", String.valueOf(wParam));
//        map.put("szFileName", szFileName);
//        String json = JSON.toJSONString(map);
//        byte[] bytes = json.getBytes();
//
//        if (init_flag) {
//            // 直连anychat，直接发消息
//            anychat.SendBufToUser(dwUserId, bytes, bytes.length);
//            return;
//        }
//        // anychat共享模式，通过redis发送消息
//        JSONArray array = new JSONArray();
//        array.add(dwUserId);
//        array.add(new String(bytes));
//        array.add(bytes.length);
//        JSONObject jsonObject = new JSONObject();
//        jsonObject.put("method_name", "SendBufToUser");
//        jsonObject.put("arguments", array);
//        jsonObject.put("trans_byte", "[1]");
//        getAnyChatRedisClient().publish(
//                VideoConstant.CHANNEL_ANYCHAT_INVOKE, jsonObject.toJSONString());

    }

//    private AnyChatRedisClient getAnyChatRedisClient() {
//        if (anyChatRedisClient != null) {
//            return anyChatRedisClient;
//        }
//        anyChatRedisClient = SpringContext.getBean(AnyChatRedisClient.class);
//        return anyChatRedisClient;
//    }

    /**
     * 获取AnyChatSDK对象
     *
     * @return 返回null，如果开启了
     */
    public AbstractAnyChatServerSDK getAnyChatSDK() {
        if (init_flag) {
            return anychat;
        }
        return null;
    }

    @Override
    protected String getAnyChatVersion() {
        return "V6_3";
    }

}
