package com.cairh.cpe.esb.component.video.business.vender;

import cn.hutool.core.lang.Assert;
import com.cairh.cpe.component.common.model.VideoUser;
import com.cairh.cpe.esb.component.video.business.IVideoVenderService;
import com.cairh.cpe.esb.component.video.constant.VideoVender;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 视频厂商
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class VideoVenderServiceImpl implements IVideoVenderService {


    @Override
    public VideoVender getVender(VideoUser user) {
        Assert.notNull(user, "can not get vender of user that not exist");
        String vender = user.getService_vender();

        if (StringUtils.isBlank(vender)) {
            log.warn("no vender found, default set to anychat");
            return VideoVender.ANYCHAT;
        }

        return doGetVender(convertH5IfNeed(vender));
    }

    @Override
    public VideoVender getDefaultVender() {
        return getVender(new VideoUser());
    }

    /**
     * 厂商H5转换
     *
     * @param vender 视频厂商
     * @return H5厂商
     */
    private String convertH5IfNeed(String vender) {
        vender = StringUtils.toRootLowerCase(vender);

        if (StringUtils.equals(vender, VideoVender.ZEGO.value)) {
            vender = VideoVender.ZEGO_H5_PRIVATE_CLOUD.value;
        }
        if (StringUtils.equals(vender, VideoVender.TENCENT.value)) {
            vender = VideoVender.TENCENT_H5.value;
        }
        if (StringUtils.equals(vender, VideoVender.ZT_ZEGO.value)) {
            vender = VideoVender.ZHENGTONG.value;
        }
        return vender;
    }

    /**
     * 获取视频厂商
     *
     * @param vender 视频厂商(已经过H5转换)
     * @return {@link VideoVender}
     */
    private VideoVender doGetVender(String vender) {
        return EnumUtils.getEnum(VideoVender.class, StringUtils.toRootUpperCase(vender), VideoVender.ANYCHAT);
    }
}
