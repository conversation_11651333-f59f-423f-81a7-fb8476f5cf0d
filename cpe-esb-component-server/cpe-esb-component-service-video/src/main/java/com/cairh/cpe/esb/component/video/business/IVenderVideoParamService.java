package com.cairh.cpe.esb.component.video.business;


import com.cairh.cpe.component.common.model.VideoOperator;
import com.cairh.cpe.component.common.model.VideoUser;
import com.cairh.cpe.esb.component.video.dto.resp.StartRecordResponse;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * 厂商-视频联通参数
 *
 * <AUTHOR>
 */
public interface IVenderVideoParamService {

    /**
     * 获取通用视频联通参数(config)
     *
     * @param user {@link VideoUser}
     * @return 通用视频联通参数, non null
     */
    Map<String, Object> getCommonVideoParam(VideoUser user,boolean isToken);

    /**
     * 获取特定视频联通参数(roomId,roomPwd)
     *
     * @param user {@link VideoUser}
     * @return 特定视频联通参数, non null
     */
    Map<String, Object> getSpecialVideoParam(VideoUser user,boolean isToken);

    /**
     * 获取缓存视频联通参数
     *
     * @param user {@link VideoUser}
     * @return 视频联通参数
     */
    Map<String, Object> getCachedVideoParam(VideoUser user);

    /**
     * 开始录制
     *
     * @param videoOperator 操作员
     * @param videoUser    队列用户
     * @return 返回开始录制结果
     */
    @NotNull
    StartRecordResponse startRecord(VideoOperator videoOperator,VideoUser videoUser,String waterMark);

    /**
     * 结束录制
     *
     * @param videoOperator 操作员
     * @param videoUser    队列用户
     */
    void stopRecord(VideoOperator videoOperator,VideoUser videoUser);
}
