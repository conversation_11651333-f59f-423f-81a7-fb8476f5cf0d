package com.cairh.cpe.esb.component.video.business.factory.impl;

import com.cairh.cpe.esb.component.video.constant.VideoVender;
import com.cairh.cpe.esb.component.video.business.IVenderVideoParamService;
import com.cairh.cpe.esb.component.video.business.factory.BaseParamServiceFactory;
import com.cairh.cpe.esb.component.video.business.param.vender.anychat.AnychatVideoParamService;
import com.cairh.cpe.esb.component.video.business.param.vender.zego.ZegoVideoParamService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.beans.Introspector;

/**
 * 视频联通参数服务工厂
 *
 * <AUTHOR>
 */
@Component
public class VideoParamServiceFactory extends BaseParamServiceFactory<IVenderVideoParamService> {

    @Autowired
    private ApplicationContext applicationContext;

    @Override
    public IVenderVideoParamService getService(VideoVender vender) {
        String service = convertVenderToService(vender);

        return applicationContext.getBean(service, IVenderVideoParamService.class);
    }

    /**
     * 视频厂商 -> 服务提供商
     *
     * @param vender {@link VideoVender}
     * @return 服务提供商
     */
    private String convertVenderToService(VideoVender vender) {
        Class<? extends IVenderVideoParamService> provider;

        switch (vender) {
            case ANYCHAT:
                provider = AnychatVideoParamService.class;
                break;
            case ZEGO:
            case ZHENGTONG:
            case ZEGO_H5_PRIVATE_CLOUD:
                provider = ZegoVideoParamService.class;
                break;
            //more vender

            default:
                provider = AnychatVideoParamService.class;
                break;
        }

        return Introspector.decapitalize(provider.getSimpleName());
    }
}
