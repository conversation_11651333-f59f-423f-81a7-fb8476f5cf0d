package com.cairh.cpe.esb.component.video.queue;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.cairh.cpe.component.common.model.VideoUser;
import com.cairh.cpe.esb.component.video.core.IVideoGroup;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.SmartInitializingSingleton;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.beans.Introspector;
import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 视频队列
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Component
public class VideoQueue implements IQueue, SmartInitializingSingleton {

    public static final String VIDEO_QUEUE_PREFIX = "video_queue";

    private String name;

    private Integer level;

    private static RedisTemplate<Object, Object> redisTemplate;

    @Autowired
    private  IVideoGroup<VideoUser> videoGroup;

    private static QueueGroup queueGroup;

    public VideoQueue(Integer level) {
        this.name = VIDEO_QUEUE_PREFIX + level;
        this.level = level;
    }

    @JsonIgnore
    @Override
    public Set<String> getAllUserId() {
        Set<Object> userIdSet = redisTemplate.opsForZSet().range(name, 0L, -1L);

        if (CollectionUtil.isNotEmpty(userIdSet)) {
            Set<String> result = new LinkedHashSet<>();

            userIdSet.forEach(userId -> result.add(String.valueOf(userId)));
            return result;
        }

        return null;
    }

    @JsonIgnore
    @Override
    public List<VideoUser> getAllUser() {
        Set<String> userIdSet = getAllUserId();

        return videoGroup.query(CollectionUtil.newArrayList(userIdSet));
    }

    @Override
    public void enqueue(VideoUser user) {
        redisTemplate.opsForZSet().add(name, user.getUnique_id(), user.getScore());

        user.setQueue_name(name);
    }

    @Override
    public boolean dequeue(String user_id) {
        IQueue queue;

        do {
            queue = queueGroup.exist(user_id);

            if (Objects.nonNull(queue)) {
                Long count = redisTemplate.opsForZSet().remove(queue.getName(), user_id);
                if (Objects.nonNull(count) && count > 0L) {
                    return true;
                }
            }
        } while (Objects.nonNull(queue));

        return false;
    }

    @Override
    public long size() {
        Long size = redisTemplate.opsForZSet().size(name);

        return Objects.nonNull(size) ? size : 0L;
    }

    @Override
    public long position(String user_id) {
        List<String> allUserId = new ArrayList<>(getAllUserId());

        return CollectionUtil.indexOf(allUserId, id -> StringUtils.endsWith(user_id, id)) + 1;
    }

    @Override
    public void afterSingletonsInstantiated() {
        redisTemplate = SpringUtil.getBean(Introspector.decapitalize(RedisTemplate.class.getSimpleName()));
//        videoGroup = SpringUtil.getBean(IVideoGroup.class);
        queueGroup = SpringUtil.getBean(QueueGroup.class);
    }
}
