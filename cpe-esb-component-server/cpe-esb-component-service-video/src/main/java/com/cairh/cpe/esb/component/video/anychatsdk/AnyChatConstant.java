package com.cairh.cpe.esb.component.video.anychatsdk;

/**
 * 功能说明: 静态常量<br>
 * 系统版本: v1.0<br>
 * 开发人员: <AUTHOR>
 * 开发时间: 2015年8月5日<br>
 * 涉及到与硬件交互，包名不能修改
 */
public class AnyChatConstant {

	public static final int LOGIN_IN_STATE = 0;// 登陆是标记状态
	public static final int LOGIN_OUT_STATE = 1;// 正常退出时标记状态

	public static final int LEVEL_USER = 1;// 用户等级
	public static final int LEVEL_EMP = 0;// 客服等级

	public static final String USER_NET_ERROR = "用户网络异常中断！";
	public static final String EMP_NET_ERROR = "客服网络异常中断！";

	public static final String REVIDEO_STRING = "revideo";
	public static final String VERIFY_STRING = "verify";

	public static final String VERIFY_TYPE = "1";// 视频采集
	public static final String REVIDEO_TYPE = "4";// 视频重采

	public static final String TRANS_EXIT = "exit";// 传送退出标志
	public static final String TRANS_ID = "emp";// 传送用户客服ID
	public static final String TRANS_VIDEO_TYPE = "type";// 传送采集类型（验证还是重采）
	public static final String TRANS_RESULT = "result";// 传送验证结果
	public static final String TRANS_STARTTIME = "start";// 传送视频录像开始时间
	public static final String TRANS_ENDTIME = "end";// 传送视频录像结束时间
	public static final String TRANS_OPERATORID = "opera";// 传送操作员ID

	// 视频验证状态
	public static final long VIDEO_VERIFY_STATUS_SAVE = 1L;
	public static final long VIDEO_VERIFY_STATUS_SUCC = 8L;
	public static final long VIDEO_VERIFY_STATUS_FAIL = 9L;

	// 视频验证结果
	public static final String VIDEO_VERIFY_STATUS_NOT_VERIFY_STRING = "没有验证";
	public static final String VIDEO_VERIFY_STATUS_SUCC_STRING = "验证成功";
	public static final String VIDEO_VERIFY_STATUS_FAIL_STRING = "验证失败";

}
