package com.cairh.cpe.esb.component.video.business;

import com.cairh.cpe.component.common.model.VideoUser;
import com.cairh.cpe.esb.component.video.constant.VideoParamType;

import java.util.Map;

/**
 * 视频联通参数
 *
 * <AUTHOR>
 */
public interface IVideoParamService {

    /**
     * 获取视频联通参数
     *
     * @param user {@link VideoUser}
     * @param type 视频参数类型
     * @return 视频联通参数
     */
    Map<String, Object> getParam(VideoUser user, VideoParamType type);
}
