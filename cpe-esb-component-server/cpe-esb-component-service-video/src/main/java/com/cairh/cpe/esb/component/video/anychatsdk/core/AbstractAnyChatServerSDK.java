package com.cairh.cpe.esb.component.video.anychatsdk.core;

import com.cairh.cpe.esb.component.video.anychatsdk.AnyChatOutParam;
import com.cairh.cpe.esb.component.video.anychatsdk.AnyChatVerifyUserOutParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * AnyChatServerSDK的abstract版，与具体sdk的版本解耦，各个具体版本的sdk类均继承此类
 */
public abstract class AbstractAnyChatServerSDK {

	private static Logger logger = LoggerFactory.getLogger(AbstractAnyChatServerSDK.class);

	// 查询SDK版本信息、编译时间等
	public abstract String GetSDKVersion();

	// 获取用户的详细信息
	public abstract String GetUserInfo(int dwUserId, int dwInfoId);

	// 初始化SDK
	public abstract int InitSDK(int flags);

	// 注册消息通知
	public abstract int RegisterNotify();

	// 注册用户身份验证回调类（部署到Web容器中需要调用）
	public abstract int RegisterVerifyUserClass(AnyChatVerifyUserOutParam ParamObject);

	// 释放资源
	public abstract int Release();

	// 向指定房间发送数据
	public abstract int SendBufToRoom(int roomid, byte[] buf, int len);

	// 向指定用户发送数据
	public abstract int SendBufToUser(int userid, byte[] buf, int len);

	// 发送SDK Filter 通信数据
	public abstract int SendSDKFilterData(byte[] buf, int len);

	// SDK内核参数设置（整型）
	public abstract int SetSDKOptionInt(int optname, int dwValue);

	// SDK内核参数设置（字符串）
	public abstract int SetSDKOptionString(int optname, String lpStrValue);

	// 设置事件回调通知接口
	public abstract void SetServerEvent(AnyChatServerEventHandlerAdapter e);

	// 设置SDK定时器回调函数（dwElapse：定时器间隔，单位：ms）
	public abstract int SetTimerEventCallBack(int elapse);

	// 设置用户的详细信息
	public abstract int SetUserInfo(int dwUserId, int dwInfoId, String lpInfoValue, int dwFlags);

	// 中心端录像控制
	public abstract int StreamRecordCtrl(int dwUserId, int bStartRecord, int dwFlags, int dwParam, int dwRecordServerId);

	// 中心端录像控制（扩展）
	public abstract int StreamRecordCtrlEx(int dwUserId, int bStartRecord, int dwFlags, int dwParam, String lpUserStr, int dwRecordServerId);

	// 透明通道传送缓冲区
	public abstract int TransBuffer(int userid, byte[] buf, int len);

	// 发送透明通道数据给录像服务器
	public abstract int TransBuffer2RecordServer(int dwUserId, byte[] buf, int len, int dwParam, int dwRecordServerId);

	// 透明通道传送缓冲区扩展
	public abstract int TransBufferEx(int userid, byte[] buf, int len, int wparam, int lparam, int flags, AnyChatOutParam outParam);

	// 传送文件
	public abstract int TransFile(int userid, String filepath, int wparam, int lparam, int flags, AnyChatOutParam outParam);

	// 用户信息控制
	public abstract int UserInfoControl(int dwUserId, int dwCtrlCode, int wParam, int lParam, String lpStrValue);

	// 视频呼叫事件控制（请求、回复、挂断等）
	public abstract int VideoCallControl(int dwEventType, int dwUserId, int dwErrorCode, int dwFlags, int dwParam, String lpUserStr);

}
