package com.cairh.cpe.esb.component.video.core.enhance;

import cn.hutool.core.lang.Assert;
import com.cairh.cpe.esb.component.video.constant.VideoConfigConstant;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 视频联通参数缓存标志补充
 *
 * <AUTHOR>
 */
@Aspect
@Component
public class CachedVideoParamAspect {

    @SuppressWarnings("unchecked")
    @Around("execution(public * com.cairh.cpe.esb.component.video.business.IVenderVideoParamService.getCachedVideoParam(..))")
    public Object videoParamCachedSignInject(ProceedingJoinPoint joinPoint) throws Throwable {
        Map<String, Object> result = (Map<String, Object>) joinPoint.proceed();
        Assert.notNull(result, "videoParamCachedSignInject null result found");

        result.put(VideoConfigConstant.VIDEO_PARAM_FROM_CACHE, true);
        return result;
    }
}
