package com.cairh.cpe.esb.component.video.service.dubbo;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.URLUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cairh.cpe.component.common.constant.Constant;
import com.cairh.cpe.component.common.constant.ErrorConstant;
import com.cairh.cpe.component.common.data.entity.*;
import com.cairh.cpe.component.common.data.service.*;
import com.cairh.cpe.component.common.model.UploadBizParamRequest;
import com.cairh.cpe.component.common.model.VideoOperator;
import com.cairh.cpe.component.common.model.VideoUser;
import com.cairh.cpe.component.common.model.VideoUserStatus;
import com.cairh.cpe.component.common.service.IRedisService;
import com.cairh.cpe.component.common.utils.MessageUtil;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.context.constant.ErrorCode;
import com.cairh.cpe.context.expression.ExpressionParser;
import com.cairh.cpe.core.autoconfiure.env.CompositePropertySources;
import com.cairh.cpe.esb.base.rpc.IVBaseAllBranchDubboService;
import com.cairh.cpe.esb.base.rpc.IVBaseUserInfoDubboService;
import com.cairh.cpe.esb.base.rpc.dto.req.VBaseAllBranchQryRequest;
import com.cairh.cpe.esb.base.rpc.dto.req.VBaseQryUserInfosRequest;
import com.cairh.cpe.esb.base.rpc.dto.req.VBaseUserInfoQryRequest;
import com.cairh.cpe.esb.base.rpc.dto.resp.VBaseAllBranchQryResponse;
import com.cairh.cpe.esb.base.rpc.dto.resp.VBaseQryUserInfosResponse;
import com.cairh.cpe.esb.base.rpc.dto.resp.VBaseUserInfoQryResponse;
import com.cairh.cpe.esb.component.core.util.BaseBeanUtil;
import com.cairh.cpe.esb.component.core.util.CompositePropertySourcesUtil;
import com.cairh.cpe.esb.component.elect.IEsbComponentElectDubboService;
import com.cairh.cpe.esb.component.elect.dto.req.ElectUploadFileByUriRequest;
import com.cairh.cpe.esb.component.elect.dto.resp.ElectUploadFileByUriResponse;
import com.cairh.cpe.esb.component.video.IEsbComponentVideoDubboService;
import com.cairh.cpe.esb.component.video.business.IVenderVideoParamService;
import com.cairh.cpe.esb.component.video.business.IVideoParamService;
import com.cairh.cpe.esb.component.video.business.IVideoVenderService;
import com.cairh.cpe.esb.component.video.business.constant.BusinessFlagConstant;
import com.cairh.cpe.esb.component.video.business.factory.impl.VideoParamServiceFactory;
import com.cairh.cpe.esb.component.video.constant.VideoConstant;
import com.cairh.cpe.esb.component.video.constant.VideoParamType;
import com.cairh.cpe.esb.component.video.constant.VideoPropertyConstant;
import com.cairh.cpe.esb.component.video.constant.VideoVender;
import com.cairh.cpe.esb.component.video.core.IVideoGroup;
import com.cairh.cpe.esb.component.video.dto.req.*;
import com.cairh.cpe.esb.component.video.dto.resp.*;
import com.cairh.cpe.esb.component.video.dto.resp.support.VideowordsConfig;
import com.cairh.cpe.esb.component.video.dto.resp.support.VideowordsModel;
import com.cairh.cpe.esb.component.video.queue.IQueue;
import com.cairh.cpe.esb.component.video.queue.QueueGroup;
import com.cairh.cpe.esb.component.video.service.IAssemblyAcceptService;
import com.cairh.cpe.esb.component.video.service.IVideoRecordBuilder;
import com.cairh.cpe.esb.component.video.service.impl.VideoRecordBuilder;
import com.cairh.cpe.esb.dispatch.IEsbDispatchDubboService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.jetbrains.annotations.Nullable;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.support.atomic.RedisAtomicLong;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 视频服务(dubbo)
 *
 * <AUTHOR>
 */
@Slf4j
@DubboService
public class EsbComponentVideoDubboServiceImpl implements IEsbComponentVideoDubboService {
    private static final String VIDEO_AUTO_ASSIGN = "config.video.autoAssign.flag";
    public static final String VIDEO_USER_EXIT_QUEUE = "cpe.websocket.notice.redis.channel";
    public static final String VIDEO_USER_JOIN_QUEUE = "cpe.websocket.notice.redis.channel";
    public static final String VIDEO_QUEUE_USER_WAIT_POSITION = "video_queue_user_wait_position";
    public static final String VIDEO_PRIORITY_LOCK = "video_priority_lock";

    @Autowired
    private CompositePropertySources compositePropertySources;

    @Autowired
    private IVideoGroup<VideoUser> videoGroup;
    @Autowired
    private IVideoGroup<VideoOperator> VideoOperatorGroup;

    @Autowired
    private RedisAtomicLong scoreAutoIncrementer;

    @Autowired
    private QueueGroup queueGroup;

    @Autowired
    private IVideoParamService videoParamService;

    @Autowired
    private IVideoRecordBuilder videoRecordBuilder;

    @Autowired
    private IVideoRecordService videoRecordService;

    @Autowired
    private IVideoWordsModelService videowordsmodelService;

    @Autowired
    private IVideoWordsBusinessService videowordsbusinessService;

    @Autowired
    private ExpressionParser expressionParser;

    @Autowired
    private IVideoWordsConfigService videowordsconfigService;

    @Autowired
    private IVideoVerityService videoVerityService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private IAssemblyAcceptService assemblyacceptService;

    @Autowired
    private IVideoVenderService videoVenderService;

    @DubboReference(check = false, lazy = true)
    private IVBaseUserInfoDubboService baseUserInfoDubboService;

    @DubboReference(check = false, lazy = true)
    private IEsbDispatchDubboService esbDispatchDubboService;

    @Autowired
    private VideoRecordBuilder videorecordBuilder;
    @Autowired
    private IRedisService redisService;
    @Autowired
    private VideoParamServiceFactory videoParamServiceFactory;

    @Autowired
    private IVideoPriorityConfigService videoPriorityConfigService;

    @DubboReference(check = false)
    private IVBaseUserInfoDubboService userInfoDubboService;
    @DubboReference(check = false)
    private IEsbComponentElectDubboService componentElectDubboService;
    @DubboReference(check = false)
    private IVBaseAllBranchDubboService branchDubboService;

    @Autowired
    private RedisTemplate<Object, Object> redisTemplate;

    @Resource
    private RedissonClient redissonClient;

    /**
     * 队列优先设置
     *
     * @param request
     * @return
     */
    @Override
    public boolean handleVideoPriority(VideoQryQueueInfoRequest request) {

        RLock lock = redissonClient.getLock(VIDEO_PRIORITY_LOCK);
        // 尝试加锁，可以设置等待时间和锁自动过期时间
        // 尝试加锁，最多等待1秒，锁自动失效时间为0.5秒
        try {
            boolean flag = false;
            boolean isLocked = lock.tryLock(1000, 200, TimeUnit.MILLISECONDS);
            if (!isLocked) {
                return flag;
            }
            if (StringUtils.isBlank(request.getUnique_id())) {
                return flag;
            }
            // 查询用户 队列名称
            VideoUser videoUser = videoGroup.query(request.getUnique_id());
            if (Objects.isNull(videoUser)) {
                return flag;
            }
            String uniqueId = videoUser.getUnique_id();
            //判断 当前用户时候还在队列中 再队列中才可以操作
            IQueue oldQueue = queueGroup.exist(uniqueId);
            if (Objects.isNull(oldQueue)) {
                throw new BizException(ErrorConstant.VIDEO_USER_OUT_QUEUE_ERROR_NO, "该视频见证用户已不在排队队列中");
            }

            VideoUser user = videoGroup.query(uniqueId);
            if (StringUtils.equals(user.getStatus(), VideoConstant.STATUS_0_WAITING)) {
                log.info("视频最高优先队列, user_id: {}", uniqueId);
            } else {
                throw new BizException(ErrorConstant.VIDEO_USER_OUT_QUEUE_ERROR_NO, "该视频见证用户已不在排队队列中");
            }

            //先出队
            boolean dequeue = oldQueue.dequeue(uniqueId);
            //获取创建 最高优先级队列 （有就就使用，没有就创建）  0 设置为最高优先级队列
            IQueue queue = queueGroup.getQueue(0);
            double value = scoreAutoIncrementer.incrementAndGet() + 0.0D;
            videoUser.setScore(value);
            Map<String, Object> map = new HashMap<>();
            map.put("queue_name", queue.getName());
            map.put("video_level", 0);
            map.put("priority_flag", 1);
            map.put("score", value);

            user.setScore(value);
            queue.enqueue(user);
            flag = true;

            videoGroup.update(uniqueId, map);
            //重新计算 所有用户 排队位置 及排队总数
            updateQueueInfo();
            return flag;
        } catch (InterruptedException e) {
            e.printStackTrace();
        } finally {
            // 无论是否获取到锁，最终都要尝试释放锁
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return false;
    }

    @Override
    public void joinQueue(VideoJoinQueueRequest request) {
        VideoUser user = new VideoUser();
        // 判断排队总人数 是否大于设定值（500） 提示语  comp.video.wait.queue.num
        List<IQueue> queues = queueGroup.getAllQueue();
        if (queues != null) {
            long total = queues.stream().mapToLong(IQueue::size).sum();
            if (total >= Long.parseLong(compositePropertySources.getProperty(VideoPropertyConstant.COMP_VIDEO_WAIT_QUEUE_NUM, "500"))) {
                throw new BizException(ErrorConstant.VIDEO_MAX_QUEUE_ERROR_NO, "排队人数已满，请稍后再试");
            }
        }

        // 属性继承
        BeanUtil.copyProperties(request, user, false);


        // user_id
        user.setUnique_id(user.getSubsys_no() + user.getUnique_id());

        // queue_level
        Integer video_level = user.getVideo_level();
        if (Objects.isNull(video_level)) {
            user.setVideo_level(VideoConstant.DEFAULT_VIDEO_LEVEL);
        }

        // score
        VideoUser localUser = videoGroup.query(user.getUnique_id());
        // 是否启用视频用户score记忆
        if (StringUtils.equals("1", compositePropertySources.getProperty(VideoPropertyConstant.USER_SCORE_PRESERVE, "0"))
                && Objects.nonNull(localUser) && Objects.nonNull(localUser.getScore()) && localUser.getScore() > 0.0D) {
            user.setScore(localUser.getScore());
            log.info("加入队列 用户：{}，历史分数：{}", user.getUnique_id(), localUser.getScore());
        } else {
            user.setScore(scoreAutoIncrementer.incrementAndGet() + 0.0D);
        }

        // 设置最近一次分配的坐席
        if (Objects.nonNull(localUser)) {
            user.setLast_assigned_emp(StringUtils.defaultIfBlank(localUser.getOperator_no(), localUser.getLast_assigned_emp()));

        }
        setUserLevel(user);
        // 判断是否 优先置顶过
        if (Objects.nonNull(localUser) && StringUtils.equals("1", localUser.getPriority_flag().toString())) {
            log.info("视频优先置顶过： {}，{}", user.getUnique_id(), localUser.getPriority_flag());
            user.setPriority_flag(localUser.getPriority_flag());
            if (Objects.nonNull(localUser.getVideo_level())) {
                user.setVideo_level(localUser.getVideo_level());
            }
        }
        // 进入队列
        IQueue queue = queueGroup.getQueue(user.getVideo_level());
        if (Objects.isNull(queue)) {
            throw new BizException(ErrorConstant.VIDEO_QUEUE_INEXISTENCE_ERROR_NO, com.cairh.cpe.component.common.utils.StringUtils.format("video user: {} no queue found", user.getUnique_id()));
        }
        //用户加入对列
        queue.enqueue(user);
        log.info("video user: {} enqueue {} success", user.getUnique_id(), queue.getName());
        long enqueue_time = System.currentTimeMillis();
        // 入队时间
        user.setEnqueue_time(enqueue_time);
        //最近状态更新时间
        user.setLast_status_update_time(enqueue_time);
        // 视频用户排队中
        user.setStatus(VideoConstant.STATUS_0_WAITING);
        log.info("用户视频加入队列：{}", user.getVideo_level());
        // 当前位置
        determinePosition(user);
        // determinePageAddress(user);

        videoGroup.insert(user.getUnique_id(), user);
        //删除排队显示信息 删除排队缓存
        delCacheViewData(user.getUnique_id());

        videoRecordService.save(videoRecordBuilder.joinQueueBuild(user));
        user.setNotice_type("refresh_video_user_list");
        stringRedisTemplate.convertAndSend(VIDEO_USER_JOIN_QUEUE, JSON.toJSONString(user));
    }

    private void setUserLevel(VideoUser user) {
        // 新增队列优先级 判断 获取设置的优先级配置 计算出最佳优先级 subsys_no（子系统编号）busin_type(业务类型)app_id(接入方式)id_kind（证件类型）
        // businflow_no()流程编号
        LambdaQueryWrapper<VideoPriorityConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(VideoPriorityConfig::getEnable_flag, "1");
        List<VideoPriorityConfig> videoPriorityConfigList = videoPriorityConfigService.list(queryWrapper);
        List<Integer> levelList = new ArrayList<>();
        videoPriorityConfigList.stream().forEach(v -> {
            String fieldName = v.getField_name();
            Integer fieldPriority = v.getField_priority();
            String fieldValue = v.getField_value();
            List<String> fieldValueList = new ArrayList<>();
            try {
                String[] split = fieldValue.split(",");
                fieldValueList = Arrays.asList(split);
            } catch (Exception e) {

            }
            Map<String, Object> stringObjectMap = BeanUtil.beanToMap(user);
            if (fieldValueList.contains(stringObjectMap.get(fieldName).toString()) && com.cairh.cpe.component.common.utils.StringUtils.isNotBlank(fieldValue)) {
                // 当匹配字段一致时 开始给定默认值-》 字段优先级
                levelList.add(fieldPriority);
            }
        });
        if (CollectionUtil.isNotEmpty(levelList)) {
            log.info("用户视频队列序号集合: {} ,{}", user.getUnique_id(), levelList);
            Collections.sort(levelList);
            user.setVideo_level(levelList.get(0));
            log.info("用户视频队列序号: {} ,{}", user.getUnique_id(), user.getVideo_level());
        }
    }

    @Override
    public VideoQryQueueInfoResponse qryQueueInfo(VideoQryQueueInfoRequest request) {
        // 定位到视频用户
        String user_id = request.getSubsys_no() + request.getUnique_id();
        VideoUser user = videoGroup.query(user_id);
        if (Objects.isNull(user)) {
            throw new BizException(ErrorConstant.VIDEO_USER_INEXISTENCE_ERROR_NO, com.cairh.cpe.component.common.utils.StringUtils.format("qryQueueInfo user not exist, user_id: " + user_id));
        }
        // 保活
        long value = System.currentTimeMillis() + 1000;
        videoGroup.update(user_id, VideoConstant.LAST_UPDATE_STATUS_TIME, value);

        VideoUser query = videoGroup.query(user_id);
        // todo 测试提出不要该流水
//        videoRecordService.save(videoRecordBuilder.getConnInfoBuild(user));
        return determineQueueInfo(user);
    }

    @Override
    public void exitQueueInfo(VideoExitQueueInfoRequest request) {
        String user_id = request.getSubsys_no() + request.getUnique_id();

        VideoUser user;
        String assignedEmp;
        if (!videoGroup.exist(user_id)) {
            throw new BizException(ErrorConstant.VIDEO_USER_INEXISTENCE_ERROR_NO, "视频用户不存在");
        } else {
            user = videoGroup.query(user_id);
            assignedEmp = user.getAssignedEmp();
            if (StringUtils.equals(user.getStatus(), VideoConstant.STATUS_1_MATCHED)) {
                log.warn("exitQueueInfo when video user was matched, user_id: {}", user_id);
                Map<String, Object> map = new HashMap<>();
                map.put(VideoUser.AUTO_ASSIGN_TIME, "");
                map.put(VideoUser.ASSIGNED_EMP, "");
                videoGroup.update(user.getUnique_id(), map);
            }
        }

        IQueue queue = queueGroup.getQueue(user.getVideo_level());

        //判断视频用户是否属于排队等待
        String status = user.getStatus();
        boolean flag = StringUtils.equals(status, VideoConstant.STATUS_0_WAITING);

        if (queue.dequeue(user_id)) {
            // 用户主动退出队列记录日志 仅作为用户退出，视频流水记录使用
            user.setStatus(VideoConstant.STATUS_4_VIDEOED);
            videoRecordService.save(videoRecordBuilder.updateStatusBuild(user));
            log.info("exitQueueInfo success, user_id: {}", user_id);
        } else {
            log.error("exitQueueInfo failed, user_id: {}", user_id);
        }
        //删除该用户队列缓存
        redisTemplate.opsForSet().remove("{cpe_esb_video}video_user", user.getUnique_id());
        // 修改 用户信息状态 不能是0与1
        //更新用户缓存信息
        Map<String, Object> updateMap = new HashMap<>();
        long currTime = redisService.getMillisecond();
        // 当用户退出时，没办法判断 该用户是异常中断还是 视频见证完毕，设置状态为空，在坐席操作视频见证通过时 会再次写入状态 ；
        updateMap.put(VideoUser.STATUS, "");
        videoGroup.update(user_id, updateMap);
        //删除排队显示信息 删除排队缓存
        delCacheViewData(user.getUnique_id());

        user.setNotice_type("refresh_video_user_list");
        stringRedisTemplate.convertAndSend(VIDEO_USER_EXIT_QUEUE, JSON.toJSONString(user));
        // 发消息通知前端
        if (StringUtils.isNotBlank(assignedEmp)) {
            com.alibaba.fastjson2.JSONObject jsonObject = new com.alibaba.fastjson2.JSONObject();
            jsonObject.put("notice_type", "video_user_exit");
            jsonObject.put("operator_no", assignedEmp);
            jsonObject.put("user_name", user.getUser_name());
            stringRedisTemplate.convertAndSend(VIDEO_USER_EXIT_QUEUE, jsonObject.toJSONString());
        }
        //更新其他用户的排队信息
        updateQueueInfo();

    }

    /**
     * 删除视频显示位置缓存
     *
     * @param unique_id
     */
    private void delCacheViewData(String unique_id) {
        String videoNum = VIDEO_QUEUE_USER_WAIT_POSITION + unique_id;
        if (stringRedisTemplate.hasKey(videoNum)) {
            stringRedisTemplate.delete(videoNum);
        }
    }

    private void updateQueueInfo() {
        // 退出时 更新其他用户 排队信息
        try {
            List<VideoUser> videoUsers = videoGroup.queryAll();
            videoUsers.stream().forEach(u -> {
                // 更新用户实时排队位子
                determinePosition(u);
                Map<String, Object> info = new HashMap<>(4);
                info.put("queue_position", u.getQueue_position());
                info.put("queue_count", u.getQueue_count());
                videoGroup.update(u.getUnique_id(), info);
            });
        } catch (Exception e) {
            log.info("用户视频退出 更新视频队列{cpe_esb_video}video_user fail");
        }
    }

    @Override
    public void disruptionUserVideo(VideoOperatorExitQueueInfoRequest request) {
        String user_id = request.getSubsys_no() + request.getUnique_id();

        VideoUser user = videoGroup.query(user_id);
        if (Objects.nonNull(user)) {
            //处理视频队列 弹出该用户
            if (redisService.srem(Constant.COMPONENT_VIDEO_REDIS_VIDEO_USER_UNIFIED_PREFIX, user_id)) {

                log.info("用户视频退出 视频队列{cpe_esb_video}video_user success, user_id: {}", user_id);
            } else {
                log.error("用户视频退出 视频队列{cpe_esb_video}video_user failed, user_id: {}", user_id);
            }
            // 修改视频用户 状态
            // 更新视频状态为已完成
            Map<String, Object> map = new HashMap<>();
            map.put(VideoUser.STATUS, VideoConstant.STATUS_3_VIDEOED);
            videoGroup.update(user_id, map);
            // 清除优先级用户排队信息
            delCacheViewData(user_id);
            user.setNotice_type("refresh_video_user_list");
            stringRedisTemplate.convertAndSend(VIDEO_USER_EXIT_QUEUE, JSON.toJSONString(user));
            // 记录视频坐席中断视频
            user.setStatus(VideoConstant.STATUS_6_VIDEOED);
            videoRecordService.save(videoRecordBuilder.updateStatusBuild(user));
        }
    }

    @Override
    public void interruptVideoByUser(VideoOperatorExitQueueInfoRequest request) {
        String user_id = request.getSubsys_no() + request.getUnique_id();
        VideoUser user = videoGroup.query(user_id);
        if (Objects.nonNull(user)) {
            //处理视频队列 弹出该用户
            IQueue queue = queueGroup.getQueue(user.getVideo_level());
            if (queue.dequeue(user_id)) {
                log.info("exitQueueInfo success, user_id: {}", user_id);
            } else {
                log.error("exitQueueInfo failed, user_id: {}", user_id);
            }
            // 清除优先级用户排队信息
            delCacheViewData(user_id);
            // 记录视频用户中断视频
            user.setStatus(VideoConstant.STATUS_7_VIDEOED);
            videoRecordService.save(videoRecordBuilder.updateStatusBuild(user));
        }
    }

    @Override
    public void abnormalAnswer(VideoOperatorExitQueueInfoRequest request) {
        // 让该用户返回排队队列
        String user_id = request.getSubsys_no() + request.getUnique_id();
        VideoUser user = videoGroup.query(user_id);

        if (Objects.isNull(user)) {
            log.info("用户视频异常接听 用户{cpe_esb_video}video_user 不存在, user_id: {}", user_id);
            return;
        }
        IQueue queue = queueGroup.getQueue(user.getVideo_level());
        //用户加入对列
        queue.enqueue(user);
        // 判断该用户是否存在于 {cpe_esb_video}video_user
        if (!redisTemplate.opsForSet().isMember("{cpe_esb_video}video_user", user.getUnique_id())) {
            redisTemplate.opsForSet().add("{cpe_esb_video}video_user", user.getUnique_id());
        }
        Map<String, Object> map = new HashMap<>();
        map.put("operator_no", "");
        map.put("operator_name", "");
        map.put("room_id", "");
        map.put(VideoUser.STATUS, "0");
        videoGroup.update(user.getUnique_id(), map);
    }

    @Override
    public void answerUserVideo(VideoAnswerUserRequest request) {
        String user_id = request.getSubsys_no() + request.getUnique_id();

        String key = "answerUserVideo:" + user_id;
        // 并发控制
        Boolean setIfAbsent = stringRedisTemplate.opsForValue().setIfAbsent(key, "1", 5, TimeUnit.MINUTES);
        if (BooleanUtils.isNotTrue(setIfAbsent)) {
            log.error("该任务已被其它坐席接通 user_id={}", user_id);
            throw new BizException(ErrorConstant.VIDEO_TASK_INEXISTENCE_ERROR_NO, "该任务已被其它坐席接通");
        }

        try {
            if (!videoGroup.exist(user_id)) {
                log.error("该视频见证用户已不在队列 user_id={}", user_id);
                throw new BizException(ErrorConstant.VIDEO_USER_INEXISTENCE_ERROR_NO, "视频用户不存在");
            }

            IQueue queue = queueGroup.exist(user_id);
            if (Objects.isNull(queue)) {
                log.error("该视频见证用户已不在排队队列中 user_id={}", user_id);
                throw new BizException(ErrorConstant.VIDEO_USER_OUT_QUEUE_ERROR_NO, "该视频见证用户已不在排队队列中");
            }

            VideoUser user = videoGroup.query(user_id);
            if (StringUtils.equals(user.getStatus(), VideoConstant.STATUS_0_WAITING)) {
                log.info("answerUserVideo video user fit in with condition, user_id: {}", user_id);
            } else {
                log.error("该视频任务已不在等待队列中 user_id={}", user_id);
                throw new BizException(ErrorConstant.VIDEO_USER_OUT_QUEUE_ERROR_NO, "该视频见证用户已不在排队队列中");
            }

            if (queue.dequeue(user_id)) {
                // todo 处理用户被清理出视频队列
                user.setStatus(VideoConstant.STATUS_5_VIDEOED); // 该状态不计入视频用户只作用于视频记录流水
                videoRecordService.save(videoRecordBuilder.updateStatusBuild(user));
                log.info("answerUserVideo operator: {} answer video user: {} success", request.getOperator_no(), user_id);
                //更新 其他用户队列信息
                updateQueueInfo();
            } else {
                throw new BizException(ErrorConstant.VIDEO_TASK_ANSWER_ERROR_NO, String.format("answerUserVideo operator: %s answer video user: %s failed", request.getOperator_no(), user_id));
            }
            //更新用户缓存信息
            Map<String, Object> updateMap = new HashMap<>();
            long currTime = redisService.getMillisecond();
            //更新用户自动接受分配时间以及被分配给某个坐席
            updateMap.put(VideoUser.AUTO_ASSIGN_ACCEPT_TIME, currTime);
            updateMap.put(VideoUser.AUTO_ASSIGN_TIME, currTime);
            updateMap.put(VideoUser.ASSIGNED_EMP, request.getOperator_no());
            videoGroup.update(user_id, updateMap);

            if (StringUtils.isNotBlank(request.getOperator_no())) {
                user.setOperator_no(request.getOperator_no());
            }
            prepareEnvironment(request, user);

            videoRecordService.save(videoRecordBuilder.answerVideoBuild(user));
        } catch (BizException bizException) {
            throw bizException;
        } catch (Exception e) {
            throw new BizException(e.getMessage());
        } finally {
            stringRedisTemplate.delete(key);
        }
    }

    @Override
    public void updateUserStatus(String subsys_no, String unique_id, String status, String remote_user_id, String zego_token) {
        this.updateUserStatus(subsys_no, unique_id, status, remote_user_id, zego_token, null);
    }

    @Override
    public void updateUserStatus(String subsys_no, String unique_id, String status, String remote_user_id, String zego_token, String room_id) {
        String user_id = subsys_no + unique_id;

        Map<String, Object> info = new HashMap<>(4);

        info.put(VideoConstant.STATUS, status);
        info.put(VideoConstant.LAST_UPDATE_STATUS_TIME, System.currentTimeMillis());
        if (StringUtils.isNotBlank(remote_user_id)) {
            info.put(VideoConstant.REMOTE_USER_ID, remote_user_id);
        }
        if (StringUtils.equals(status, VideoConstant.STATUS_1_MATCHED)) {
            // 状态为已匹配时,将视频联通参数写入用户信息
            VideoUser user = videoGroup.query(user_id);
            if (Objects.isNull(user)) {
                throw new BizException(ErrorConstant.VIDEO_USER_INEXISTENCE_ERROR_NO, com.cairh.cpe.component.common.utils.StringUtils.format("qryQueueInfo user not exist, user_id: " + user_id));
            }
            if (!StringUtils.equalsAny(user.getStatus(), VideoConstant.STATUS_0_WAITING, VideoConstant.STATUS_N1_VANISH)) {
                return;
            }

            if (StringUtils.isNotBlank(room_id)) {
                user.setRoom_id(room_id);
            }
            Map<String, Object> video_param = videoParamService.getParam(user, VideoParamType.SPECIAL);
            video_param.put(VideoConstant.REMOTE_USER_ID, remote_user_id);
            info.put(VideoConstant.ROOM_ID, video_param.get(VideoConstant.ROOM_ID));
            info.put(VideoConstant.VIDEO_JSON_PARAM, JSON.toJSONString(video_param));
            //设置操作员姓名
            VBaseUserInfoQryRequest requestDTO = new VBaseUserInfoQryRequest().setStaff_no(user.getOperator_no());
            VBaseUserInfoQryResponse vBaseUserInfoQryResponse = userInfoDubboService.baseUserQryUserInfo(requestDTO);
            info.put(VideoConstant.OPERATOR_NAME, vBaseUserInfoQryResponse.getUser_name());
            info.put(VideoUser.ASSIGNED_EMP, user.getOperator_no());
        }
        if (StringUtils.equals(status, VideoConstant.STATUS_1_MATCHED)) {
            // 用户视频中 清除排队位置 排队总数
            info.put("queue_position", "");
            info.put("queue_count", "");
        }
        if (StringUtils.equals(status, VideoConstant.STATUS_2_VIDEOING)) {
            //删除排队显示信息 删除排队缓存
            delCacheViewData(user_id);
        }
        log.info("视频用户信息更新请求入参：{}", info.get(VideoUser.ASSIGNED_EMP));
        videoGroup.update(user_id, info);

        VideoUser user = videoGroup.query(user_id);
        if (Objects.isNull(user)) {
            throw new BizException(ErrorConstant.VIDEO_USER_INEXISTENCE_ERROR_NO, com.cairh.cpe.component.common.utils.StringUtils.format("updateUserStatus error as user not exist, user_id: " + user_id));
        }
        log.info("视频状态变更：{}，{}", user.getUnique_id(), user.getStatus());
        videoRecordService.save(videoRecordBuilder.updateStatusBuild(user));
    }

    @Override
    public void videoVerityRecord(String subsys_no, String unique_id, String status, String remark, String filerecord_id) {
        String user_id = subsys_no + unique_id;
        VideoUser user = videoGroup.query(user_id);
        if (user != null) {
            VideoRecord videoRecord = new VideoRecord();
            BeanUtil.copyProperties(user, videoRecord, false);
            videoRecord.setFull_name(user.getUser_name());
            videoRecord.setUnique_id(StringUtils.removeStart(user.getUnique_id(), user.getSubsys_no()));
            videoRecord.setCreate_datetime(new Date());
            videoRecord.setFilerecord_id(filerecord_id);
            VideoVender vender = videoVenderService.getVender(user);
            videoRecord.setFactory_name(vender.name);
            videoRecord.setBusin_flag(status);
            videoRecord.setReject_info(remark);
            videoRecord.setRemark(StringUtils.equals("8", status) ? "见证通过" : "见证不通过");
            user.setStatus(StringUtils.equals("8", status) ? BusinessFlagConstant.USER_VIDEOED_PASS : BusinessFlagConstant.USER_VIDEOED_NO_PASS);
            videoRecord.setInterval_sec(videorecordBuilder.dealInterValSec(user));
            videoRecord.setTohis_flag(VideoConstant.NOT_TOHIS); //temporary ignore
            if (StringUtils.isNotBlank(user.getOperator_no())) {
                VBaseUserInfoQryResponse vBaseUserInfoQryResponse = baseUserInfoDubboService.baseUserQryUserInfo(new VBaseUserInfoQryRequest().setStaff_no(user.getOperator_no()));
                if (vBaseUserInfoQryResponse != null) {
                    videoRecord.setOp_branch_no(vBaseUserInfoQryResponse.getBranch_no());
                    videoRecord.setOperator_name(vBaseUserInfoQryResponse.getUser_name());
                }
            }
            videoRecordService.save(videoRecord);
        }
    }

    @Override
    public QueryVideowordsRes compvideoQueryVideowords(QueryVideowordsReq request) {
        log.info("视频话术查询dubbo接口请求入参：{}", JSON.toJSONString(request));
        if (request.getBusin_type() == null || StringUtils.isEmpty(request.getVideo_type())
                || request.getSubsys_no() == null) {
            throw new BizException(ErrorCode.ERR_PARAM_IN_NOT_EMPTY, "请求参数不能有空");
        }
        QueryVideowordsReq businessRequest = new QueryVideowordsReq();
        businessRequest.setBusin_type(request.getBusin_type());
        businessRequest.setSubsys_no(request.getSubsys_no());
        VideoWordsBusiness videowordsbusiness = BaseBeanUtil.copyProperties(businessRequest, VideoWordsBusiness.class);
        LambdaQueryWrapper<VideoWordsBusiness> queryWrapper =
                new QueryWrapper<>(videowordsbusiness).lambda().eq(VideoWordsBusiness::getStatus, "8")
                        .eq(VideoWordsBusiness::getBusin_type, request.getBusin_type());
        List<VideoWordsBusiness> videoBusinessList = videowordsbusinessService.list(queryWrapper);

        QueryVideowordsRes queryVideowordsRes = new QueryVideowordsRes();
        queryVideowordsRes.setVideomodel_resultlist(Collections.emptyList());
        queryVideowordsRes.setVideoconfig_resultlist(Collections.emptyList());
        if (CollectionUtils.isEmpty(videoBusinessList)) {
            log.info("视频话术查询dubbo接口未查询到满足条件的数据，请求入参：{}", JSON.toJSONString(request));
            return queryVideowordsRes;
        }

        // 获取规则表达式参数
        Map<String, Object> params = parse_replace_str(request.getRegular_data());
        if (MapUtils.isNotEmpty(params)) {
            // 过滤不满足规则表达式的视频话术规则配置
            videoBusinessList = videoBusinessList.stream().filter(temp -> {
                String regularExpre = temp.getRegular_expre();
                if (StringUtils.isBlank(regularExpre)) {
                    return Boolean.TRUE;
                }
                try {
                    return expressionParser.parse(params, regularExpre, Boolean.class);
                } catch (Exception e) {
                    return Boolean.FALSE;
                }
            }).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(videoBusinessList)) {
            log.info("视频话术查询dubbo接口过滤规则表达式后未查询到满足条件的数据，请求入参：{}", JSON.toJSONString(request));
            return queryVideowordsRes;
        }

        List<String> videoWordsModelIdList = videoBusinessList.stream()
                .map(VideoWordsBusiness::getVideowordsmodel_id)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(videoWordsModelIdList)) {
            log.info("视频话术查询dubbo接口过滤模板id后未查询到满足条件的数据，请求入参：{}", JSON.toJSONString(request));
            return queryVideowordsRes;
        }

        LambdaQueryWrapper<VideoWordsModel> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(VideoWordsModel::getStatus, "8");
        wrapper.eq(VideoWordsModel::getVideo_type, request.getVideo_type());
        wrapper.eq(VideoWordsModel::getBusin_type, String.valueOf(request.getBusin_type()));
        wrapper.in(VideoWordsModel::getSerial_id, videoWordsModelIdList);
        List<VideoWordsModel> videoModelList = videowordsmodelService.list(wrapper);
        queryVideowordsRes.setVideomodel_resultlist(BaseBeanUtil.copyToList(videoModelList, VideowordsModel.class));
        if (CollectionUtils.isEmpty(videoModelList)) {
            log.info("视频话术查询dubbo接口过滤视频方式后未查询到满足条件的数据，请求入参：{}", JSON.toJSONString(request));
            return queryVideowordsRes;
        }

        videoWordsModelIdList = videoModelList.stream()
                .map(VideoWordsModel::getSerial_id)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        LambdaQueryWrapper<VideoWordsConfig> videowordsconfigLambdaQueryWrapper = new LambdaQueryWrapper<>();
        videowordsconfigLambdaQueryWrapper.eq(VideoWordsConfig::getStatus, "8");
        videowordsconfigLambdaQueryWrapper.in(VideoWordsConfig::getVideowordsmodel_id, videoWordsModelIdList).orderByAsc(VideoWordsConfig::getOrder_no);
        List<VideoWordsConfig> list = videowordsconfigService.list(videowordsconfigLambdaQueryWrapper);
        queryVideowordsRes.setVideoconfig_resultlist(BaseBeanUtil.copyToList(list, VideowordsConfig.class));

        log.info("视频话术查询dubbo接口出参：{}", JSON.toJSONString(queryVideowordsRes));
        return queryVideowordsRes;
    }

    @Override
    public QueryVideowordsRes compvideoQueryVideowordsToReplaceDate(Integer subsys_no, String unique_id, Map<String, String> from, QueryVideowordsRes response) {
        List<VideowordsModel> modelList = response.getVideomodel_resultlist();

        String userId = subsys_no + unique_id;
        VideoUser user = videoGroup.query(userId);
        Map<String, Object> videoMap = BaseBeanUtil.beanToMap(user);
        log.info("获取到redis用户信息{}", videoMap);
        Map<String, String> replaceMap = new HashMap<>();
        if (videoMap != null && !videoMap.isEmpty()) {
            replaceMap = videoMap.entrySet().stream()
                    .collect(Collectors.toMap(e -> String.valueOf(e.getKey()), e -> String.valueOf(e.getValue())));
            log.info("处理后用户信息用户信息{}", replaceMap);
        }

        //将所有操作员和用户渲染数据放到一起
        Map<String, String> operatorData = from;
        if (operatorData != null) {
            replaceMap.putAll(operatorData);
        }
        for (Iterator<VideowordsModel> iterator = modelList.iterator(); iterator.hasNext(); ) {
            VideowordsModel model = iterator.next();
            //用户前台提示话术
            String client_tips_words = model.getClient_tips_words();
            String client_tips_words_render = renderData(client_tips_words, replaceMap);
            model.setClient_tips_words(client_tips_words_render);

            //发送消息话术
            String send_msg_words = model.getSend_msg_words();
            String send_msg_words_render = renderData(send_msg_words, replaceMap);
            model.setSend_msg_words(send_msg_words_render);
        }

        List<VideowordsConfig> configList = response.getVideoconfig_resultlist();
        if (CollectionUtils.isNotEmpty(configList)) {
            for (Iterator<VideowordsConfig> iterator = configList.iterator(); iterator.hasNext(); ) {
                VideowordsConfig config = iterator.next();
                //话术内容
                String words_content = config.getWords_content();
                String words_content_render = renderData(words_content, replaceMap);
                config.setWords_content(words_content_render);

                //话术白名单
                String correct_answer = config.getCorrect_answer();
                String correct_answer_render = renderData(correct_answer, replaceMap);
                config.setCorrect_answer(correct_answer_render);

                //话术黑名单
                String error_answer = config.getError_answer();
                String error_answer_render = renderData(error_answer, replaceMap);
                config.setError_answer(error_answer_render);
            }
        }
        return response;
    }

    public String renderData(String context, Map<String, String> render_data) {
        if (StringUtils.isNotBlank(context) && MapUtils.isNotEmpty(render_data)) {
            //进行渲染
            //新增 从业人员执业编号EMP_CERT
            try {
                VBaseUserInfoQryRequest request = new VBaseUserInfoQryRequest();
                request.setStaff_no(render_data.get("operator_no"));
                VBaseUserInfoQryResponse vBaseUserInfoQryResponse = baseUserInfoDubboService.baseUserQryUserInfo(request);
                render_data.put("EMP_CERT", StringUtils.defaultString(vBaseUserInfoQryResponse.getProfession_cert(), " "));
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            context = MessageUtil.getMessage(context, render_data);
        }
        // 当该数据不存在时，替换掉该字段
        context = context.replace("${EMP_CERT}", "");
        return context;
    }

    @Override
    public QueryVideoVerifyResponse queryVideoVerify(QueryVideoVerifyRequest request) {
        if (StringUtils.isBlank(request.getUnique_id())) {
            throw new BizException(ErrorCode.ERR_PARAM_IN_NOT_EMPTY, "unique_id不能为空!");
        }
        log.info("视频验证结果查询dubbo接口入参：{}", request);
        LambdaQueryWrapper<VideoVerify> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(VideoVerify::getUnique_id, request.getUnique_id());
        lambdaQueryWrapper.eq(StringUtils.isNotBlank(request.getSubsys_no()), VideoVerify::getSubsys_no, request.getSubsys_no());
        lambdaQueryWrapper.eq(StringUtils.isNotBlank(request.getBusin_type()), VideoVerify::getBusin_type, request.getBusin_type());
        lambdaQueryWrapper.orderByDesc(VideoVerify::getModify_datetime);
        List<VideoVerify> list = videoVerityService.list(lambdaQueryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        } else {
            VideoVerify searchResult = list.get(0);
            log.info("视频验证结果查询dubbo接口数据库查询结果：{}", searchResult);
            QueryVideoVerifyResponse response = BaseBeanUtil.copyProperties(searchResult, QueryVideoVerifyResponse.class);
            log.info("视频验证结果查询dubbo接口出参：{}", response);
            return response;
        }
    }

    private Map<String, Object> parse_replace_str(String replace_str) {
        Map<String, Object> params = new HashMap<>();
        if (StringUtils.isNotBlank(replace_str)) {
            try {
                replace_str = replace_str.replace("“", "\"").replace("”", "\"");
                params = JSONObject.parseObject(replace_str, Map.class);

            } catch (Exception e) {
                log.error("json格式不正确：" + e.getMessage(), e);
                Map<String, String> busin_param = new HashMap<>();
                busin_param.put("replace_str", replace_str);
                throw new BizException(ErrorCode.ERR_UNKNOWN, "json格式不正确：" + busin_param.toString(), e);
            }
        }
        return params;
    }

    /**
     * 决定位置
     *
     * @param user 视频用户
     */
    public void determinePosition(VideoUser user) {
        List<IQueue> queues = queueGroup.getAllQueue();
        if (CollectionUtil.isEmpty(queues)) {
            throw new BizException(ErrorConstant.VIDEO_ALL_QUEUE_INEXISTENCE_ERROR_NO, com.cairh.cpe.component.common.utils.StringUtils.format("can not determine position for user as no queue exist, user_id: " + user.getUnique_id()));
        }

        // 总排队用户数
        //取出每个队列的 里面的用户数
        long total = queues.stream().mapToLong(IQueue::size).sum();
//        long waitNum = queues.stream().mapToLong(IQueue::size).sum();
        user.setQueue_count(total);

        // 当前排队位置
        Assert.notNull(user.getVideo_level(), "user must have video_level when determine position, user_id: " + user.getUnique_id());
//        long previousWaitNum = queues.stream().filter(queue -> queue.getLevel() < user.getVideo_level()).mapToLong(IQueue::size).sum();

        // 获取大于 该用户 等级的队列 中人数
        List<IQueue> collect = queues.stream().filter(queue -> queue.getLevel() < user.getVideo_level()).collect(Collectors.toList());
//        log.info("视频用户 之前的队列：{},{},{}", user.getUnique_id(), collect.size(),collect.get(0).getLevel());
        long sum = collect.stream().mapToLong(IQueue::size).sum();

        IQueue queue = queueGroup.getQueue(user.getVideo_level());
        long currentWaitPostion = queue.position(user.getUnique_id());

        user.setQueue_position(sum + currentWaitPostion);
        log.info("视频用户加入排队：{},{},{}", user.getUnique_id(), user.getQueue_position(), user.getQueue_count());
    }

    /**
     * 决定位置
     *
     * @param user 视频用户
     */
    private VideoQryQueueInfoResponse determineQueueInfo(VideoUser user) {

        VideoQryQueueInfoResponse response = new VideoQryQueueInfoResponse();

        Map<String, Object> video_param = videoParamService.getParam(user, VideoParamType.COMMON);
        mergeUserJsonParam(user, video_param);
        response.setJson_params(JSON.toJSONString(video_param));
        response.setStatus(user.getStatus());
        // 视频用户对列查询 只能小不能增加数量  涉及队列优先级只能小不能大  走缓存 没有添加 （该用户视频排队中） video_queue_user_Wait_Position  再视频接通删除该字段
        String videoNum = VideoConstant.VIDEO_QUEUE_USER_WAIT_POSITION + user.getUnique_id();
        Long queuePosition = user.getQueue_position();
        log.debug("视频用户 队列位子存储:{}，{}", videoNum, queuePosition);
        if (Objects.nonNull(queuePosition) && stringRedisTemplate.hasKey(videoNum)) {
            //根据状态 判断缓存是否添加 加上用户排队状态 status = 1 为排队中
            if (StringUtils.equals("0", user.getStatus()) || StringUtils.equals("1", user.getStatus())) {
                // 获取缓存 对比数值 缓存越小 用缓存的
                long num = Long.valueOf(stringRedisTemplate.boundValueOps(videoNum).get());
                queuePosition = num < queuePosition ? num : queuePosition;
                log.debug("视频用户 队列位子存储: 缓存数据比对，{}，{}", queuePosition, num);
                if (queuePosition < num) {
                    stringRedisTemplate.boundValueOps(videoNum).set(Long.toString(queuePosition));
                }
            }
        } else {
            if (StringUtils.equals("0", user.getStatus())) {
                //创建缓存
                stringRedisTemplate.boundValueOps(videoNum).set(Long.toString(queuePosition));
                log.debug("视频用户 队列位子存储: 缓存数据新建：{}， {}", videoNum, queuePosition);
            }
        }
        response.setWaitPosition(queuePosition);
        response.setWaitNum(user.getQueue_count());

        return response;
    }

    /**
     * 合并用户jsonParam
     *
     * @param user        {@link VideoUser}
     * @param video_param 视频联通参数
     * @return 合并参数
     */
    private void mergeUserJsonParam(VideoUser user, Map<String, Object> video_param) {
        if (StringUtils.isNotBlank(user.getJson_param())) {
            Map<String, Object> json_param = JSON.parseObject(user.getJson_param(), Map.class);
            video_param.putAll(json_param);
        }
    }

    /**
     * 接听环境准备
     *
     * @param request {@link VideoAnswerUserRequest}
     */
    private void prepareEnvironment(VideoAnswerUserRequest request, VideoUser user) {
        // 视频联通参数不再由此写入
        if (StringUtils.isNotBlank(request.getOperator_no())) {
            VBaseUserInfoQryResponse vBaseUserInfoQryResponse = userInfoDubboService.baseUserQryUserInfo(new VBaseUserInfoQryRequest().setStaff_no(user.getOperator_no()));
            Map<String, Object> map = new HashMap<>(2);
            map.put(VideoConstant.OPERATOR_NO, request.getOperator_no());
            if (vBaseUserInfoQryResponse != null) {
                map.put(VideoConstant.PROFESSION_CERT, vBaseUserInfoQryResponse.getProfession_cert());
                map.put(VideoConstant.OPERATOR_NAME, vBaseUserInfoQryResponse.getUser_name());
                map.put(VideoConstant.OPERATOR_BRANCH_NO, vBaseUserInfoQryResponse.getBranch_no());
                List<VBaseAllBranchQryResponse> vBaseAllBranchQryResponses =
                        branchDubboService.baseDataQryAllBranch(new VBaseAllBranchQryRequest().setBranch_no(vBaseUserInfoQryResponse.getBranch_no()));
                if (CollectionUtils.isNotEmpty(vBaseAllBranchQryResponses)) {
                    VBaseAllBranchQryResponse vBaseAllBranchQryResponse = vBaseAllBranchQryResponses.get(0);
                    map.put(VideoConstant.OPERATOR_BRANCH_NAME, vBaseAllBranchQryResponse.getBranch_name());
                }
            }
            videoGroup.update(user.getUnique_id(), map);
            user.setOperator_no(request.getOperator_no());
        }
        log.info("answerUserVideo environment prepared finish, user_id: {}", user.getUnique_id());
    }

    @Override
    public QryBizParamResponse qryBizParamInfo(QryBizParamRequest qryBizParamRequest) {
        UploadBizParamRequest bizParam = assemblyacceptService.getBizParam(qryBizParamRequest.getRequest_id());
        if (Objects.isNull(bizParam)) {
            return null;
        }

        QryBizParamResponse qryBizParamResponse = new QryBizParamResponse();
        if (Objects.nonNull(bizParam.getBase_data())) {
            qryBizParamResponse.setBase_data(bizParam.getBase_data().toJSONString());
        }
        if (Objects.nonNull(bizParam.getRender_data())) {
            qryBizParamResponse.setRender_data(bizParam.getRender_data().toJSONString());
        }
        if (Objects.nonNull(bizParam.getRegular_data())) {
            qryBizParamResponse.setRegular_data(bizParam.getRegular_data().toJSONString());
        }
        if (Objects.nonNull(bizParam.getSign_seal_data())) {
            qryBizParamResponse.setSign_seal_data(bizParam.getSign_seal_data().toJSONString());
        }
        if (Objects.nonNull(bizParam.getEx_param_data())) {
            qryBizParamResponse.setEx_param_data(bizParam.getEx_param_data().toJSONString());
        }
        if (Objects.nonNull(bizParam.getUpdate_data())) {
            qryBizParamResponse.setUpdate_data(bizParam.getUpdate_data().toJSONString());
        }
        return qryBizParamResponse;
    }

    @Override
    public StartRecordResponse startRecord(VideoQryQueueInfoRequest request) {
        // 参数检查并查询用户信息 以及坐席信息
        // 定位到视频用户
        String user_id = request.getSubsys_no() + request.getUnique_id();
        VideoUser videoUser = videoGroup.query(user_id);
        VideoOperator videoOperator = VideoOperatorGroup.query(videoUser.getOperator_no());

        // 匹配厂商实现
        VideoVender videoVender = videoVenderService.getVender(videoUser);
        IVenderVideoParamService venderVideoParamService = videoParamServiceFactory.getService(videoVender);
        String waterMark = getWatermarkInfo(videoUser.getOperator_no());
        log.info("视频水印：{}", waterMark);
        return venderVideoParamService.startRecord(videoOperator, videoUser, waterMark);
    }

    /**
     * 获取双向视频的水印文本
     *
     * @param operator_no 当前接听的操作员编号
     * @return 返回转义后的水印。返回空字符串，如果没有配置双向视频水印
     */
    @Override
    public String getWatermarkInfo(String operator_no) {
        // 取模板
        String template = compositePropertySources.getProperty(VideoPropertyConstant.COMP_VIDEO_BOTHWAY, "");
        if (StringUtils.isBlank(template)) {
            return "";
        }
        // 取操作员信息用作转义
        VBaseUserInfoQryRequest requestDTO = new VBaseUserInfoQryRequest().setStaff_no(operator_no);
        VBaseUserInfoQryResponse vBaseUserInfoQryResponse = baseUserInfoDubboService.baseUserQryUserInfo(requestDTO);
        // 转义
        template = template.replace("{operator_name}", StringUtils.defaultString(vBaseUserInfoQryResponse.getUser_name(), ""));
        template = template.replace("{staff_no}", StringUtils.defaultString(vBaseUserInfoQryResponse.getStaff_no(), ""));
        template = template.replace("{profession_cert_label}", StringUtils.defaultString(vBaseUserInfoQryResponse.getProfession_cert(), ""));
        template = template.replace("{timestamp}", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));

        // 返回
        return template;
    }

    @Override
    public String refreshUserZegoToken(String subsys_no, String unique_id, String status, String remote_user_id, String zego_token, String room_id) {
        String user_id = subsys_no + unique_id;
        // 状态为已匹配时,将视频联通参数写入用户信息
        VideoUser user = videoGroup.query(user_id);
        if (Objects.isNull(user)) {
            throw new BizException("qryQueueInfo user not exist, user_id: " + user_id);
        }

        if (StringUtils.isNotBlank(room_id)) {
            user.setRoom_id(room_id);
        }
        Map<String, Object> video_param = videoParamService.getParam(user, VideoParamType.SPECIAL);
        return MapUtils.getString(video_param, VideoConstant.ZEGO_TOKEN);
    }

    @Override
    public void stopRecord(VideoQryQueueInfoRequest request) {
        // 参数检查并查询用户信息 以及坐席信息
        // 定位到视频用户
        String user_id = request.getSubsys_no() + request.getUnique_id();
        VideoUser videoUser = videoGroup.query(user_id);
        VideoOperator videoOperator = VideoOperatorGroup.query(videoUser.getOperator_no());

        // 匹配厂商实现
        VideoVender videoVender = videoVenderService.getVender(videoUser);
        IVenderVideoParamService venderVideoParamService = videoParamServiceFactory.getService(videoVender);

        venderVideoParamService.stopRecord(videoOperator, videoUser);
    }


    @Override
    public void callback(String jsonString) {
        log.info("即构视频厂商回调 jsonString={}", jsonString);
        JSONObject jsonObject = JSON.parseObject(jsonString);
        if (jsonObject == null || jsonObject.getJSONObject(VideoConstant.EVENT_INFO) == null) {
            return;
        }
        JSONObject header = jsonObject.getJSONObject(VideoConstant.HEADER);
        if (header == null || !header.getString(VideoConstant.CMD).contains(VideoConstant.EVENT_RECORD_REPORT)) {
            log.info("即构视频厂商回调 未录制完成");
            return;
        }
        JSONObject eventInfo = jsonObject.getJSONObject(VideoConstant.EVENT_INFO);
        String roomId = eventInfo.getString(VideoConstant.ROOM_ID);
        String mixFileSize = eventInfo.getString(VideoConstant.MIX_FILE_SIZE);
        if (StringUtils.isAnyBlank(roomId, mixFileSize)) {
            log.info("即构视频厂商回调 房间号为空 或 文件大小为空");
            return;
        }
        //设置缓存
        stringRedisTemplate.boundValueOps(VideoConstant.CALL_BACK_ROOM_CACHE + roomId).set(mixFileSize);
        stringRedisTemplate.expire(VideoConstant.CALL_BACK_ROOM_CACHE + roomId, 300, TimeUnit.SECONDS);
        log.info("即构视频录制回调成功！");
    }

    @Override
    public String serverUploadFile(String subsys_no, String unique_id, String file_path) {
        //查询用户信息
        String user_id = subsys_no + unique_id;
        VideoUser videoUser = videoGroup.query(user_id);

        //开启回调开关 通过即构回调下载
        if (com.cairh.cpe.component.common.utils.StringUtils.equals("1", compositePropertySources.getProperty(VideoPropertyConstant.COMP_VIDEO_BOTHWAY, "0"))) {
            return callbackHandle(videoUser.getRoom_id(), file_path, user_id);
        }

        // dubbo接口下载
        ElectUploadFileByUriResponse electUploadFileByUriResponse = getElectUploadFileByUriResponse(file_path);
        return electUploadFileByUriResponse.getFilerecord_id();
    }

    private ElectUploadFileByUriResponse getElectUploadFileByUriResponse(String file_path) {
        ElectUploadFileByUriRequest request = new ElectUploadFileByUriRequest();
        request.setFile_path(file_path);
        ElectUploadFileByUriResponse electUploadFileByUriResponse = componentElectDubboService.electUploadFileByUri(request);
        return electUploadFileByUriResponse;
    }

    @Override
    public Integer queryVideoUserNum(String subsys_no) {
        List<VideoUser> videoUsers = videoGroup.queryAll();
        if (CollectionUtils.isEmpty(videoUsers)) {
            return 0;
        }
        videoUsers = videoUsers.stream().filter(videoUser -> videoUser.getStatus().equals(VideoConstant.STATUS_0_WAITING)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(videoUsers)) {
            return 0;
        }
        Integer num = 0;
        if (StringUtils.equals("-1",subsys_no)) {
            num = videoUsers.size();
        }else {
            num=videoUsers.stream().filter(videoUser -> videoUser.getSubsys_no().equals(subsys_no)).collect(Collectors.toList()).size();
        }
        return num;
    }

    private String callbackHandle(String roomId, String url, String user_id) {
        // 下载
        for (int i = 0; i < 30; i++) {
            //获取redis
            String fileSize = stringRedisTemplate.boundValueOps(VideoConstant.CALL_BACK_ROOM_CACHE + roomId).get();
            //5秒内读取即构回调的redis值
            if (i < 5 && StringUtils.isBlank(fileSize)) {
                log.info("即构视频文件下载需要重试1，url={}", url);
                try {
                    Thread.sleep(1000);
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
                continue;
            }

            //大于5秒 没有读取到回调 直接根据url下载 文件大小大于10kb返回
            if (i >= 5 && i <= 28 && StringUtils.isBlank(fileSize)) {
                byte[] filebyte = getFilebyte(url);
                //创建临时文件
                File tmpFile = null;
                try {
                    tmpFile = genTempFile("video-busin-" + System.currentTimeMillis() + ".mp4");
                    cn.hutool.core.io.FileUtil.writeBytes(filebyte, tmpFile);
                } catch (Exception e) {
                    log.error("文件转File错误");
                    throw new BizException(e.getMessage());
                }
                if (tmpFile.length() > 10240) {
                    ElectUploadFileByUriResponse electUploadFileByUriResponse = getElectUploadFileByUriResponse(url);
                    return electUploadFileByUriResponse.getFilerecord_id();
                } else {
                    log.info("即构视频文件下载需要重试2，url={}");
                    try {
                        boolean delete = tmpFile.delete();
                        Thread.sleep(1000);
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }
                    continue;
                }
            }
            // 清理回调缓存
            stringRedisTemplate.delete(VideoConstant.CALL_BACK_ROOM_CACHE + roomId);
            //读取到回调值或到30次没有下载到文件 文件小于10kb抛异常 否则返回临时文件路径

            byte[] filebyte = getFilebyte(url);
            //创建临时文件
            File tmpFile = null;
            try {
                tmpFile = genTempFile("video-busin-" + System.currentTimeMillis() + ".mp4");
                cn.hutool.core.io.FileUtil.writeBytes(filebyte, tmpFile);
            } catch (Exception e) {
                log.error("文件转File错误");
                throw new BizException(e.getMessage());
            }
            log.info("即构视频文件最后一次下载,user_id{},fileSize：{},i: {}", user_id, fileSize, i);
            if (!tmpFile.exists() || tmpFile.length() < 10240) {
                boolean delete = tmpFile.delete();
                throw new BizException(ErrorConstant.VIDEO_DOWNLOAD_FILE_ERROR, "即构视频回调下载失败");
            }
            boolean delete = tmpFile.delete();
            ElectUploadFileByUriResponse electUploadFileByUriResponse = getElectUploadFileByUriResponse(url);
            return electUploadFileByUriResponse.getFilerecord_id();
        }
        throw new BizException(ErrorConstant.VIDEO_DOWNLOAD_FILE_ERROR, "即构视频回调下载失败");
    }

    @Nullable
    private byte[] getFilebyte(String url) {
        byte[] filebyte = null;
        if (url.startsWith("http")) {
            log.info("即构回调---依据url下载文件任务");
            try {
                filebyte = IOUtils.toByteArray(URLUtil.getStream(URLUtil.url(url)));
            } catch (IOException e) {
                log.error("即构回调 io流报错：{}", e);
            }
        } else {
            log.info("即构回调---依据文件地址下载文件任务");
            filebyte = readFile(url);
        }
        return filebyte;
    }

    /**
     * 生成临时文件[会创建文件]
     */
    public static File genTempFile(String file_name) throws Exception {
        String tempPath = System.getProperty("java.io.tmpdir");
        File file = new File(tempPath + File.separator + file_name);
        file.createNewFile();
        return file;
    }

    public byte[] readFile(String filePath) {
        FileInputStream in = null;
        byte[] fileContent = null;
        try {
            in = new FileInputStream(filePath);
            fileContent = IOUtils.toByteArray(in);
            IOUtils.closeQuietly(in);
        } catch (Exception e) {
            log.error("读取文件异常", e);
            throw new BizException("-1", "读取文件异常:" + e);
        } finally {
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                    log.error("关闭流失败", e);
                }
            }
        }
        return fileContent;
    }
}
