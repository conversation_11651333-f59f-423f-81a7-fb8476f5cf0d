package com.cairh.cpe.esb.component.video.refresh.configuration;

import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;

import java.util.concurrent.Executors;

/**
 * 视频刷新调度配置(任务间的时间重叠依托事务保证数据的正确性)
 *
 * <AUTHOR>
 * @see ScheduledAnnotationBeanPostProcessor
 */
@Configuration
@EnableScheduling
public class VideoRefreshSchedulingConfigurer implements SchedulingConfigurer {

    public void configureTasks(ScheduledTaskRegistrar registrar) {
        //支持任务并行处理
        registrar.setScheduler(Executors.newScheduledThreadPool(4));
    }
}
