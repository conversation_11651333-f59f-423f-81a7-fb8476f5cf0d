package com.cairh.cpe.esb.component.video.core;

import com.cairh.cpe.esb.component.video.queue.VideoQueue;

import java.util.List;
import java.util.Map;

/**
 * 视频集, 用于维护视频信息(目前为视频用户信息), 与之关联的有视频队列, 后者维护了视频用户排队信息
 *
 * <AUTHOR>
 * @see VideoQueue
 */
public interface IVideoGroup<T extends Comparable> {

    /**
     * 新增
     *
     * @param id    对于视频集的唯一标识
     * @param video 视频信息
     */
    void insert(String id, T video);

    /**
     * 更改
     *
     * @param id   对于视频集的唯一标识
     * @param info 待更新的视频信息
     */
    void update(String id, Map<String, Object> info);

    /**
     * 更改(仅特定场景使用,无事务)
     *
     * @param id    对于视频集的唯一标识
     * @param key   键
     * @param value 值
     */
    void update(String id, String key, Object value);

    /**
     * 单个查询
     *
     * @param id 对于视频集的唯一标识
     * @return 对应的视频信息
     */
    T query(String id);

    /**
     * 组查询
     *
     * @param ids 对于视频集的唯一标识集
     * @return 对应的视频信息集
     */
    List<T> query(List<String> ids);

    /**
     * 全部查询
     *
     * @return 全部的视频信息集
     */
    List<T> queryAll();

    /**
     * 全部映射查询
     *
     * @return 全部的视频信息映射集
     */
    Map<String, T> queryAllMap();

    /**
     * 视频信息存在性
     *
     * @param id 对于视频集的唯一标识
     * @return 视频信息存在与否
     */
    boolean exist(String id);

    /**
     * 单个删除
     *
     * @param id 对于视频集的唯一标识
     */
    void remove(String id);

    /**
     * 组删除
     *
     * @param ids 对于视频集的唯一标识集
     */
    void remove(List<String> ids);
}
