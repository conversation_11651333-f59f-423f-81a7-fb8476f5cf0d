package com.cairh.cpe.esb.component.video.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cairh.cpe.component.common.data.entity.VideoRecord;
import com.cairh.cpe.component.common.data.service.IVideoRecordService;
import com.cairh.cpe.esb.component.video.dto.resp.StartRecordResponse;
import com.cairh.cpe.component.common.model.VideoUser;
import com.cairh.cpe.esb.base.rpc.IVBaseUserInfoDubboService;
import com.cairh.cpe.esb.base.rpc.dto.req.VBaseUserInfoQryRequest;
import com.cairh.cpe.esb.base.rpc.dto.resp.VBaseUserInfoQryResponse;
import com.cairh.cpe.esb.component.video.business.IVideoVenderService;
import com.cairh.cpe.esb.component.video.business.constant.BusinessFlagConstant;
import com.cairh.cpe.esb.component.video.constant.VideoConstant;
import com.cairh.cpe.esb.component.video.constant.VideoVender;
import com.cairh.cpe.esb.component.video.service.IVideoRecordBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * builder for {@link VideoRecord}
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class VideoRecordBuilder implements IVideoRecordBuilder {

    @Autowired
    private IVideoVenderService videoVenderService;

    @DubboReference(check = false)
    private IVBaseUserInfoDubboService userInfoDubboService;

    @Autowired
    private IVideoRecordService videoRecordService;

    @Override
    public VideoRecord joinQueueBuild(@NonNull VideoUser user) {
        VideoRecord videoRecord = new VideoRecord();
        commonVideoRecordInject(videoRecord, user);

        videoRecord.setBusin_flag(BusinessFlagConstant.JOIN_QUEUE);
        videoRecord.setRemark("加入队列");
        videoRecord.setTohis_flag(VideoConstant.NOT_TOHIS);

        return videoRecord;
    }

    @Override
    public VideoRecord answerVideoBuild(@NonNull VideoUser user) {
        VideoRecord videoRecord = new VideoRecord();
        commonVideoRecordInject(videoRecord, user);
        long enqueueTime = user.getEnqueue_time();
        long lastUpdateTime = user.getLast_status_update_time();
        // 每个视频阶段自入队来的等待时间
        long interval_sec = (lastUpdateTime - enqueueTime) / 1000;
        videoRecord.setInterval_sec(interval_sec);
        videoRecord.setBusin_flag(BusinessFlagConstant.ANSWER_VIDEO);
        videoRecord.setRemark("接听视频");
        videoRecord.setTohis_flag(VideoConstant.NOT_TOHIS);

        return videoRecord;
    }

    @Override
    public VideoRecord getConnInfoBuild(@NonNull VideoUser user) {
        VideoRecord videoRecord = new VideoRecord();
        commonVideoRecordInject(videoRecord, user);

        VideoVender vender = videoVenderService.getVender(user);
        videoRecord.setFactory_name(vender.name);

        videoRecord.setBusin_flag(BusinessFlagConstant.GET_CONN_INFO);
        videoRecord.setRemark("获取连接信息");
        videoRecord.setTohis_flag(VideoConstant.NOT_TOHIS);

        return videoRecord;
    }

    @Override
    public VideoRecord updateStatusBuild(@NonNull VideoUser user) {
        VideoRecord videoRecord = new VideoRecord();
        commonVideoRecordInject(videoRecord, user);
        VideoVender vender = videoVenderService.getVender(user);
        videoRecord.setFactory_name(vender.name);

        adaptForBusinFlag(user, videoRecord);
        videoRecord.setTohis_flag(VideoConstant.NOT_TOHIS); //temporary ignore
        if (StringUtils.isNotBlank(user.getOperator_no())) {
            VBaseUserInfoQryResponse vBaseUserInfoQryResponse = userInfoDubboService.baseUserQryUserInfo(new VBaseUserInfoQryRequest().setStaff_no(user.getOperator_no()));
            if (vBaseUserInfoQryResponse != null) {
                videoRecord.setOp_branch_no(vBaseUserInfoQryResponse.getBranch_no());
                videoRecord.setOperator_name(vBaseUserInfoQryResponse.getUser_name());
            }
        }

        return videoRecord;
    }

    /**
     * 公共视频记录注入
     *
     * @param videoRecord {@link VideoRecord}
     * @param user        视频用户
     */
    private void commonVideoRecordInject(VideoRecord videoRecord, VideoUser user) {
        log.info("流水记录 room_id:{}:{}",user.getUnique_id(),StringUtils.isNotBlank(user.getRoom_id())?user.getRoom_id():"");
        BeanUtil.copyProperties(user, videoRecord, false);

        videoRecord.setFull_name(user.getUser_name());
        videoRecord.setUnique_id(StringUtils.removeStart(user.getUnique_id(), user.getSubsys_no()));
        videoRecord.setQueue_length((int) (Objects.nonNull(user.getQueue_count()) ? user.getQueue_count() : 0L));
        videoRecord.setCreate_datetime(new Date());
    }

    /**
     * 跟随状态变化 按状态适配
     *
     * @param videoRecord {@link VideoRecord}
     */
    private void adaptForBusinFlag(VideoUser user, VideoRecord videoRecord) {
        String status = user.getStatus();
        long enqueueTime = user.getEnqueue_time();
        long currTime = System.currentTimeMillis();
        long intervalSec = (currTime - enqueueTime) / 1000;
        if (StringUtils.isNotBlank(status)) {
            switch (status) {
                case VideoConstant.STATUS_1_MATCHED:
                    videoRecord.setBusin_flag(BusinessFlagConstant.ENTER_ROOM_SUCCESS);
                    videoRecord.setInterval_sec(dealInterValSec(user));
                    videoRecord.setRemark("进入房间成功");
                    break;
                case VideoConstant.STATUS_2_VIDEOING:
                    log.info("视频中记录成功");
                    videoRecord.setBusin_flag(BusinessFlagConstant.USER_VIDEOING);
                    videoRecord.setInterval_sec(dealInterValSec(user));
                    videoRecord.setRemark("用户视频中");
                    break;
                case VideoConstant.STATUS_4_VIDEOED:
                    videoRecord.setBusin_flag(BusinessFlagConstant.USER_LEAVE_QUEUE);
                    videoRecord.setInterval_sec(dealInterValSec(user));
                    videoRecord.setRemark("用户退出视频队列");
                    break;
                case VideoConstant.STATUS_5_VIDEOED:
                    videoRecord.setBusin_flag(BusinessFlagConstant.USER_CLEANED_FROM_QUEUE);
                    videoRecord.setInterval_sec(dealInterValSec(user));
                    videoRecord.setRemark("用户从视频等待队列移除");
                    break;
                case VideoConstant.STATUS_6_VIDEOED:
                    videoRecord.setBusin_flag(BusinessFlagConstant.OPERATOR_EXIT_ACCIDENT);
                    videoRecord.setInterval_sec(dealInterValSec(user));
                    videoRecord.setRemark("坐席中断见证");
                    break;
                case VideoConstant.STATUS_7_VIDEOED:
                    videoRecord.setBusin_flag(BusinessFlagConstant.USER_EXIT_ACCIDENT);
                    videoRecord.setInterval_sec(dealInterValSec(user));
                    videoRecord.setRemark("用户异常中断");
                    break;
            }
        }
    }

    // 计算等待时间
    public long dealInterValSec(VideoUser user) {
        long intervalSec = 0;
        try {
            String subSysNo = user.getSubsys_no();
            log.info("当前业务系统：{}", user.getSubsys_no());
            String unique_id = user.getUnique_id().substring(subSysNo.length());
            log.info("当前用户名称,unique_id,状态：{}，{}，{}", user.getUser_name(), unique_id, user.getStatus());
            LambdaQueryWrapper<VideoRecord> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(VideoRecord::getUnique_id, unique_id);
            queryWrapper.ne(VideoRecord::getInterval_sec, 0);
            queryWrapper.orderByDesc(VideoRecord::getCreate_datetime);
            List<VideoRecord> recordList = videoRecordService.list(queryWrapper);
            VideoRecord record = recordList.get(0);
            log.info("查询到的用户视频流水记录：{}", record);
            long currentTime = System.currentTimeMillis();
            long enqueueTime = user.getEnqueue_time();
            log.info("当前时间，入队时间:{},{}", currentTime, enqueueTime);
            intervalSec = (currentTime - enqueueTime) / 1000;
            intervalSec = intervalSec < 0 ? 0 : intervalSec;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return intervalSec;
    }
}
