package com.cairh.cpe.esb.component.video.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.component.common.data.entity.AssemblyAccept;
import com.cairh.cpe.component.common.data.entity.AssemblyParamInfo;
import com.cairh.cpe.component.common.data.mapper.AssemblyAcceptMapper;
import com.cairh.cpe.component.common.data.mapper.AssemblyParamInfoMapper;
import com.cairh.cpe.esb.base.rpc.IVBaseSysconfigdubboService;
import com.cairh.cpe.esb.base.rpc.dto.req.VBaseSysconfigReq;
import com.cairh.cpe.esb.base.rpc.dto.resp.VBaseSysconfigResp;
import com.cairh.cpe.esb.component.video.service.IAssemblyParamInfoService;
import com.cairh.cpe.esb.component.video.util.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class AssemblyParamInfoServiceImpl extends ServiceImpl<AssemblyParamInfoMapper, AssemblyParamInfo> implements IAssemblyParamInfoService {

    /**
     * GBK
     */
    private static final String CHARSET_GBK = "GBK";

    @Autowired
    private IdentifierGenerator identifierGenerator;

    @Autowired
    private AssemblyAcceptMapper assemblyacceptMapper;

    @DubboReference(check = false)
    private IVBaseSysconfigdubboService baseSysconfigdubboService;

    @Transactional(rollbackFor = Exception.class)
    public AssemblyAccept insert(String busi_content) {
        log.info("busiContent: {}", busi_content);

        AssemblyAccept assemblyaccept = new AssemblyAccept();
        String requestId = identifierGenerator.nextUUID(null);
        assemblyaccept.setRequest_id(requestId);
        assemblyaccept.setTohis_flag("0");
        assemblyaccept.setReq_content(" ");
        assemblyaccept.setRep_content(" ");
        assemblyaccept.setStatus("8");
        assemblyaccept.setRemark(" ");
        assemblyacceptMapper.insert(assemblyaccept);

        IAssemblyParamInfoService service = (IAssemblyParamInfoService) AopContext.currentProxy();
        service.sliceInsert(requestId, busi_content);

        return assemblyaccept;
    }

    public void sliceInsert(String requestId, String busi_content) {
        String charset = getDataBaseCharset();
        List<String> param_content_list = slice(busi_content, 4000, charset);
        if (param_content_list != null && !param_content_list.isEmpty()) {
            for(int i=0;i<param_content_list.size();i++){
                String serialId = identifierGenerator.nextUUID(null);
                AssemblyParamInfo param = new AssemblyParamInfo();
                param.setSerial_id(serialId);
                param.setParam_id(requestId);
                param.setOrder_no(i);
                param.setBusi_content(param_content_list.get(i));
                param.setTohis_flag("0");
                param.setRemark(" ");
                param.setStatus("8");
                baseMapper.insert(param);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void update(String requestId, String content) {
        // 1. 查询出分片的参数信息
        String fullBusiContent = queryFullBizContent(requestId);

        // 2. 合并json字符串
        JSONObject sourceJSON = JSON.parseObject(content);
        JSONObject targetJSON = JSON.parseObject(fullBusiContent);
        log.info("1. sourceJSON: {}", JSON.toJSONString(sourceJSON));
        log.info("2. targetJSON: {}", JSON.toJSONString(targetJSON.toJSONString()));

        JSONObject mergeJSON = JSONUtil.jsonMerge(sourceJSON, targetJSON);
        log.info("3. mergeJSON: {}", JSON.toJSONString(mergeJSON.toJSONString()));

        // 3. 删除原分片信息
        LambdaQueryWrapper<AssemblyParamInfo> queryWrapper
                = getQueryParamInfoByRequestIdQueryWrapper(requestId);
        this.baseMapper.delete(queryWrapper);

        // 4. 重新保存分片
        IAssemblyParamInfoService service = (IAssemblyParamInfoService) AopContext.currentProxy();
        service.sliceInsert(requestId, mergeJSON.toJSONString());
    }

    public String queryFullBizContent(String requestId) {
        LambdaQueryWrapper<AssemblyParamInfo> queryWrapper
                = getQueryParamInfoByRequestIdOrderQueryWrapper(requestId);

        List<AssemblyParamInfo> assemblyparaminfos
                = this.baseMapper
                .selectList(queryWrapper);

        // 1. 查询出分片的参数信息
        StringBuilder fullBusiContent = new StringBuilder();
        for (AssemblyParamInfo assemblyparaminfo : assemblyparaminfos) {
            fullBusiContent.append(assemblyparaminfo.getBusi_content());
        }

        return fullBusiContent.toString();
    }

    public LambdaQueryWrapper<AssemblyParamInfo> getQueryParamInfoByRequestIdOrderQueryWrapper(String requestId) {
        return new LambdaQueryWrapper<AssemblyParamInfo>()
                .eq(AssemblyParamInfo::getParam_id, requestId)
                .orderByAsc(AssemblyParamInfo::getOrder_no);
    }

    public LambdaQueryWrapper<AssemblyParamInfo> getQueryParamInfoByRequestIdQueryWrapper(String requestId) {
        return new LambdaQueryWrapper<AssemblyParamInfo>()
                .eq(AssemblyParamInfo::getParam_id, requestId);
    }

    public String slice(String str) {
        StringBuilder sb = new StringBuilder();
        if(StringUtils.isBlank(str)){
            return sb.toString();
        }
        String charset = getDataBaseCharset();
        List<String> result = slice(str, 4000, charset);
        for(String tmp : result){
            sb.append(tmp.length()).append(",");
        }
        return sb.substring(0, sb.length()-1);
    }

    /**
     * 按照编码方式不同截取指定长度字节
     * @param str 待截取字符串
     * @param length 需要截取的字节长度
     * @param charset 编码方式
     * @return
     */
    private String substring(String str,int length,String charset) {
        String result = null;
        try {
            if(StringUtils.isBlank(str)){
                return str;
            }
            //记录原始的待截字符串
            String originalStr = str;
            //判断str的长度是否超过length，超过了就先直接截取length
            if(str.length() > length){
                str = str.substring(0, length);
            }
            //从length处倒序处理
            while(true){
                //判断str的字节数是否<=length，如果小于则直接返回
                int str_bytes_length = str.getBytes(charset).length;
                if("unicode".equalsIgnoreCase(charset)){
                    str_bytes_length = str_bytes_length - 2;
                }
                if(str_bytes_length > length){
                    str = str.substring(0, str.length()-1);
                    continue;
                }
                result = str;
                break;
            }
            //如果不是最后一段，找到最后一个,，然后截取从0到,的字符串
            if(result.length() < originalStr.length()){
                //字符串中是否包含,
                if(result.contains(",")){
                    //取出最后一个,的位置
                    int lastIndexOf = result.lastIndexOf(",");
                    result = result.substring(0, lastIndexOf+1);
                }
            }
        } catch (UnsupportedEncodingException e) {
            //logger.error("切片异常", e);
        }
        return result;
    }

    /**
     * 将字符串按照指定编码切片
     * @param str 待切片字符串
     * @param length 每片的长度
     * @param charset 编码方式
     * @return
     */
    private List<String> slice(String str,int length,String charset) {
        List<String> result = new ArrayList<String>();
        if(StringUtils.isBlank(str)){
            return null;
        }

        while(true){
            String tmp = substring(str,length,charset);
            if(StringUtils.isNotBlank(tmp)){
                result.add(tmp);
                str = str.substring(tmp.length());
            }else{
                break;
            }
        }
        return result;
    }

    /**
     * 获取数据库编码配置，默认返回GBK
     * @return
     */
    private String getDataBaseCharset() {
        VBaseSysconfigReq baseSysconfigReq = new VBaseSysconfigReq();
        baseSysconfigReq.setProperty_key("base.common.database.charset");
        VBaseSysconfigResp vBaseSysconfigResp = baseSysconfigdubboService.baseDataQrySysconfig(baseSysconfigReq);
        if (vBaseSysconfigResp != null
                && StringUtils.isNotBlank(vBaseSysconfigResp.getProperty_value())) {
            return vBaseSysconfigResp.getProperty_value();
        } else {
            return CHARSET_GBK;
        }
    }
}
