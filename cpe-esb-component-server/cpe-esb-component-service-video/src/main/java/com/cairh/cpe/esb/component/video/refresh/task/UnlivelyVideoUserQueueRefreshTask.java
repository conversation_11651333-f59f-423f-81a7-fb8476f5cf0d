package com.cairh.cpe.esb.component.video.refresh.task;

import cn.hutool.core.collection.CollectionUtil;
import com.cairh.cpe.component.common.data.service.IVideoRecordService;
import com.cairh.cpe.component.common.model.VideoUser;
import com.cairh.cpe.component.common.utils.StringUtils;
import com.cairh.cpe.core.autoconfiure.env.CompositePropertySources;
import com.cairh.cpe.esb.component.video.constant.VideoConstant;
import com.cairh.cpe.esb.component.video.constant.VideoPropertyConstant;
import com.cairh.cpe.esb.component.video.core.IVideoGroup;
import com.cairh.cpe.esb.component.video.queue.IQueue;
import com.cairh.cpe.esb.component.video.queue.QueueGroup;
import com.cairh.cpe.esb.component.video.service.IVideoRecordBuilder;
import com.cairh.cpe.mem.redis.util.MemLockUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 非活跃视频用户 - RefreshTask(队列清理)
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class UnlivelyVideoUserQueueRefreshTask {

    @Autowired
    private IVideoGroup<VideoUser> videoGroup;

    @Autowired
    private CompositePropertySources compositePropertySources;

    @Autowired
    private QueueGroup queueGroup;
    @Autowired
    private MemLockUtil redissonLockUtil;
    @Autowired
    private IVideoRecordService videoRecordService;
    @Autowired
    private IVideoRecordBuilder videoRecordBuilder;
    /**
     * 非活跃视频用户 - refresh - 五秒一次
     */
    @Scheduled(initialDelay = 1000, fixedDelayString = "#{@compositePropertySources.getProperty(T(com.cairh.cpe.esb.component.video.constant.VideoPropertyConstant).USER_QUEUE_REFRESH_INTERVAL, '5000')}")
    public void refresh() {
        redissonLockUtil.lockExecConsumer(getClass().getSimpleName(), "", x -> {
            log.debug("UnlivelyVideoUserQueueRefresh lock success");
//            doRefresh();
            log.debug("UnlivelyVideoUserQueueRefresh unlock success");
        });
    }

    /**
     * 非活跃视频用户 - refresh
     */
    private void doRefresh() {
//        log.info("清理非活跃用户  执行周期时间："+System.currentTimeMillis());
        List<VideoUser> users = videoGroup.queryAll();
        if (CollectionUtil.isNotEmpty(users)) {
            users.forEach(user -> {
                if (unLive(user)) {
                    //清理非活跃用户
//                    videoGroup.remove(user.getUnique_id());
//                    log.info("清理非活跃用户："+user.getUnique_id());
//                    log.info("清理非活跃用户 时间差值：{} - {}"+System.currentTimeMillis(),user.getLast_status_update_time());
                    // 按顺序依次刷新(队列)
                    IQueue queue = queueGroup.exist(user.getUnique_id());
                    if (Objects.nonNull(queue)) {
                        // 非活跃用户仍处于排队状态
                        if (queue.dequeue(user.getUnique_id())) {
                            // 处理用户被清理出视频队列
                            user.setStatus(VideoConstant.STATUS_5_VIDEOED);
                            videoRecordService.save(videoRecordBuilder.updateStatusBuild(user));
                            log.debug("UnlivelyVideoUserRefresh user dequeue success, user_id: {}", user.getUnique_id());
                        } else {
                            log.debug("UnlivelyVideoUserRefresh user dequeue failed, user_id: {}", user.getUnique_id());
                        }
                    }
                }
            });
        }
    }


    /**
     * 视频用户是否活跃
     *
     * @param user 视频用户
     * @return 是否活跃, true否, false是
     */
    private boolean unLive(VideoUser user) {
        if (Objects.isNull(user) || Objects.isNull(user.getLast_status_update_time()) || user.getLast_status_update_time() <= 0L) {
            // 用户不存在,但已无凭证或状态最后变更时间尚未赋值/异常取值,统一认为用户新到尚活跃
            return false;
        }

        long interval = System.currentTimeMillis() - user.getLast_status_update_time();
//        if (StringUtils.equals("0",user.getStatus())) {
//            // 用户排队中 十秒钟内未更新 时间则清理
//            return interval > 20000;
//        }
        // 状态最后变更时间超过阈值认为非活跃 USER_INFO_EXPIRE_INTERVAL（非活跃用户信息过期时间）    USER_QUEUE_UNLIVE_INTERVAL （队列过期时间）
        return interval > Long.parseLong(compositePropertySources.getProperty(VideoPropertyConstant.USER_INFO_EXPIRE_INTERVAL, String.valueOf(5000)));
    }
}
