package com.cairh.cpe.esb.component.video.customize;

import com.cairh.cpe.esb.component.video.constant.RedisConstant;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.support.atomic.RedisAtomicLong;

/**
 * 视频用户自增器
 *
 * <AUTHOR>
 */
@Configuration
public class VideoUserAutoIncrementerConfiguration {


    @Bean
    @Primary
    RedisAtomicLong scoreAutoIncrementer(RedisConnectionFactory redisConnectionFactory) {
        RedisAtomicLong scoreRedisAtomicLong = new RedisAtomicLong(RedisConstant.UNIFIED_PREFIX + "user_score_auto_incrementer", redisConnectionFactory, 1L);

        // default expire infinite
        return scoreRedisAtomicLong;
    }

    @Bean
    RedisAtomicLong roomIdAutoIncrementer(RedisConnectionFactory redisConnectionFactory) {
        RoomIdRedisAtomicLong roomIdRedisAtomicLong = new RoomIdRedisAtomicLong(RedisConstant.UNIFIED_PREFIX + "roomid_auto_incrementer", redisConnectionFactory);

        // default expire infinite
        return roomIdRedisAtomicLong;
    }
}
