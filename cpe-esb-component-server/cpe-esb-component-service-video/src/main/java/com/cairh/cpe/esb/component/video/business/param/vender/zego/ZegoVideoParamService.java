package com.cairh.cpe.esb.component.video.business.param.vender.zego;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cairh.cpe.component.common.model.VideoOperator;
import com.cairh.cpe.component.common.model.VideoUser;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.core.autoconfiure.env.CompositePropertySources;
import com.cairh.cpe.esb.base.rpc.IVBaseUserInfoDubboService;
import com.cairh.cpe.esb.component.video.business.IVenderVideoParamService;
import com.cairh.cpe.esb.component.video.constant.VideoConfigConstant;
import com.cairh.cpe.esb.component.video.constant.VideoConstant;
import com.cairh.cpe.esb.component.video.constant.VideoPropertyConstant;
import com.cairh.cpe.esb.component.video.constant.VideoVender;
import com.cairh.cpe.esb.component.video.core.IVideoGroup;
import com.cairh.cpe.esb.component.video.dto.resp.StartRecordResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.support.atomic.RedisAtomicLong;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * zego 厂商-视频联通参数
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ZegoVideoParamService implements IVenderVideoParamService {

    @Autowired
    @Qualifier("roomIdAutoIncrementer")
    private RedisAtomicLong roomIdAutoIncrementer;

    @Autowired
    private IZegoService zegoService;

    @Autowired
    private CompositePropertySources compositePropertySources;
    @DubboReference(check = false, lazy = true)
    private IVBaseUserInfoDubboService baseUserInfoDubboService;

    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private IVideoGroup<VideoUser> videoGroup;

    private String recordServerAddress;

    private int app_id;

    @Override
    public Map<String, Object> getCommonVideoParam(VideoUser user, boolean isToken) {
        Map<String, Object> result = new HashMap<>();

        int appid = getAppId(user);
        Map<String, String> videoAddress = getVideoAddress(user);
        boolean useHttps = false;
        String sign_key = null;
        String zegoEnv = null;
        try {
            if (!isToken) {
                useHttps = StringUtils.equals(compositePropertySources.getProperty(VideoConfigConstant.CONFIG_VIDEO_ZEGO_USE_HTTPS), "1");
                sign_key = compositePropertySources.getProperty(VideoConfigConstant.CONFIG_VIDEO_ZEGO_SIGN_KEY);
                zegoEnv = compositePropertySources.getProperty(VideoConfigConstant.CONFIG_VIDEO_ZEGO_ENV);
            } else {
                // 证通云 底层是即构 逻辑共用
                useHttps = StringUtils.equals(compositePropertySources.getProperty(VideoConfigConstant.CONFIG_VIDEO_ZHENGTONG_USE_HTTPS), "1");
                sign_key = compositePropertySources.getProperty(VideoConfigConstant.CONFIG_VIDEO_ZHENGTONG_SIGN_KEY);
                zegoEnv = compositePropertySources.getProperty(VideoConfigConstant.CONFIG_VIDEO_ZHENGTONG_ENV);
            }

        } catch (Exception e) {
            e.printStackTrace();
        }

        result.put(VideoConstant.ZEGO_VIDEO_ADDRESS, videoAddress.get("addrOut"));
        result.put(VideoConstant.ZEGO_VIDEO_ADDRESS_IN, videoAddress.get("addrIn"));//VideoConfigConstant.CONFIG_VIDEO_ZEGO_VIDEO_FILE_URL
        result.put(VideoConstant.ZEGO_VIDEO_ADDRESS_FILE, compositePropertySources.getProperty(VideoConfigConstant.CONFIG_VIDEO_ZEGO_VIDEO_FILE_URL));
        result.put(VideoConstant.ZEGO_USE_HTTPS, Boolean.toString(useHttps));
        result.put(VideoConstant.ZEGO_APPID, String.valueOf(appid));
        result.put(VideoConstant.ZEGO_SIGNKEY, sign_key);
        result.put(VideoConstant.ZEGO_ENV, zegoEnv);

        return result;
    }

    @Override
    public Map<String, Object> getSpecialVideoParam(VideoUser user, boolean isToken) {
        Map<String, Object> result = new HashMap<>();

        // 房间号
        String roomId;
        if (StrUtil.isNotBlank(user.getRoom_id())) {
            roomId = user.getRoom_id();
        } else {
            roomId = String.valueOf(roomIdAutoIncrementer.getAndIncrement());
        }
        // 房间密码
        String roomPwd = "123456";
        String userToken = "";
        if (!isToken) {
            userToken = getZegoUserToken(getAppId(user), user);
        }

        result.put(VideoConstant.ROOM_ID, roomId);
        result.put(VideoConstant.ROOM_PASSWORD, roomPwd);
        result.put(VideoConstant.ZEGO_TOKEN, userToken);

        return result;
    }

    /**
     * get zego user token
     *
     * @param appid appid
     * @param user  {@link VideoUser}
     * @return zego user token
     */
    private String getZegoUserToken(int appid, VideoUser user) {
        String tokenUrl = compositePropertySources.getProperty(VideoPropertyConstant.ZEGO_H5_token_ADDR);
        String zegoUrl = compositePropertySources.getProperty(VideoPropertyConstant.ZEGO_H5_ADDR_IN);
        String userTokenUrl = StringUtils.join(StringUtils.defaultString(tokenUrl, zegoUrl), "/logintoken");
        log.info("ZegoUserTokenUrl is :{}, appid is:{}", userTokenUrl, appid);
        Map<String, Object> params = new HashMap<>();
        params.put("app_id", appid);
        params.put("device_id", "deviceid01");
        params.put("net_type", 2);
        params.put("queue_role", 10);
        params.put("room_role", 0);
        params.put("seq", 1);
        params.put("timestamp", (int) (System.currentTimeMillis() / 1000));
        params.put("user_id", user.getSubsys_no() + user.getUnique_id());
        params.put("user_name", "u" + user.getSubsys_no() + user.getUnique_id());

        String result = restTemplate.postForObject(userTokenUrl, params, String.class);
        log.info("Zego logintoken return is :{}", result);
        JSONObject jsonResult = JSON.parseObject(result);
        if (StrUtil.equals(jsonResult.getString("code"), "0")) {
            return jsonResult.getString("login_token");
        }
        return null;
    }

    @Override
    public Map<String, Object> getCachedVideoParam(VideoUser user) {
        Map<String, Object> result = new HashMap<>();

        result.put(VideoConstant.ROOM_ID, user.getRoom_id());
        result.put(VideoConstant.ROOM_PASSWORD, user.getRoom_pwd());
        result.put(VideoConstant.ZEGO_UESR_TOKEN, user.getZego_user_token());
        result.put(VideoConstant.ZEGO_OPERATOR_TOKEN, user.getZego_operator_token());
        result.put(VideoConstant.ZEGO_VIDEO_ADDRESS, user.getZego_video_address());
        result.put(VideoConstant.ZEGO_USE_HTTPS, Boolean.toString(user.isZego_use_https()));
        result.put(VideoConstant.ZEGO_APPID, String.valueOf(user.getZego_appid()));
        result.put(VideoConstant.ZEGO_SIGNKEY, user.getZego_signkey());
        result.put(VideoConstant.ZEGO_ENV, user.getZego_env());

        return result;
    }

    @Override
    public StartRecordResponse startRecord(VideoOperator videoOperator, VideoUser videoUser, String waterMark) {
        // 获取并检查即构配置
        initZegoH5CloudConfig(videoUser);

        // 录制参数准备
        String operator_no = videoUser.getOperator_no();
        String user_id = videoUser.getUnique_id();
        long currTime = System.currentTimeMillis();
        // 基础参数
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("app_id", app_id);
        jsonObject.put("room_id", videoUser.getRoom_id());
        jsonObject.put("user_id", operator_no);
        jsonObject.put("file_name",
                DateUtil.format(DateUtil.date(), "yyyy/MM/dd/")
                        + operator_no + "_" + user_id + "_" + currTime + ".mp4");
        // 录制参数
        JSONObject record_config = new JSONObject();
        jsonObject.put("record_config", record_config);
        record_config.put("record_mode", 2);
        JSONArray jsonArray = new JSONArray();
        jsonArray.add(operator_no);
        jsonArray.add(user_id);
        record_config.put("user_id", jsonArray);
        record_config.put("muxer_stream_type", 3);//录制流类型，控制是否录制音视频。默认值：3,MuxerStreamTypeAudio = 1, 只录制音频、MuxerStreamTypeVideo = 2, 只录制视频 、MuxerStreamTypeBoth = 3, 录制音视频
        record_config.put("fragment_seconds", 2);
        record_config.put("output_audio_bitrate", 8000);
        record_config.put("output_fps", 15);
        record_config.put("output_bitrate", 150000);
        record_config.put("output_width", 640);
        record_config.put("output_height", 240);
        record_config.put("background_color", 0);

        //设置时间水印
        record_config.put("dynamic_watermarking_switch", true); //动态水印开关（时间戳）
        record_config.put("dynamic_watermarking_width", 45);//动态水印宽度
        record_config.put("dynamic_watermarking_height", 10);//动态水印高度
        record_config.put("dynamic_watermarking_pos", 0);//动态水印位置：0右上角 1左上角 2右下角 3左下


        // 混流参数-座席端
        JSONArray mix_stream_layout = new JSONArray();
        record_config.put("mix_stream_layout", mix_stream_layout);
        JSONObject layout_operator = new JSONObject();
        JSONObject layout_user = new JSONObject();
        mix_stream_layout.add(layout_operator);
        mix_stream_layout.add(layout_user);
        layout_operator.put("role", 1);
        layout_operator.put("user_id", operator_no);
        layout_operator.put("stream_id", operator_no);
        layout_operator.put("top", 0);
        layout_operator.put("left", 0);
        layout_operator.put("bottom", 240);//320
        layout_operator.put("right", 320);//240
        layout_operator.put("layer", 0);
        layout_operator.put("fill_mode", 1);

        //设置水印 文本大小
        layout_operator.put("watermarking_width", 320);//320
        layout_operator.put("watermarking_height", 28);//28
        layout_operator.put("watermarking_info", waterMark);//waterMark


        // 混流参数-用户端
        layout_user.put("role", 2);
        layout_user.put("user_id", StringUtils.isBlank(videoUser.getRequest_id()) ? videoUser.getUnique_id().substring(videoUser.getSubsys_no().length(), videoUser.getUnique_id().length()) : videoUser.getRequest_id());
        layout_user.put("stream_id", StringUtils.isBlank(videoUser.getRequest_id()) ? videoUser.getUnique_id().substring(videoUser.getSubsys_no().length(), videoUser.getUnique_id().length()) : videoUser.getRequest_id());
        layout_user.put("watermarking_width", 0);
        layout_user.put("watermarking_height", 0);
        layout_user.put("watermarking_info", "");
        layout_user.put("layer", 0);
        layout_user.put("top", 0);
        layout_user.put("left", 380);
        layout_user.put("bottom", 240);
        layout_user.put("right", 560);
        layout_user.put("fill_mode", 1);


        // 接口调用
        try {
            log.info("即构录制开始调用地址：{}", recordServerAddress + "/recorder/start");
            log.info("即构录制开始调用入参：{}", jsonObject.toJSONString());
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON_UTF8);

            HttpEntity<String> requestEntity = new HttpEntity<>(jsonObject.toJSONString(), headers);
            byte[] bytes = restTemplate.postForObject(recordServerAddress + "/recorder/start", requestEntity, byte[].class);

            String json = new String(bytes, StandardCharsets.UTF_8);
            log.info("即构视频开始录制接口调用结果 operator_no={} user_id={} result={}", operator_no, user_id, json);
            JSONObject resultJSON = JSON.parseObject(json);
//            if (Constant.COMPANY_CODE_GDZQ.equals(configurationSupport.getSecurityAlias())) {
            // 光大证券，保存即构开始录制返回的录制服务地址，结束录制时也使用这个地址去结束
            // 张一凡的经验：如果即构视频服务集群部署，开始录制和结束录制不调用同一个服务地址的话会导致录制无法正常结束（缓冲区写满才会结束）
            Map<String, String> updateMap = new HashMap<>();
            Map<Object, Object> map = videoUser.getMap();
            String host = resultJSON.getString("record_svr_host");
            // 即构返回的录制服务地址是公网地址，转换为http，域名在hosts中配置解析
            host = host.replace("https", "http");
            map.put("zegoRecordServer", host);
            updateMap.put("map", JSON.toJSONString(map));
            String user_id_key = videoUser.getUnique_id();
            videoGroup.update(user_id_key, "map", map);
//            }
            // 返回
            StartRecordResponse result = new StartRecordResponse();
            result.setRawResult(json);
            result.setFile_path(resultJSON.getString("url"));
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            throw new BizException("即构录制接口调用失败");
        }
    }


    @Override
    public void stopRecord(VideoOperator videoOperator, VideoUser videoUser) {

        initZegoH5CloudConfig(videoUser);

        // 录制参数准备
        String operator_no = videoUser.getOperator_no();
        String user_id = videoUser.getUnique_id();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("app_id", app_id);
        jsonObject.put("room_id", videoUser.getRoom_id());
        jsonObject.put("user_id", operator_no);

        // 结束录制主机
        String host = recordServerAddress;
//        if (Constant.COMPANY_CODE_GDZQ.equals(configurationSupport.getSecurityAlias())) {
        // 光大证券，使用开始录制时返回的录制服务地址去结束录制
        host = (String) videoUser.getMap().get("zegoRecordServer");
//        }

        // 接口调用
        try {
            // 协议（protocol）项目 访问 第三方项目
            log.info("即构录制结束调用地址：{}", recordServerAddress + "/recorder/stop");
            byte[] bytes = restTemplate.postForObject(recordServerAddress + "/recorder/stop", jsonObject.toJSONString(), byte[].class);
//            byte[] bytes = httpClientUtil.httpPost(
//                    host + "/recorder/stop",
//                    jsonObject.toJSONString(),
//                    ContentType.APPLICATION_JSON,
//                    null);
            String json = new String(bytes, StandardCharsets.UTF_8);
            log.info("即构视频结束录制接口调用结果 operator_no={} user_id={} result={}", operator_no, user_id, json);
        } catch (Exception e) {
            log.error("即构视频，结束录制异常。message={}", e.getMessage(), e);
            throw new BizException("即构录制接口调用失败");
        }
    }

    private void initZegoH5CloudConfig(VideoUser videoUser) {
        // 获取并检查即构配置
        VideoVender videoVender = VideoVender.valueOf(videoUser.getService_vender().toUpperCase());
        if (VideoVender.ZEGO_H5_PRIVATE_CLOUD.equals(videoVender)) {
            // /** 即构h5公有云视频录制服务地址 */
            //    public static final String
            //            CONFIG_VIDEO_ZEGO_H5_PUBLIC_CLOUD_RECORD_ADDRESS = "config.video.zego.h5.public.cloud.record.address";
            ///** 即构h5公有云视频分配的app_id */
            //    public static final String
            //            CONFIG_VIDEO_ZEGO_H5_PUBLIC_CLOUD_APP_ID = "config.video.zego.h5.public.cloud.app.id";
//            throw new BizException("即构云服务器暂未开通！");
            if (StringUtils.isBlank(compositePropertySources.getProperty(VideoPropertyConstant.ZEGO_H5_ADDR))) {
                throw new BizException("zego视频服务地址未配置！");
            }
            String zegoUrl = compositePropertySources.getProperty(VideoPropertyConstant.ZEGO_H5_ADDR);
            String zegoUrlIn = compositePropertySources.getProperty(VideoPropertyConstant.ZEGO_H5_ADDR_IN);
            recordServerAddress = StringUtils.defaultString(zegoUrlIn, zegoUrl);
            if (StringUtils.isBlank(compositePropertySources.getProperty(VideoPropertyConstant.ZEGO_H5_APPID))) {
                throw new BizException("zego app_id未配置！");
            }
            app_id = Integer.valueOf(compositePropertySources.getProperty(VideoPropertyConstant.ZEGO_H5_APPID));
        } else {
            if (StringUtils.isBlank(compositePropertySources.getProperty(VideoPropertyConstant.ZEGO_H5_ADDR))) {
                throw new BizException("zego视频服务地址未配置！");
            }
            String zegoUrl = compositePropertySources.getProperty(VideoPropertyConstant.ZEGO_H5_ADDR);
            String zegoUrlIn = compositePropertySources.getProperty(VideoPropertyConstant.ZEGO_H5_ADDR_IN);
            recordServerAddress = StringUtils.defaultString(zegoUrlIn, zegoUrl);
            if (StringUtils.isBlank(compositePropertySources.getProperty(VideoPropertyConstant.ZEGO_H5_APPID))) {
                throw new BizException("zego app_id未配置！");
            }
            app_id = Integer.valueOf(compositePropertySources.getProperty(VideoPropertyConstant.ZEGO_H5_APPID));
        }
    }

    /**
     * 获取用户token
     *
     * @param user 视频用户
     * @return token
     */
    private String getUserToken(VideoUser user) {
        String vender = user.getService_vender();
        String unique_id = user.getUnique_id();
        VideoVender videoVender = VideoVender.valueOf(vender.toUpperCase());
        switch (videoVender) {
            case ZEGO:
            case ZEGO_H5_PRIVATE_CLOUD:
            case ZEGO_MINI_PROGRAM:
                return zegoService.getH5PrivateCloudToken(unique_id, IZegoService.Role.USER).getLogin_token();
            default:
                throw new BizException("illegal videoVender: " + vender);
        }
    }

    /**
     * 获取坐席登录token
     *
     * @param user 视频用户
     * @return token
     */
    private String getOperatorToken(VideoUser user) {
        String vender = user.getService_vender();
        String operator_no = user.getOperator_no();
        VideoVender videoVender = VideoVender.valueOf(vender.toUpperCase());
        switch (videoVender) {
            case ZEGO:
            case ZEGO_H5_PRIVATE_CLOUD:
            case ZEGO_MINI_PROGRAM:
                return zegoService.getH5PrivateCloudToken(operator_no, IZegoService.Role.OPERATOR).getLogin_token();
            default:
                throw new BizException("illegal videoVender: " + vender);
        }
    }

    /**
     * 获取视频服务地址
     *
     * @param user 视频用户
     * @return 服务地址
     */
    private Map<String, String> getVideoAddress(VideoUser user) {
        String vender = user.getService_vender();
        VideoVender videoVender = VideoVender.valueOf(vender.toUpperCase());
        Map<String, String> result = new HashMap<>();
        switch (videoVender) {
            case ZEGO:
            case ZEGO_H5_PRIVATE_CLOUD:
            case ZEGO_MINI_PROGRAM:
                result.put("addrOut", compositePropertySources.getProperty(VideoPropertyConstant.ZEGO_H5_ADDR));
                if (Objects.isNull(compositePropertySources.getProperty(VideoPropertyConstant.ZEGO_H5_ADDR_IN))) {
                    result.put("addrIn", compositePropertySources.getProperty(VideoPropertyConstant.ZEGO_H5_ADDR));
                } else {
                    result.put("addrIn", compositePropertySources.getProperty(VideoPropertyConstant.ZEGO_H5_ADDR_IN));
                }
                return result;
            case ZHENGTONG:
                result.put("addrIn", compositePropertySources.getProperty(VideoPropertyConstant.ZHENGTONG_H5_ADDR));
                return result;
            default:
                throw new BizException("illegal videoVender: " + vender);
        }
    }

    /**
     * 获取app_id
     *
     * @param user 视频用户
     * @return app_id
     */
    private int getAppId(VideoUser user) {
        String vender = user.getService_vender();
        VideoVender videoVender = VideoVender.valueOf(vender.toUpperCase());
        switch (videoVender) {
            case ZEGO:
            case ZEGO_H5_PRIVATE_CLOUD:
            case ZEGO_MINI_PROGRAM:
                return Integer.parseInt(compositePropertySources.getProperty(VideoPropertyConstant.ZEGO_H5_APPID, "0"));
            case ZHENGTONG:
                return Integer.parseInt(compositePropertySources.getProperty(VideoPropertyConstant.ZHENGTONG_H5_APPID, "0"));
            default:
                throw new BizException("illegal videoVender: " + vender);
        }
    }
}
