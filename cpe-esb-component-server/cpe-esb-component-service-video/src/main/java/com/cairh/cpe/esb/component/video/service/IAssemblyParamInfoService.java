package com.cairh.cpe.esb.component.video.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cairh.cpe.component.common.data.entity.AssemblyAccept;
import com.cairh.cpe.component.common.data.entity.AssemblyParamInfo;

public interface IAssemblyParamInfoService extends IService<AssemblyParamInfo> {
    AssemblyAccept insert(String busi_content);

    void update(String requestId, String content);

    String queryFullBizContent(String requestId);

    void sliceInsert(String requestId, String busi_content);
}
