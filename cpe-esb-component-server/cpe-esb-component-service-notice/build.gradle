dependencies {
    api(project(':cpe-esb-component-server-api'))
    api(project(':cpe-esb-component-server:cpe-esb-component-core'))
    api(project(':cpe-component-common'))
//    api('com.cairh:cpe-counter-ws')
    api('com.cairh:cpe-base-protocol-gateway-ws')
    api('com.google.protobuf:protobuf-java')
    api(project(':cpe-counter-extension:cpe-counter-extension-component'))

    api('org.dom4j:dom4j')
    api('org.springframework.boot:spring-boot-starter-mail')



    testImplementation('org.springframework.boot:spring-boot-starter-test')
}