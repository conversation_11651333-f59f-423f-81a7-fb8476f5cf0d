package com.cairh.cpe.esb.component.base.service;

import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;

import com.cairh.cpe.esb.component.base.IEsbComponentBaseDubboService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@DubboService
public class EsbComponentBaseDubboServiceImpl implements IEsbComponentBaseDubboService {

	@Autowired
	private CacheManager cacheManager;

	@Override
	public String clearCache(String ... cacheNames) {
		if (cacheNames != null && cacheNames.length > 0) {
			for (String cacheName : cacheNames) {
				Cache cache = cacheManager.getCache(cacheName);
				if (cache != null) {
					cache.clear();
					log.info("Cache[{}]执行了清除", cache.getName());
				}
			}
		}
		return "success";
	}

}
