package com.cairh.cpe.esb.component.business.service;

import com.alibaba.fastjson.JSON;
import com.cairh.cpe.esb.component.business.config.DictionaryCacheConfig;
import com.cairh.cpe.esb.component.business.dto.req.BusinessQueryAccountDictInfoReq;
import com.cairh.cpe.esb.component.business.dto.resp.BusinessQueryAccountDictInfoResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 字典缓存服务类
 * 
 * <AUTHOR>
 * @since 2025/7/17
 */
@Slf4j
@Service
public class DictionaryCacheService {

    @Resource
    private EsbComponentBusinessServiceImpl businessService;

    /**
     * 获取字典信息（带缓存）
     * 
     * @param dictEntry 字典编码
     * @return 字典信息响应
     */
    @Cacheable(value = DictionaryCacheConfig.DICTIONARY_CACHE_NAME, key = "#dictEntry", unless = "#result == null")
    public BusinessQueryAccountDictInfoResp getDictionaryInfo(String dictEntry) {
        log.info("从数据库查询字典信息, dictEntry: {}", dictEntry);
        
        BusinessQueryAccountDictInfoReq request = new BusinessQueryAccountDictInfoReq();
        request.setDictEntry(dictEntry);
        
        try {
            BusinessQueryAccountDictInfoResp response = businessService.businessQueryAccountDictInfo(request);
            log.info("字典信息查询成功, dictEntry: {}, response: {}", dictEntry, JSON.toJSONString(response));
            return response;
        } catch (Exception e) {
            log.error("字典信息查询失败, dictEntry: {}", dictEntry, e);
            return null;
        }
    }

    /**
     * 获取字典映射表（带缓存）
     * 
     * @param dictEntry 字典编码
     * @return 字典映射表 (subentry -> dict_prompt)
     */
    @Cacheable(value = DictionaryCacheConfig.DICTIONARY_CACHE_NAME, key = "'map_' + #dictEntry", unless = "#result.isEmpty()")
    public Map<String, String> getDictionaryMap(String dictEntry) {
        log.info("从数据库查询字典映射表, dictEntry: {}", dictEntry);
        
        BusinessQueryAccountDictInfoResp response = getDictionaryInfo(dictEntry);
        
        return Optional.ofNullable(response)
                .map(BusinessQueryAccountDictInfoResp::getData)
                .map(data -> {
                    Map<String, String> dictMap = new HashMap<>();
                    data.forEach(item -> dictMap.put(item.getSubentry(), item.getDict_prompt()));
                    return dictMap;
                })
                .orElse(Collections.emptyMap());
    }

    /**
     * 批量获取字典映射表（带缓存）
     * 
     * @param dictEntries 字典编码列表
     * @return 合并后的字典映射表
     */
    public Map<String, String> getBatchDictionaryMap(String... dictEntries) {
        Map<String, String> mergedMap = new HashMap<>();
        
        for (String dictEntry : dictEntries) {
            Map<String, String> dictMap = getDictionaryMap(dictEntry);
            mergedMap.putAll(dictMap);
        }
        
        log.info("批量获取字典映射表完成, 总数量: {}", mergedMap.size());
        return mergedMap;
    }

    /**
     * 清除指定字典的缓存
     * 
     * @param dictEntry 字典编码
     */
    @CacheEvict(value = DictionaryCacheConfig.DICTIONARY_CACHE_NAME, key = "#dictEntry")
    public void evictDictionaryCache(String dictEntry) {
        log.info("清除字典缓存, dictEntry: {}", dictEntry);
    }

    /**
     * 清除字典映射表缓存
     * 
     * @param dictEntry 字典编码
     */
    @CacheEvict(value = DictionaryCacheConfig.DICTIONARY_CACHE_NAME, key = "'map_' + #dictEntry")
    public void evictDictionaryMapCache(String dictEntry) {
        log.info("清除字典映射表缓存, dictEntry: {}", dictEntry);
    }

    /**
     * 清除所有字典缓存
     */
    @CacheEvict(value = DictionaryCacheConfig.DICTIONARY_CACHE_NAME, allEntries = true)
    public void evictAllDictionaryCache() {
        log.info("清除所有字典缓存");
    }

    /**
     * 预热字典缓存
     * 
     * @param dictEntries 需要预热的字典编码列表
     */
    public void warmUpDictionaryCache(String... dictEntries) {
        log.info("开始预热字典缓存, dictEntries: {}", JSON.toJSONString(dictEntries));
        
        for (String dictEntry : dictEntries) {
            try {
                getDictionaryInfo(dictEntry);
                getDictionaryMap(dictEntry);
                log.info("字典缓存预热成功, dictEntry: {}", dictEntry);
            } catch (Exception e) {
                log.error("字典缓存预热失败, dictEntry: {}", dictEntry, e);
            }
        }
        
        log.info("字典缓存预热完成");
    }
}
