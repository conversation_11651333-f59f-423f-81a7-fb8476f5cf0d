package com.cairh.cpe.esb.component.business.config;

import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.concurrent.ConcurrentMapCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.concurrent.TimeUnit;

/**
 * 字典缓存配置类
 * 
 * <AUTHOR>
 * @since 2025/7/17
 */
@Configuration
@EnableCaching
public class DictionaryCacheConfig {

    /**
     * 字典缓存名称
     */
    public static final String DICTIONARY_CACHE_NAME = "dictionary_cache";
    
    /**
     * 字典缓存TTL（秒）
     */
    public static final long DICTIONARY_CACHE_TTL = TimeUnit.HOURS.toSeconds(1); // 1小时

    /**
     * 配置缓存管理器
     * 使用内存缓存作为默认实现，可以根据需要替换为Redis
     */
    @Bean
    @Primary
    public CacheManager dictionaryCacheManager() {
        ConcurrentMapCacheManager cacheManager = new ConcurrentMapCacheManager();
        cacheManager.setCacheNames(java.util.Arrays.asList(DICTIONARY_CACHE_NAME));
        return cacheManager;
    }
}
