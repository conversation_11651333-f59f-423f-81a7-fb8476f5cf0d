package com.cairh.cpe.esb.component.business.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.cairh.cpe.esb.component.business.IEsbComponentBusinessDubboService;
import com.cairh.cpe.esb.component.business.dto.req.*;
import com.cairh.cpe.esb.component.business.dto.resp.*;
import com.cairh.cpe.esb.component.business.service.support.T2ServiceInvoker;
import com.cairh.cpe.esb.component.file.utils.MapUtil;
import com.cairh.cpe.t2.data.fgt.dto.req.*;
import com.cairh.cpe.t2.data.fgt.service.T2GtFunctionService;
import com.cairh.cpe.util.crypt.Md5Encrypt;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/7/9 10:13
 */
@Slf4j
@DubboService
public class EsbComponentBusinessServiceImpl implements IEsbComponentBusinessDubboService {

    // 一级标签字典
    private final static String DICT_ENTRY_590349 = "590349";
    // 二级标签字典
    private final static String DICT_ENTRY_590350 = "590350";

    /**
     * MD5加密盐值常量
     */
    private static final String MD5_SALT = "gtjajzyy";

    @Resource
    private T2GtFunctionService t2GtFunctionService;
    @Resource
    private T2ServiceInvoker t2ServiceInvoker;

    @Override
    public BusinessQueryClientAccountResp businessQueryClientAccount(BusinessQueryClientAccountReq request) {
        return t2ServiceInvoker.invoke(
                request,
                T2_10201862_Request.class,
                BusinessQueryClientAccountResp.class,
                t2GtFunctionService::T2_10201862,
                "查询客户账户信息",
                req -> generateMd5Token(request.getId_no())
        );
    }

    @Override
    public BusinessExistsClientAccountTagResp businessExistsClientAccountTag(BusinessExistsClientAccountTagReq request) {
        return t2ServiceInvoker.invoke(
                request,
                T2_10231024_Request.class,
                BusinessExistsClientAccountTagResp.class,
                t2GtFunctionService::T2_10231024,
                "查询账户标签信息是否存在",
                null
        );
    }

    @Override
    public BusinessQueryAccountLabelInfoResp businessQueryAccountLabelInfo(BusinessQueryAccountLabelInfoReq request) {
        BusinessQueryAccountLabelInfoResp businessQueryAccountLabelInfoResp = t2ServiceInvoker.invoke(
                request,
                T2_10231023_Request.class,
                BusinessQueryAccountLabelInfoResp.class,
                t2GtFunctionService::T2_10231023,
                "账户标签信息查询",
                null
        );
        log.info("账户标签信息查询:{}", JSON.toJSONString(businessQueryAccountLabelInfoResp));
        if (businessQueryAccountLabelInfoResp == null) {
            return null;
        }
        // 查询字典,进行转译
        BusinessQueryAccountDictInfoReq businessQueryAccountDictInfoReq = new BusinessQueryAccountDictInfoReq();
        // 一级标签
        businessQueryAccountDictInfoReq.setDictEntry(DICT_ENTRY_590349);
        BusinessQueryAccountDictInfoResp businessQueryAccountDictInfoResp = businessQueryAccountDictInfo(businessQueryAccountDictInfoReq);
        log.info("一级标签信息查询:{}", JSON.toJSONString(businessQueryAccountDictInfoResp));
        // 二级标签
        businessQueryAccountDictInfoReq.setDictEntry(DICT_ENTRY_590350);
        BusinessQueryAccountDictInfoResp secBusinessQueryAccountDictInfoResp = businessQueryAccountDictInfo(businessQueryAccountDictInfoReq);
        log.info("二级标签信息查询:{}", JSON.toJSONString(secBusinessQueryAccountDictInfoResp));
        // 合并所有标签
        List<BusinessQueryAccountDictInfoResp.T2_10231081_Resp> allDictList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(businessQueryAccountDictInfoResp.getData())) {
            allDictList.addAll(businessQueryAccountDictInfoResp.getData());
        }
        if (CollectionUtils.isNotEmpty(secBusinessQueryAccountDictInfoResp.getData())) {
            allDictList.addAll(secBusinessQueryAccountDictInfoResp.getData());
        }
        log.info("账户标签字典信息查询allLabelDictList:{}", allDictList);
        if (CollectionUtils.isNotEmpty(allDictList)) {
            return businessQueryAccountLabelInfoResp;
        }
        // 转成map
        Map<String, String> dictMap = allDictList.stream()
                .collect(Collectors.toMap(BusinessQueryAccountDictInfoResp.T2_10231081_Resp::getSubentry, BusinessQueryAccountDictInfoResp.T2_10231081_Resp::getDict_prompt));
        // 设置标签名称
        businessQueryAccountLabelInfoResp.setGt_acctlabel_name(MapUtil.getString(dictMap, businessQueryAccountLabelInfoResp.getGt_acctlabel(), StrUtil.EMPTY));
        businessQueryAccountLabelInfoResp.setGt_sec_acctlabel_name(MapUtil.getString(dictMap, businessQueryAccountLabelInfoResp.getGt_sec_acctlabel(), StrUtil.EMPTY));
        return businessQueryAccountLabelInfoResp;
    }

    @Override
    public BusinessQueryAccountAppropriateInfoResp businessQueryAccountAppropriateInfo(BusinessQueryAccountAppropriateInfoReq request) {
        return t2ServiceInvoker.invoke(
                request,
                T2_10201216_Request.class,
                BusinessQueryAccountAppropriateInfoResp.class,
                t2GtFunctionService::T2_10201216,
                "客户一户通号的适当性信息查询",
                null
        );
    }

    @Override
    public BusinessQueryAccountDictInfoResp businessQueryAccountDictInfo(BusinessQueryAccountDictInfoReq request) {
        return t2ServiceInvoker.invoke(
                request,
                T2_10231081_Request.class,
                BusinessQueryAccountDictInfoResp.class,
                t2GtFunctionService::T2_10231081,
                "账户标签字典信息查询",
                null
        );
    }

    /**
     * 生成MD5令牌
     *
     * @param identifier 标识符（如客户ID、账户号等）
     * @return MD5加密后的令牌（大写）
     */
    private String generateMd5Token(String identifier) {
        return Md5Encrypt.MD5(identifier + MD5_SALT).toUpperCase();
    }
}
