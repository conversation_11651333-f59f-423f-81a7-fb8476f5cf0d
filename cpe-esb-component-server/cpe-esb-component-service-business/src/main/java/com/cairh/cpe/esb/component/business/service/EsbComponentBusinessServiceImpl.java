package com.cairh.cpe.esb.component.business.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.esb.component.business.IEsbComponentBusinessDubboService;
import com.cairh.cpe.esb.component.business.dto.req.*;
import com.cairh.cpe.esb.component.business.dto.resp.*;
import com.cairh.cpe.esb.component.business.service.support.T2ServiceInvoker;
import com.cairh.cpe.t2.data.fgt.dto.req.*;
import com.cairh.cpe.t2.data.fgt.service.T2GtFunctionService;
import com.cairh.cpe.util.crypt.Md5Encrypt;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @since 2025/7/9 10:13
 */
@Slf4j
@DubboService
public class EsbComponentBusinessServiceImpl implements IEsbComponentBusinessDubboService {

    /**
     * 一级标签字典编码
     */
    private static final String DICT_ENTRY_PRIMARY_LABEL = "590349";

    /**
     * 二级标签字典编码
     */
    private static final String DICT_ENTRY_SECONDARY_LABEL = "590350";

    /**
     * MD5加密盐值常量
     */
    private static final String MD5_SALT = "gtjajzyy";

    @Resource
    private T2GtFunctionService t2GtFunctionService;
    @Resource
    private T2ServiceInvoker t2ServiceInvoker;

    @Override
    public BusinessQueryClientAccountResp businessQueryClientAccount(BusinessQueryClientAccountReq request) {
        return t2ServiceInvoker.invoke(
                request,
                T2_10201862_Request.class,
                BusinessQueryClientAccountResp.class,
                t2GtFunctionService::T2_10201862,
                "查询客户账户信息",
                req -> generateMd5Token(request.getId_no())
        );
    }

    @Override
    public BusinessExistsClientAccountTagResp businessExistsClientAccountTag(BusinessExistsClientAccountTagReq request) {
        return t2ServiceInvoker.invoke(
                request,
                T2_10231024_Request.class,
                BusinessExistsClientAccountTagResp.class,
                t2GtFunctionService::T2_10231024,
                "查询账户标签信息是否存在",
                null
        );
    }

    @Override
    public BusinessQueryAccountLabelInfoResp businessQueryAccountLabelInfo(BusinessQueryAccountLabelInfoReq request) {
        // 1. 查询账户标签信息
        BusinessQueryAccountLabelInfoResp labelInfoResp = t2ServiceInvoker.invoke(
                request,
                T2_10231023_Request.class,
                BusinessQueryAccountLabelInfoResp.class,
                t2GtFunctionService::T2_10231023,
                "账户标签信息查询",
                null
        );
        log.info("账户标签信息查询结果: {}", JSON.toJSONString(labelInfoResp));
        if (labelInfoResp == null) {
            log.warn("账户标签信息查询结果为空, request: {}", JSON.toJSONString(request));
            return null;
        }

        // 2. 查询字典信息并进行转译
        enrichLabelInfoWithDictionary(labelInfoResp);

        return labelInfoResp;
    }

    @Override
    public BusinessQueryAccountAppropriateInfoResp businessQueryAccountAppropriateInfo(BusinessQueryAccountAppropriateInfoReq request) {
        return t2ServiceInvoker.invoke(
                request,
                T2_10201216_Request.class,
                BusinessQueryAccountAppropriateInfoResp.class,
                t2GtFunctionService::T2_10201216,
                "客户一户通号的适当性信息查询",
                null
        );
    }

    @Override
    public BusinessQueryAccountDictInfoResp businessQueryAccountDictInfo(BusinessQueryAccountDictInfoReq request) {
        return t2ServiceInvoker.invoke(
                request,
                T2_10231081_Request.class,
                BusinessQueryAccountDictInfoResp.class,
                t2GtFunctionService::T2_10231081,
                "账户标签字典信息查询",
                null
        );
    }

    /**
     * 使用字典信息丰富标签信息
     *
     * @param labelInfoResp 标签信息响应对象
     */
    private void enrichLabelInfoWithDictionary(BusinessQueryAccountLabelInfoResp labelInfoResp) {
        try {
            // 并行查询一级和二级标签字典
            CompletableFuture<BusinessQueryAccountDictInfoResp> primaryDictFuture =
                    CompletableFuture.supplyAsync(() -> queryDictionaryInfo(DICT_ENTRY_PRIMARY_LABEL));

            CompletableFuture<BusinessQueryAccountDictInfoResp> secondaryDictFuture =
                    CompletableFuture.supplyAsync(() -> queryDictionaryInfo(DICT_ENTRY_SECONDARY_LABEL));

            // 等待两个查询完成
            BusinessQueryAccountDictInfoResp primaryDictResp = primaryDictFuture.get();
            BusinessQueryAccountDictInfoResp secondaryDictResp = secondaryDictFuture.get();

            log.info("一级标签字典查询结果: {}", JSON.toJSONString(primaryDictResp));
            log.info("二级标签字典查询结果: {}", JSON.toJSONString(secondaryDictResp));

            Map<String, String> dictMap = new HashMap<>();

            // 添加一级标签字典
            Optional.ofNullable(primaryDictResp)
                    .map(BusinessQueryAccountDictInfoResp::getData)
                    .filter(CollectionUtils::isNotEmpty)
                    .ifPresent(data -> data.forEach(item ->
                            dictMap.put(item.getSubentry(), item.getDict_prompt())));

            // 添加二级标签字典
            Optional.ofNullable(secondaryDictResp)
                    .map(BusinessQueryAccountDictInfoResp::getData)
                    .filter(CollectionUtils::isNotEmpty)
                    .ifPresent(data -> data.forEach(item ->
                            dictMap.put(item.getSubentry(), item.getDict_prompt())));

            log.info("合并后的字典信息数量: {}", dictMap.size());
            if (dictMap.isEmpty()) {
                log.warn("字典信息查询结果为空，跳过标签名称设置");
                return;
            }

            String primaryLabelName = dictMap.getOrDefault(labelInfoResp.getGt_acctlabel(), StrUtil.EMPTY);
            String secondaryLabelName = dictMap.getOrDefault(labelInfoResp.getGt_sec_acctlabel(), StrUtil.EMPTY);

            labelInfoResp.setGt_acctlabel_name(primaryLabelName);
            labelInfoResp.setGt_sec_acctlabel_name(secondaryLabelName);

            log.debug("设置标签名称 - 一级标签: {} -> {}, 二级标签: {} -> {}",
                    labelInfoResp.getGt_acctlabel(), primaryLabelName,
                    labelInfoResp.getGt_sec_acctlabel(), secondaryLabelName);
        } catch (Exception e) {
            log.error("字典信息查询或设置失败", e);
            throw new BizException(e);
        }
    }


    /**
     * 查询单个字典信息
     *
     * @param dictEntry 字典编码
     * @return 字典信息响应
     */
    private BusinessQueryAccountDictInfoResp queryDictionaryInfo(String dictEntry) {
        BusinessQueryAccountDictInfoReq request = new BusinessQueryAccountDictInfoReq();
        request.setDictEntry(dictEntry);
        return businessQueryAccountDictInfo(request);
    }

    /**
     * 生成MD5令牌
     *
     * @param identifier 标识符（如客户ID、账户号等）
     * @return MD5加密后的令牌（大写）
     */
    private String generateMd5Token(String identifier) {
        return Md5Encrypt.MD5(identifier + MD5_SALT).toUpperCase();
    }
}
