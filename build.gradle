plugins {
    id 'io.spring.dependency-management' version '1.0.11.RELEASE' apply false
    id 'org.springframework.boot' version '2.6.15' apply false
    id 'idea'
}

ext {
    artifactProjects = [
            project(":cpe-esb-component-server-api")
    ]
    mavenUserHome = "MAVEN_USER_HOME"
}
Map<String, String> versionMap = getVersionMap();
Map<String, List<String>> branchVersionMap = getBranchVersionMap();
String newMavenVersion = newMavenVersion();
allprojects {
    apply plugin: 'java'
    apply plugin: 'java-library'
    apply plugin: 'io.spring.dependency-management'

    group 'com.cairh'
    // 此处默认使用自增版本号，如果要自定义版本号，请在此处自行修改 eg：version '0.3.1'
    // version newMavenVersion
    version '0.1.19-gj13'
    repositories {
        //优先maven本地仓库(url需指定为实际的本地仓库地址(必需指定到仓库顶层,不止到M2_HOME级,形如:D:\soft\maven\repo),亦可见于IDEA的Maven - Local repository,对动态版本无影响)


        // mavenCentral()
        mavenLocal()
        maven { url 'https://maven.aliyun.com/repository/public' }
        maven {
            allowInsecureProtocol = true
            url 'http://maven.cairenhui.com/nexus/content/repositories/crh_dev'
        }
        maven {
            allowInsecureProtocol = true
            url 'http://maven.cairenhui.com/nexus/content/repositories/thirdparty'
        }
        maven {
            allowInsecureProtocol = true
            url 'http://maven.cairenhui.com/nexus/content/repositories/3rd_gtja'
        }
    }

    tasks.withType(JavaCompile) {
        options.encoding = "UTF-8"
        // 添加参数名称编译的支持， 可以运行时获取
        options.compilerArgs << '-parameters'
        // 关闭编译警告
        options.compilerArgs << "-Xlint:-unchecked" << "-Xlint:-deprecation" << '-Xlint:-varargs'
    }

    jar {
        manifest {
            attributes('Manifest-Version': project.version,
                    'Implementation-Git-Version': getGitVersion(),
                    'Implementation-Time': buildTime("yyyy-MM-dd HH:mm"),
                    'Implementation-Title': project.name,
                    'Implementation-Vendor': '杭州财人汇网络股份有限公司',
                    'Implementation-Vendor-Id': 'CRH',
                    'Build-Author-Id': getAuthor(),
                    'Build-Author-Email': getAuthorEmail())
        }
    }

    test {
        useJUnitPlatform()
    }

    dependencies {
        compileOnly('org.projectlombok:lombok')
        annotationProcessor('org.projectlombok:lombok')
        annotationProcessor("org.springframework.boot:spring-boot-configuration-processor")
        testImplementation('org.junit.jupiter:junit-jupiter-api')
        testRuntimeOnly('org.junit.jupiter:junit-jupiter-engine')
        List<String> includes = branchVersionMap.get("includes")
        includes.forEach { String e -> api e }
    }

    configurations.all {
        List<String> excludes = branchVersionMap.get("excludes")
        excludes.forEach { String e ->
            String[] splits = e.split(":")
            exclude group: splits[0], module: splits[1]
        }
        resolutionStrategy {
            eachDependency { DependencyResolveDetails details ->
                String groupVersion = versionMap.get(details.requested.group);
                if (groupVersion != null && groupVersion != '') {
                    details.useVersion groupVersion;
                    return
                }
                String moduleVersion = versionMap.get(details.requested.group + ':' + details.requested.name)
                if (moduleVersion != null && moduleVersion != '') {
                    details.useVersion moduleVersion;
                    return
                }

            }

            cacheDynamicVersionsFor 0, 'seconds'
            cacheChangingModulesFor 0, 'seconds'
        }
        // 手动排除一些不需要的依赖
        exclude group: 'com.cairh', module: 'cpe-base-protocol-gateway-t2'

    }

    dependencyManagement {
        resolutionStrategy {
            cacheChangingModulesFor 0, 'seconds'
            cacheDynamicVersionsFor 0, 'seconds'
        }

        imports {
            mavenBom "com.cairh:cpe-starter-parent:0.3.161"
            mavenBom "com.cairh:cpe-framework-infra-starter-parent:0.3.161"
            mavenBom "com.cairh:cpe-base-protocol-gateway-starter-parent:0.0.163"
        }
        dependencies {
            // cpe infra
            dependency "com.cairh:cpe-rpc:0.3.162"
            dependency "com.cairh:cpe-auth:0.3.162"
            dependency 'com.cairh:cpe-base-protocol-gateway-t2-1x:0.0.163'
            // cpe counter
            dependency "com.cairh:cpe-counter-data-t2:0.4.224"
            dependency "com.cairh:cpe-counter-data-kingdom:0.4.224"
            dependency "com.cairh:cpe-counter-data-http:0.4.224"
            dependency "com.cairh:cpe-counter-data-kcbp:0.4.224"
            dependency "com.cairh:cpe-counter-data-xpe:0.4.224"
            dependency "com.cairh:cpe-counter-extension-hundsun:0.4.224"
            dependency "com.cairh:cpe-counter-extension-http:0.4.224"
            dependency "com.cairh:cpe-esb-archive-server-api:0.1.1"
            dependency "com.cairh:cpe-counter-data-ws:0.4.224"


            // cpe scatter
            dependency "com.cairh:cpe-job-core:0.0.1-beta.169"
            dependency "com.cairh:cpe-common-backend:0.1.57"
            dependency "com.cairh:cpe-common-sysparameter-pull:0.2.2"
            dependency "com.cairh:esb-base-data-api:0.0.1-beta.9"
            dependency 'com.cairh:cpe-esb-basedata-server-api:0.1.31-gt3'

            //T2
            dependency('com.gtja:jresplus-t2sdk-base:1.1.19')
            dependency('com.gtja:jresplus-t2sdk-ext:1.1.19')

            dependency 'com.tencentcloudapi:tencentcloud-sdk-java:3.1.416'
            dependency 'org.freemarker:freemarker:2.3.30'

            dependency 'com.deepoove:poi-tl:1.8.2'                    // 生成Word(依赖poi)
            dependency 'com.sun.media:jai-codec:1.1.3'
            dependency 'javax.media:jai-core:1.1.3'

            dependency 'com.ctrip.framework.apollo:apollo-client:2.1.0'
            dependency 'org.dom4j:dom4j:2.1.4'

            //filestore
            dependency 'io.minio:minio:8.2.1'
            dependency 'net.coobird:thumbnailator:0.4.8'
            dependency 'com.aliyun.oss:aliyun-sdk-oss:3.10.2'


            //xyzq mid sdk
            dependency 'com.xyzq.mid:mid-sdk:2023.10.12'
            dependency 'com.xyzq.mid:constants-starter:0.0.1'
            dependency 'com.xyzq.mid:utils-starter:0.0.2'
            dependency 'org.springframework.cloud:spring-cloud-starter-openfeign:3.1.7'


            // gtja
            dependency 'commons-lang:commons-lang:2.6'
            dependency 'com.gtja:ggzj-thrift:1.0.14'
            dependency 'com.gtja:ucas-thrift-cust:1.0.3'
            dependency 'com.gtja:ucas-thrift:1.0.8'
            dependency 'com.gtja:ucas-thrift-emp:1.0.0'
            dependency 'com.gtja:gtja-sg:2.5.1'
            dependency 'com.gtja:remote-api:2.5.0'
            dependency 'com.gtja:remote-thrift:2.5.0'
            dependency 'com.gtja:remote-zookeeper:2.5.0'
            dependency 'com.gtja:registry-api:2.5.1'
            dependency 'com.gtja:registry-zookeeper:2.5.0'
            dependency 'com.gtja:rpc-filter:2.5.0'
            dependency 'com.gtja:rpc-framework:2.5.0'
            dependency 'com.gtja:config-spring:2.5.0'
            dependency 'com.gtja:config-api:2.5.0'
            dependency 'com.gtja:service-common:2.5.0'
            dependency 'com.gtja:spjz-adapter:1.0.3'
            dependency 'com.gtja:zookeeper:3.4.7'
            dependency 'org.apache.curator:curator-client:4.1.0'
            dependency 'org.apache.curator:curator-framework:4.1.0'
            dependency 'org.apache.curator:curator-recipes:4.1.0'
            dependency 'com.gtja:govern-api:2.5.0'
            dependency 'com.gtja:govern-connpool:2.5.0'
            dependency 'com.gtja:govern-failover:2.5.0'
            dependency 'com.gtja:govern-loadbalance:2.5.0'
            dependency 'com.gtja:govern-rate-limit:2.5.0'
            dependency 'com.gtja:govern-rate-limit:2.5.0'
            dependency 'com.gtja:govern-router:2.5.0'
            dependency 'com.gtja:obc-common:0.0.1'
            dependency 'com.gtja:registry-backup:2.5.0'
            dependency 'com.gtja:registry-init:2.5.0'
            dependency 'com.gtja:registry-local:2.5.0'
            dependency 'com.gtja:sentinel-transport-common:1.8.1'
            dependency 'com.gtja:service-ext-plugin:2.5.0'
            dependency 'com.gtja:service-registry-backup-thrift:1.0.0'
            dependency 'com.gtja:service-startup:2.5.0'
            dependency 'com.gtja:sentinel-core:1.8.1'
            dependency 'com.gtja:sentinel-datasource-extension:1.8.1'
            dependency 'com.gtja:sentinel-parameter-flow-control:1.8.1'
            dependency 'com.gtja:sentinel-transport-common:1.8.1'

            // tongweb
            dependency 'com.gtja:tongweb-embed:7.0.E.2_P1'
            dependency 'com.gtja:tongweb-spring-boot-starter:2.x.0.RELEASE_P1'
        }
    }
}

task deleteRemoteJar {
    if (!project.hasProperty("remoteJar")) {
        return
    }
    artifactProjects.each {
        def command = "curl -v -u ${rootProject.nexus_username}:${rootProject.nexus_password} -X DELETE http://maven.cairenhui.com/nexus/content/repositories/crh_dev/com/cairh/${it.getName()}/${version}"
        println "command: ${command}"
        try {
            exec {
                ExecSpec execSpec ->
                    executable 'bash'
                    args '-c', command
            }
            println "deleteArtifacts success~"
        } catch (Exception e) {
            e.printStackTrace()
        }
    }
}

configure(artifactProjects) { project ->
    apply from: "$rootDir/gradle/publications.gradle"
}

def static buildTime(String time) {
    def date = new Date()
    def formattedDate = date.format(time)
    return formattedDate
}

def static getGitVersion() {
    return 'git rev-parse --short HEAD'.execute().text.trim()
}

def static getAuthor() {
    return 'git config user.name'.execute().text.trim()
}

def static getAuthorEmail() {
    return 'git config user.email'.execute().text.trim()
}

def static getVersionMap() {
    Map<String, String> versionMap = new HashMap<>();
    String filePath = "cpe-starter%2Fcpe-starter-parent%2Fbuild-global-jar.txt";
    URL url = new URL("http://git.cairenhui.com/api/v4/projects/1538/repository/files/" + filePath + "/raw?ref=development");
    HttpURLConnection connection = (HttpURLConnection) url.openConnection();
    connection.setRequestMethod("GET");
    connection.setRequestProperty("Private-Token", "k2dEGUVG32JYhcM5d82u");
    int responseCode = connection.getResponseCode();
    if (responseCode != HttpURLConnection.HTTP_OK) {
        return
    }
    BufferedReader reader = null;
    try {
        reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
        String line;
        while ((line = reader.readLine()) != null) {
            if (line == '' || line.trim() == '') {
                continue;
            }
            line = line.trim();
            if (line.contains("//")) {
                continue
            }
            if (line.contains('#')) {
                String[] str = line.split('#');
                if (str.length != 2) {
                    continue;
                }
                versionMap.put(str[0], str[1]);
            }
            if (line.contains(':')) {
                String[] str = line.split(':');
                if (str.length != 3) {
                    continue;
                }
                versionMap.put(str[0] + ':' + str[1], str[2]);
            }
        }
    } catch (IOException e) {
        e.printStackTrace();
    } finally {
        if (reader != null) {
            try {
                reader.close();
            } catch (IOException e1) {
                e1.printStackTrace();
            }
        }
    }
    println 'versionMap大小：' + versionMap.size()
    return versionMap
}

String newMavenVersion() {
    // 自增版本号末尾自行增加，如果想定义新的大版本，在这里自行定义
    List<String> taskNames = gradle.startParameter.taskNames
    println "当前执行的任务名称: ${taskNames}"
    if (taskNames.stream().anyMatch { x -> x.startsWith('publish') }
            && project.name != rootProject.name) {
        // 不是从根项目发起的publish任务，不允许
        throw new RuntimeException("请从根项目发起publish任务");
        // 如果你是要单独发一个自己测试用的版本，修改这里即可，但是注意不要影响正常使用的版本
        // return "";
    }
    try {
        def metadataUrl = "http://maven.cairenhui.com/nexus/content/repositories/crh_dev/com/cairh/cpe-esb-component-server-api/maven-metadata.xml"
        String metadataXml = new URL(metadataUrl).text
        Node metadataObj = new XmlParser().parseText(metadataXml)
        def highestVersion = metadataObj.versioning.versions.version
                .findAll { it.text().matches(/\d+\.\d+\.\d+/) }.max {
            def version = it.text()
            def split = version.split("\\.")
            def result = 0;
            for (def i = split.length - 1; i >= 0; i--) {
                result += Math.pow(10000, split.length - i - 1) * split[i].toInteger()
            }
            return result;
        }.text()
        println "highestVersion: $highestVersion"
        int idx = highestVersion.lastIndexOf(".");
        String prefix = highestVersion.substring(0, idx + 1);
        String suffix = highestVersion.substring(idx + 1);
        int lastV = suffix.toInteger() + 1;
        return "${prefix}${lastV}"
    } catch (Exception ignored) {
        throw new RuntimeException("获取maven-metadata自增版本异常了，请到调用处自行定义一个版本号吧");
    }
}

// 获取不同分支机构的不同jar包配置
static Map<String, List<String>> getBranchVersionMap() {
    // gradle build -DsecurityAlias=htzq
    def securityAlias = System.getProperty("securityAlias");
    println("当前券商：" + securityAlias)
    Map<String, List<String>> versionMap = new HashMap<>();
    List<String> excludes = new ArrayList<>()
    List<String> includes = new ArrayList<>()
    versionMap.put("excludes", excludes)
    versionMap.put("includes", includes)
    if (securityAlias == null || securityAlias == '') return versionMap
    String filePath = "cpe-starter%2Fcpe-starter-parent%2Fbranch-jar-config.txt";
    URL url = new URL("http://git.cairenhui.com/api/v4/projects/1538/repository/files/" + filePath + "/raw?ref=development");
    HttpURLConnection connection = (HttpURLConnection) url.openConnection();
    connection.setRequestMethod("GET");
    connection.setRequestProperty("Private-Token", "k2dEGUVG32JYhcM5d82u");
    int responseCode = connection.getResponseCode();
    if (responseCode != HttpURLConnection.HTTP_OK) return versionMap
    BufferedReader reader = null;
    try {
        reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
        String line;
        while ((line = reader.readLine()) != null) {
            if (line == '' || line.trim() == '') continue
            line = line.trim();
            if (line.contains("//")) continue
            if (line.contains('=')) {
                String[] str = line.split('=');
                if (str.length != 2) continue
                if (str[0].trim() == securityAlias + "." + "exclude") excludes.add(str[1].trim())
                if (str[0].trim() == securityAlias + "." + "include") includes.add(str[1].trim())
            }
        }
    } finally {
        if (reader != null) reader.close()
    }
    return versionMap
}





