package com.cairh.cpe.esb.component.csdc.dto.response.entity;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 功能说明: Csdcholder<br> 系统版本: v1.0<br> 开发人员: <AUTHOR> 开发时间: 2019年01月21日<br>
 */
@Data
public class Csdcholder implements Serializable {
    
    /**
     * 证件类别
     */
    private String id_kind;
    /**
     * 证件号码
     */
    private String id_no;
    /**
     * 客户姓名
     */
    private String client_name;
    /**
     * 一码通账号
     */
    private String acode_account;
    /**
     * 一码通状态 0正常 1注销
     */
    private String csdc_acode_status;
    /**
     * 账户类别 字典csdc_account_type
     */
    private String csdc_account_type;
    /**
     * 证券账户
     */
    private String stock_account;
    /**
     * 中登证券账户状态 字典csdc_holder_status
     */
    private String csdc_holder_status;
    /**
     * 开户方式 2 网上开户 1 见证开户 0 临柜开户
     */
    private String csdc_open_type;
    /**
     * 开户日期
     */
    private LocalDate open_date;
    /**
     * 证券账户开户代理机构名称
     */
    private String open_organization;
    /**
     * 销户日期
     */
    private LocalDate cancel_date;
    /**
     * 证券账户销户代理机构名称
     */
    private String cancel_organization;
    /**
     * 关联关系确认标识 0是 1否
     */
    private String relation_flag;
    /**
     * 关联关系确认机构名称
     */
    private String relation_organizatioin;
    /**
     * 不合格标志 对于沪市账户，如果为不合格账户，则标识为“1”；否则标识为“0”。对于深市账户，如被查询的证券账户在业务发起的开户代理机构处为不合格账户或被中国结算认定为不合格账户，则标识为“1”；否则标识为“0
     */
    private String unquali_flag;

    /**
     * 指定/托管关系标识yxzq使用
     * 00 无指定交易 01 指定机构一致 02 指定机构不一致
     */
    private String csdc_reg_flag;
}