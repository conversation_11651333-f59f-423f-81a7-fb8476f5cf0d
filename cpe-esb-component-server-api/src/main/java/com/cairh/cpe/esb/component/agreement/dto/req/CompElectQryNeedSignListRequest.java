package com.cairh.cpe.esb.component.agreement.dto.req;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@Accessors(chain = true)
public class CompElectQryNeedSignListRequest implements Serializable {
    private static final long serialVersionUID = -999251244896391175L;
    private String subsys_no;
    private String organ_flag;

    private String agreement_type;
    private String busin_type;
    private String regular_data;
}
