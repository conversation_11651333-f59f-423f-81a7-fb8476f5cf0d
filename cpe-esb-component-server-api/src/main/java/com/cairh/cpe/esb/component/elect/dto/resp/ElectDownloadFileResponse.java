package com.cairh.cpe.esb.component.elect.dto.resp;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@Accessors(chain = true)
public class ElectDownloadFileResponse implements Serializable {
    private static final long serialVersionUID = 3963163842657917887L;
    /**文件流*/
    @NotNull
    private byte[] file;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文件路径
     */
    private String filePath;

    private String http_url;
}
