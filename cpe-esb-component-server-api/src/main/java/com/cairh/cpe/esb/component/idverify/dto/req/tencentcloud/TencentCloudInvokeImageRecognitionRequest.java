package com.cairh.cpe.esb.component.idverify.dto.req.tencentcloud;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 腾讯云-特化 高清人像比对公安认证 request
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class TencentCloudInvokeImageRecognitionRequest implements Serializable {

    private static final long serialVersionUID = -4790447940872159429L;

    /**
     * 身份证号
     */
    private String id_card;

    /**
     * 姓名。中文请使用UTF-8编码。
     */
    private String name;

    /**
     * 用于人脸比对的照片，图片的Base64值；
     Base64编码后的图片数据大小不超过3M，仅支持jpg、png格式。
     请使用标准的Base64编码方式(带=补位)，编码规范参考RFC4648。
     */
    private String image_base64;
}
