package com.cairh.cpe.esb.component.idverify.dto.req;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc 手机实名认证入参
 * @date 2022-05-12
 */
@Data
public class VerifyMobileRequest implements Serializable {

    private static final long serialVersionUID = 4684191050148153434L;
    /**
     * 证件类别
     */
    @NotNull(message = "证件类别不能为空")
    private String id_kind;

    /**
     * 证件号码
     */
    @NotNull(message = "证件号码不能为空")
    private String id_no;

    /**
     * 用户姓名
     */
    @NotNull(message = "用户姓名不能为空")
    private String full_name;

    /**
     * 营业部编号 调用方如果没有营业部编号，传0也可以
     */
    private String branch_no="0";

    /**
     * 实时标志 默认为0。传入1时实时调用底层公安接口，不取本地数据
     */
    private String realtime_flag;

    /**
     * 业务类别  01：手机号码当前状态查询  02：手机号码与客户名称对应关系核查03：手机号码与身份证明文件对应关系核查
     * 默认值 03
     */
    private String csdc_busi_kind = "03";

    /**
     * 手机号
     */
    @NotNull(message = "手机号不能为空")
    private String mobile_tel;

    private String subsys_id;

    /**
     * 服务厂商定制, 非业务参数
     */
    private String service_vender;

    /**
     * 操作员营业部编号
     */
    private String op_branch_no;

    /**
     * 操作员营业部名称
     */
    private String op_branch_name;

    /**
     * 用户营业部名称
     */
    private String branch_name;

    /**
     * 操作员编号
     */
    private String operator_no;

    /**
     * 操作员姓名
     */
    private String operator_name;
}