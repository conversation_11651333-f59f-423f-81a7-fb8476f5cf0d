package com.cairh.cpe.esb.component.suit.dto.resp;


import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 *
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SuitMatchRecordResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private String serial_id;

    /**
     * 客户编号
     */
    private String client_id;
    /**
     * 客户类型
     */
    private String client_type;
    /**
     * 客户姓名
     */
    private String client_name;
    /**
     * 机构标志
     */
    private String organ_flag;
    /**
     * 证件类别
     */
    private String id_kind;
    /**
     * 证件号码
     */
    private String id_no;
    /**
     * 产品TA编号
     */
    private String prodta_no;
    /**
     * 产品代码
     */
    private String prod_code;
    /**
     * 产品名称
     */
    private String prod_name;
    /**
     * 产品风险等级
     */
    private String risk_level;

    /**
     * 产品投资品种
     */
    private String invest_kind;

    /**
     * 产品投资期限
     */
    private String invest_term;
    /**
     * 操作时间
     */
    private String operator_time;
    /**
     * 操作员
     */
    private String operator_name;
    /**
     * 操作员编号
     */
    private String operator_no;
    /**
     * 适当性匹配标志（0:匹配 1:警示 2:禁止）
     */
    private String suit_flag;

    /**
     * 客户风险等级
     */
    private String corp_risk_level;
    /**
     * 风险等级匹配标志
     */
    private String risk_level_suit_flag;

    /**
     * 客户拟投资品种
     */
    private String en_invest_kind;
    /**
     * 投资品种匹配标志
     */
    private String invest_kind_suit_flag;

    /**
     * 客户拟投资期限
     */
    private String en_invest_term;
    /**
     * 投资期限匹配标志
     */
    private String invest_term_suit_flag;

}
