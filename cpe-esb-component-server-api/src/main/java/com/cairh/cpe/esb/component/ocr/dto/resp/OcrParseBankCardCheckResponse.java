package com.cairh.cpe.esb.component.ocr.dto.resp;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class OcrParseBankCardCheckResponse implements Serializable {
    private ErrorInfo errorInfo;
    //核验结果
    private Boolean verify_result;
    //核验结果描述
    private String description;

    @Data
    public static class ErrorInfo {
        private int code;
        private String message;
        private List<Details> details;

        // Getters and setters
    }

    @Data
    public static class Details {
        private String type;
        private int reason;
        private String message;

        // Getters and setters
    }
}
