package com.cairh.cpe.esb.component.idverify.dto.resp.tencentcloud;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 腾讯云-特化 核身鉴权 response
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class TencentCloudInvokeDetectAuthResponse implements Serializable {

    /**
     * 发起核身流程的地址
     *
     * 仅适用微信h5场景
     */
    private String url;

    /**
     * 核身标识
     *
     * 有效时间为2小时, 用于获取验证结果信息
     */
    private String biz_token;

    /**
     * 请求标识
     *
     * 每次请求都会返回
     */
    private String request_id;
}
