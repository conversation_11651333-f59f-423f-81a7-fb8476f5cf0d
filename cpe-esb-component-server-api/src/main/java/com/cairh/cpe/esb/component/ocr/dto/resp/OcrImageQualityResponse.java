package com.cairh.cpe.esb.component.ocr.dto.resp;

import lombok.Data;

import java.io.Serializable;

@Data
public class OcrImageQualityResponse implements Serializable {

    private static final long serialVersionUID = 4876094385466854218L;
    /**    /**
     * 质量类型 [0:无，1：复印件，2：拍屏，3：假证件，4：有水印，5：遮挡，6：切边，7：卡变形，
     *         8:有光斑 9:临时身份证 10:模糊 11:非二代身份证 12:身份证图片颠倒 13:信息不合法
     *         14:卡片正反面参数与实际传入图片不匹配 ]
     */
    private String quality_kind;

    /** 质量描述*/
    private String  quality_desc;

}
