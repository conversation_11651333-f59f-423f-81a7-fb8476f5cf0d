package com.cairh.cpe.esb.component.video.dto.req;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * comp.video.joinQueue(详见http://cf.cairenhui.com/display/DevelopKS/comp.video.joinQueue)
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class VideoJoinQueueRequest implements Serializable {

    private static final long serialVersionUID = 2592073248894390268L;
    /**
     * 系统编号 Y
     */
    @NotNull
    private String subsys_no;

    /**
     * 业务类型 Y
     */
    @NotNull
    private String busin_type;

    /**
     * 业务名称 Y
     */
    @NotNull
    private String busin_name;

    /**
     * 系统编号 Y
     */
    @NotNull
    private String organ_flag;

    /**
     * 客户端类型 Y
     */
    @NotNull
    private String app_id;

    /**
     * 客户营业部 Y
     */
    @NotNull
    private String branch_no;

    /**
     * 姓名 Y
     */
    @NotNull
    private String user_name;

    /**
     * 证件类型 Y
     */
    @NotNull
    private String id_kind;

    /**
     * 证件号码 Y
     */
    @NotNull
    private String id_no;

    /**
     * 客户号 N
     */
    private String client_id;

    /**
     * 资金账号 N
     */
    private String fund_account;

    /**
     * 手机号 N
     */
    private String mobile_tel;

    /**
     * 视频类型 Y
     */
    @NotNull
    private String video_type;

    /**
     * 视频优先级 N
     */
    private Integer video_level;

    /**
     * 视频厂商 N
     */
    private String service_vender;

    /**
     * 业务流水号(需保证唯一) Y
     */
    @NotNull
    private String unique_id;

    /**
     * 跳转页面(joinQueue时必传,不再由配置取得)
     */
    private String page_addr;

    /**
     * 请求流水id
     */
    private String request_id;

    /**
     * 渠道号
     */
    private String channel_code;
    private String channel_name;
    /**
     * 经纪人编号
     */
    private String broker_code;

    /**
     * 补录
     */
    private String additional;

    /**
     * 客户性别信息
     */
    private String user_gender;

    /**
     * 网厅业务办理-开户类别
     */
    private String client_category;

    /**
     * 网厅业务办理-经办人姓名
     */
    private String agent_name;

    /**
     * 网厅业务办理-经办人证件类型
     */
    private String agent_id_kind;

    /**
     * 网厅业务办理-经办人证件号码
     */
    private String agent_id_no;
}
