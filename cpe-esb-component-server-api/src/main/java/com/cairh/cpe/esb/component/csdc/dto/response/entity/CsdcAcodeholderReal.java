package com.cairh.cpe.esb.component.csdc.dto.response.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * 中登返回一码通数据真实实体
 */
@Data
public class CsdcAcodeholderReal implements Serializable {

    private static final long serialVersionUID = 3279193351718374670L;
    /**
     * 业务流水号
     */
    private String ywlsh;
    /**
     * 客户名称
     */
    private String khmc;
    /**
     * 主要身份证明文件类别
     */
    private String zjlb;
    /**
     * 主要身份证明文件代码
     */
    private String zjdm;
    /**
     * 一码通账户号码
     */
    private String ymth;
    /**
     * 一码通账户状态
     */
    private String ymtzt;
    /**
     * 一码通账户开户方式
     */
    private String khfs;
    /**
     * 一码通账户开户日期
     */
    private String khrq;
    /**
     * 一码通账户开户代理机构名称
     */
    private String khjgmc;
    /**
     * 一码通账户销户日期
     */
    private String xhrq;

    /**
     * 一码通账户销户代理机构名称
     */
    private String xhjgmc;

    /**
     * 业务发起开户代理机构代码
     */
    private String khjgdm;

    /**
     * 业务发起开户代理网点代码
     */
    private String khwddm;

    /**
     * 申请日期
     */
    private String sqrq;

    /**
     * 业务日期
     */
    private String ywrq;

    /**
     * 业务凭证报送标识
     */
    private String ywpzbs;

    /**
     * 结果代码
     */
    private String jgdm;

    /**
     * 结果说明
     */
    private String jgsm;
}