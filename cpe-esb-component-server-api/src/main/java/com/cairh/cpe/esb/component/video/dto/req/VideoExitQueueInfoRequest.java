package com.cairh.cpe.esb.component.video.dto.req;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * comp.video.exitQueueInfo(详见:http://cf.cairenhui.com/display/DevelopKS/comp.video.exitQueueInfo)
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class VideoExitQueueInfoRequest implements Serializable {

    private static final long serialVersionUID = 3206655205206968058L;
    /**
     * 系统编号 Y
     */
    @NotNull
    private String subsys_no;

    /**
     * 业务流水号(需保证唯一) Y
     */
    @NotNull
    private String unique_id;

    /**
     * 姓名 Y
     */
    private String user_name;

    /**
     * 证件类型 Y
     */
    private String id_kind;

    /**
     * 证件号码 Y
     */
    private String id_no;

    /**
     * 客户号 N
     */
    private String client_id;

    /**
     * 资金账号 N
     */
    private String fund_account;

    /**
     * 手机号 N
     */
    private String mobile_tel;

}
