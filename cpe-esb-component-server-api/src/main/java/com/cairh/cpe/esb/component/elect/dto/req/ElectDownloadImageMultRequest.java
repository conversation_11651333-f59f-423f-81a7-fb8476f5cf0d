package com.cairh.cpe.esb.component.elect.dto.req;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@Accessors(chain = true)
public class ElectDownloadImageMultRequest implements Serializable {

    private static final long serialVersionUID = 8330207926504978368L;
    /**总文件记录ID*/
    private String filerecord_id;

    /** 起始页数 **/
    private Integer begin_page;

    /** 下载页数 **/
    private Integer down_page_num;

    /** 返回文件类型 0：返回base64_images出参， 类型为1：返回http_urls出参 ，默认返回base64_images出参**/
    private String return_file_type;

    /**
     * 服务厂商定制, 非业务参数
     */
    private String service_vender;
}
