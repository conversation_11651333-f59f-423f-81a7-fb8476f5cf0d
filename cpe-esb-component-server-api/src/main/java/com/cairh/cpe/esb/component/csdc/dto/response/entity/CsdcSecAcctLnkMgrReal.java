package com.cairh.cpe.esb.component.csdc.dto.response.entity;

import lombok.Data;

import java.io.Serializable;

/**
 中登证券账户关联关系维护真实实体 */
@Data
public class CsdcSecAcctLnkMgrReal implements Serializable {

    private static final long serialVersionUID = 1483515542171177410L;
	/**
	 * 数据体
	 *[{
	 *
	 * YWLSH: "" 业务流水号
	 * YWLB: "" 业务类别
	 * YMTH: "" 一码通账户号码
	 * XYMTH: "" 新一码通账户号码
	 * ZHLB: "" 证券账户类别
	 * ZQZH: "" 证券账户号码
	 * ZJLB: "" 主要身份证明文件类别
	 * ZJDM: "" 主要身份证明文件代码
	 * GLGXBS: "" 关联关系确认标识
	 * KHJGDM: "" 业务发起开户代理机构代码
	 * KHWDDM: "" 业务发起开户代理网点代码
	 * SQRQ: "" 申请日期
	 * YWRQ: "" 业务日期
	 * YWPZBS: "" 业务凭证报送标识
	 * JGDM: "" 结果代码
	 * JGSM: "" 结果说明
	 *
	 * }]
	 */
    /**
     * 业务流水号
     */
    private String ywlsh;

	/**
	 * 业务类别
	 */
	private String ywlb;

	/**
	 * 一码通账户号码
	 */
	private String ymth;

	/**
	 * 新一码通账户号码
	 */
	private String xymth;

	/**
	 * 证券账户类别
	 */
	private String zhlb;

	/**
	 * 证券账户号码
	 */
	private String zqzh;

	/**
	 * 主要身份证明文件类别
	 */
	private String zjlb;

	/**
	 * 主要身份证明文件代码
	 */
	private String zjdm;

	/**
	 * 关联关系确认标识
	 */
	private String glgxbs;

	/**
	 * 业务发起开户代理机构代码
	 */
	private String khjgdm;

	/**
	 * 业务发起开户代理网点代码
	 */
	private String khwddm;

	/**
	 * 申请日期
	 */
	private String sqrq;

	/**
	 * 业务日期
	 */
	private String ywrq;

	/**
	 * 业务凭证报送标识
	 */
	private String ywpzbs;

	/**
	 * 结果代码
	 */
	private String jgdm;

	/**
	 * 结果说明
	 */
	private String jgsm;
}