package com.cairh.cpe.esb.component.business.dto.resp;

import lombok.Data;

import java.io.Serializable;

/**
 * 账户标签信息查询
 */
@Data
public class BusinessQueryAccountLabelInfoResp implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 账户标签
     */
    private String gt_acctlabel;

    /**
     * 账户标签-名称
     */
    private String gt_acctlabel_name;

    /**
     * 账户二级标签
     */
    private String gt_sec_acctlabel;

    /**
     * 账户二级标签-名称
     */
    private String gt_sec_acctlabel_name;

    /**
     * 交易日期
     */
    private String init_date;

    /**
     * 起始日期
     */
    private String begin_date;

    /**
     * 到期日期
     */
    private String end_date;

    /**
     * 备注
     */
    private String remark;

    /**
     * 定位串
     */
    private String position_str;
}
