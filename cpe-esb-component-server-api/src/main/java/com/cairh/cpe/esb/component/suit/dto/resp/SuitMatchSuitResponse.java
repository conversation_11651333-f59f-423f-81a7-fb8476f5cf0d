package com.cairh.cpe.esb.component.suit.dto.resp;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * comp.suit.matchSuit(详见:http://cf.cairenhui.com/display/DevelopKS/comp.suit.matchSuit)
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SuitMatchSuitResponse implements Serializable {

    private static final long serialVersionUID = -679371593578014030L;
    //适当性匹配标志
    private String suit_flag;

    //客户风险等级
    private String corp_risk_level;

    //产品风险等级
    private String risk_level;

    //风险等级匹配标志
    private String risk_level_suit_flag;

    //客户拟投资品种
    private String en_invest_kind;

    //产品投资品种
    private String invest_kind;

    //投资品种匹配标志
    private String invest_kind_suit_flag;

    //客户拟投资期限
    private String en_invest_term;

    //产品投资期限
    private String invest_term;

    //投资期限匹配标志
    private String invest_term_suit_flag;

    //客户允许最大亏损率
    private BigDecimal en_maxdeficit_rate;

    //产品最大亏损率
    private BigDecimal max_deficit_rate;

    //最大亏损率匹配标志
    private String max_deficit_rate_suit_flag;

    //客户收益类型
    private String client_income_type;

    //产品收益类型
    private String income_type;

    //收益类型匹配标志
    private String income_type_suit_flag;

    //产品名称
    private String prod_name;

    //产品预期收益
    private BigDecimal prodpre_income;

    //专业投资者标志
    private String prof_flag;

    //可操作标志
    private String enable_flag;
}
