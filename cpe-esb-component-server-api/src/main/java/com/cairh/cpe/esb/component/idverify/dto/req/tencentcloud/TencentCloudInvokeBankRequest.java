package com.cairh.cpe.esb.component.idverify.dto.req.tencentcloud;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 腾讯云-特化 银行卡三要素核验 request
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class TencentCloudInvokeBankRequest implements Serializable {

    /**
     * 开户证件号，与CertType参数的证件类型一致，如：身份证，则传入身份证号。 Y
     */
    @NotBlank
    private String id_card;

    /**
     * 姓名 Y
     */
    @NotBlank
    private String name;

    /**
     * 银行卡号 Y
     */
    @NotBlank
    private String bank_card;

    /**
     * 证件类型，请确认该证件为开户时使用的证件类型，未用于开户的证件信息不支持验证。 N
     * 目前默认：0 身份证，其他证件类型暂不支持。
     */
    private Long cert_type;

}
