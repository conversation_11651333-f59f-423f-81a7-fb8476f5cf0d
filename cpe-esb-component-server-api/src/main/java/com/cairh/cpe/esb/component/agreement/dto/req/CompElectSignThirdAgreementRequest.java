package com.cairh.cpe.esb.component.agreement.dto.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022-11-10
 */
@Data
public class CompElectSignThirdAgreementRequest implements Serializable {

    private static final long serialVersionUID = -694136791081491013L;

    @NotBlank(message = "invalid parameter agreementsign_id")
    private String agreementsign_id;//必传

    private String op_entrust_way;

    private String op_password;

    private String op_station;

    private String branch_no;

    // 客户编号
    private String client_id;

    // 资产账户
    private String fund_account;

    private String organ_flag;

    private String mobile_tel;

    private String mobile_code;

    private String client_ip;//必传

    private String finance_type;

    /**
     * 受理单编号
     */
    private String acpt_id;

    /**
     * 外部流水号
     */
    private String ext_serial_no;

    private String op_branch_no;

    private String prod_type;

    private String replace_str;

    private String prodta_no;

    private String prod_code;

    /**
     * 压感信息数据
     */
    private String pressure_data;

    /**
     * 客户签名图片渲染标志
     */
    private String draw_sign_data_flag;

    /**
     * 客户签名图片
     */
    private String sign_data;

    /**
     * 重签次数
     */
    private Integer resign_count;

    /** 天威实现类必传*/
    private ItrusSignData itrusData;

    /**
     * 光大专用 强制签署标志
     */
    private String force_sign_flag;
}