package com.cairh.cpe.esb.component.agreement.dto.resp;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class CompQueryElectAgreeModelResponse implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 协议模板id
     */
    private String serial_id;

    /**
     * 协议编号
     */
    private String agreement_no;

    /**
     * 协议名称
     */
    private String agreement_name;

    /**
     * 协议版本
     */
    private String agreement_version;

    /**
     * 拓展名称
     */
    private String ex_name;

    /**
     * 协议类型
     */
    private String agreement_type;

    /**
     * 协议内容
     */
    private String agreement_content;

    /**
     * 正文类别
     */
    private String agreement_file_type;

    /**
     * 协议文件ID
     */
    private String agreement_file_id;

    /**
     * 协议状态
     */
    private String agreement_status;

    /**
     * 加密类型
     */
    private String encrypt_type;

    /**
     * 加密值
     */
    private String encrypt_content;

    /**
     * 发布人
     */
    private String create_by;

    /**
     * 创建日期时间(申请时间查询条件)
     */
    private Date create_datetime;

    private String modify_by;

    /**
     * 修改时间
     */
    private Date modify_datetime;

    /**
     * 生效日期
     */
    private Date effective_datetime;

    /**
     * 失效日期
     */
    private Date invalid_datetime;

    private String seal_pos;

    private String sign_pos;

    /**
     * 来源标志(外部接入方式)
     */
    private String third_source_type;

    /**
     * 外部协议信息
     */
    private String ex_param;

    /**
     * 归历史标志
     */
    private String tohis_flag;

    /**
     * 归历史时间
     */
    private Date tohis_datetime;

    /**
     * 签署方式
     */
    private String agreement_sign_type;

    /**
     * 三方协议模板id
     */
    private String third_agreement_id;

    /**
     * 三方协议信息串
     */
    private String third_agreement_str;

    /**
     * 是否抄写
     */
    private String transcribe_flag;

    /**
     * 抄写内容
     */
    private String transcribe_content;

    /**
     * 清算日期
     */
    private Integer date_clear;

    /**
     * 审核状态 0（待审核），1（审核通过），2（审核不通过）
     */
    private String verify_status;

    /**
     * 审核操作人  保存审核人，如审核驳回保存审核驳回人工号
     */
    private String verify_by;

    /**
     * 审核操作时间 保存审核，或驳回时间
     */
    private Date verify_datetime;

    /**
     * 总页数，用于保存pdf转图片页数
     */
    private Integer page_num;

    /**
     * 打回原因
     */
    private String reject_reason;
}
