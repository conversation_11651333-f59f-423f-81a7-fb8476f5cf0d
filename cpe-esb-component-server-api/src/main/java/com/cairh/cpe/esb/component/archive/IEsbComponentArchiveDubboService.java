package com.cairh.cpe.esb.component.archive;

import com.cairh.cpe.esb.component.archive.dto.req.CopyToNewPath4FilerecordIdsRequest;
import com.cairh.cpe.esb.component.archive.dto.req.CopyToNewPathRequest;
import com.cairh.cpe.esb.component.archive.dto.req.MergeToTiffRequest;
import com.cairh.cpe.esb.component.archive.dto.req.QyrFileRecordRequest;
import com.cairh.cpe.esb.component.archive.dto.resp.CopyToNewPath4FilerecordIdsResponse;
import com.cairh.cpe.esb.component.archive.dto.resp.CopyToNewPathResponse;
import com.cairh.cpe.esb.component.archive.dto.resp.MergeToTiffResponse;
import com.cairh.cpe.esb.component.archive.dto.resp.QyrFileRecordResponse;

public interface IEsbComponentArchiveDubboService {

	/**
	 * 通过FilerecordId拷贝文件，多个用逗号分开
	 * @param request
	 * @return
	 */
    public CopyToNewPath4FilerecordIdsResponse copyToNewPath4FilerecordIds(CopyToNewPath4FilerecordIdsRequest request);


        /**
		 * 查询文件基本信息
		 * @param request
		 * @return
		 */
    QyrFileRecordResponse qyrFileRecord(QyrFileRecordRequest request);

    /**
     * 拷贝文件到新路径
     * @param request
     * @return
     */
    CopyToNewPathResponse copyToNewPath(CopyToNewPathRequest request);

    /**
     * 合成tiff
     * @param request
     * @return
     */
    MergeToTiffResponse mergeToTiff(MergeToTiffRequest request);

}
