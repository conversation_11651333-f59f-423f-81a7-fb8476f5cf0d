package com.cairh.cpe.esb.component.agreement.dto.req;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
@Accessors(chain = true)
public class CompElectSignAgreeRequest implements Serializable {
    private static final long serialVersionUID = 5605201985203961635L;
    @NotBlank(message = "invalid parameter: elecagreemodel_id")
    private String elecagreemodel_id;
    private String render_data;
    /*1-异步，0-同步 默认异步*/
    private String async_flag;
}
