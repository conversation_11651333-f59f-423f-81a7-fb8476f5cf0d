package com.cairh.cpe.esb.component.notice.dto.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 	短信验证码发送入参(详情:http://cf.cairenhui.com/display/DevelopKS/comp.notice.sendSms)
 *
 */
@Data
public class NoticeSendSmsRequest implements Serializable {
    private static final long serialVersionUID = -1198826531937558877L;
    /**手机号码*/
    @NotBlank(message = "invalid parameter mobile_tel")
    private String mobile_tel;
    /**短信内容*/
    private String msg_content;
    /**短信模板编号*/
    private String msg_model_no;
    /**模板渲染数据 以JSON形式传递*/
    private String render_data;
    /**短信方式 0 短信 1 语音 2 邮件  3 微信 4 app推送*/
    @NotBlank(message = "invalid parameter send_type")
    private String send_type;
    /**渠道来源*/
    @NotBlank(message = "invalid parameter channel_type")
    private String channel_type;
    /**备注，建议业务系统调用传业务ID或系统编号，否则不支持细化统计*/
    private String remark;
    /**
     * 服务厂商定制, 非业务参数
     */
    private String service_vender;

    /** 业务类型 **/
    private String busin_type;

    /** 子业务类型 **/
    private String sub_busin_type;
    /** 分公司 **/
    private String up_branch_no;
    /** 营业部 **/
    private String branch_no;
}
