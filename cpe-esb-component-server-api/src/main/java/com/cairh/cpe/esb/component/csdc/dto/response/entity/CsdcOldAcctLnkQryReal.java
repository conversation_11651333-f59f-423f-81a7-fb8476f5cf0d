package com.cairh.cpe.esb.component.csdc.dto.response.entity;

import lombok.Data;

import java.io.Serializable;

/**
 中登存量账户关联关系报送信息查询真实实体 */
@Data
public class CsdcOldAcctLnkQryReal implements Serializable {

    private static final long serialVersionUID = 1483515542171177410L;

    /**
     * 业务流水号
     */
    private String ywlsh;

    /**
	 * 一码通账户号码
     */
    private String ymth;

	/**
	 * 证券账户类别
	 */
	private String zhlb;

	/**
	 * 证券账户号码
	 */
	private String zqzh;

	/**
	 * 客户名称
	 */
	private String khmc;

	/**
	 * 主要身份证明文件类别
	 */
	private String zjlb;

	/**
	 * 主要身份证明文件代码
	 */
	private String zjdm;

	/**
	 * 业务发起开户代理机构代码
	 */
	private String khjgdm;

	/**
	 * 业务发起开户代理网点代码
	 */
	private String khwddm;

	/**
	 * 申请日期
	 */
	private String sqrq;

	/**
	 * 委托交易券商名称
	 */
	private String khjgmc;

	/**
	 * 关联关系是否报送
	 */
	private String glbs;

	/**
	 * 业务日期
	 */
	private String ywrq;

	/**
	 * 业务凭证报送标识
	 */
	private String ywpzbs;

	/**
	 * 结果代码
	 */
	private String jgdm;

	/**
	 * 结果说明
	 */
	private String jgsm;
}