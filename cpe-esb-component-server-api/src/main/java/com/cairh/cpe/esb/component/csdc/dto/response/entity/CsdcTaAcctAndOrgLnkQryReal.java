package com.cairh.cpe.esb.component.csdc.dto.response.entity;

import lombok.Data;

import java.io.Serializable;

/**
 中登TA账户与基金销售机构关联关系查询真实实体 */
@Data
public class CsdcTaAcctAndOrgLnkQryReal implements Serializable {

	private static final long serialVersionUID = -7904900170837923053L;
	/**
	 * 数据体
	 *[{
	 * YWLSH: "" 业务流水号
	 * TAZHLB: "": TA账户类别
	 * TAZH: "": TA账户号码
	 * KHJGDM: "" 业务发起开户代理机构代码
	 * KHWDDM: "" 业务发起开户代理网点代码
	 * SQRQ: "" 申请日期
	 * BYZD: "" 备用字段
	 * XSJGMC: "" 基金销售 机构名称
	 * JGDM: "" 结果代码
	 * JGSM: "" 结果说明
	 * }]
	 */
    /**
     * 业务流水号
     */
    private String ywlsh;

	/**
	 * TA账户类别
	 */
	private String tazhlb;

	/**
	 * TA账户号码
	 */
	private String tazh;

	/**
	 * 业务发起开户代理机构代码
	 */
	private String khjgdm;

	/**
	 * 业务发起开户代理网点代码
	 */
	private String khwddm;

	/**
	 * 申请日期
	 */
	private String sqrq;

	/**
	 * 备用字段
	 */
	private String byzd;

	/**
	 * 基金销售 机构名称
	 */
	private String xsjgmc;

	/**
	 * 结果代码
	 */
	private String jgdm;

	/**
	 * 结果说明
	 */
	private String jgsm;
}