package com.cairh.cpe.esb.component.idverify.dto.req.tencentcloud;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 腾讯云-特化 获取核身信息 request
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class TencentCloudInvokeGetDetectInfoRequest implements Serializable {

    /**
     * 核身标识 Y
     *
     * 由核身鉴权返回
     */
    private String biz_token;

    /**
     * 客户场景 N
     *
     * 同核身鉴权保持一致, 不传则走配置
     */
    private String rule_id;

    /**
     * 拉取信息指定 N
     *
     * 取值(0:全部,1:文本类,2:身份证正反面,3:视频最佳截图照片,4:视频), 不传则走配置
     */
    private String info_type;
}
