package com.cairh.cpe.esb.component.elect.dto.resp;

import com.cairh.cpe.esb.component.elect.dto.req.ThirdFileInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 第三方文件上传结果
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ThirdFileUploadResult extends ThirdFileInfo {

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 上传失败时的失败原因
     */
    private String failCause;


    public static ThirdFileUploadResult success(ThirdFileInfo info) {
        ThirdFileUploadResult result = new ThirdFileUploadResult();
        result.setSuccess(true);
        result.setFile_no(info.getFile_no());
        result.setFilerecord_ids(info.getFilerecord_ids());
        return result;
    }

    public static ThirdFileUploadResult fail(ThirdFileInfo info, String cause) {
        ThirdFileUploadResult result = new ThirdFileUploadResult();
        result.setSuccess(false);
        result.setFailCause(cause);
        result.setFile_no(info.getFile_no());
        result.setFilerecord_ids(info.getFilerecord_ids());
        return result;
    }

}
