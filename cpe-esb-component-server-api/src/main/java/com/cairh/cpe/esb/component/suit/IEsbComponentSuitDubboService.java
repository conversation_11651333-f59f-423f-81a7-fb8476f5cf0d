package com.cairh.cpe.esb.component.suit;

import com.cairh.cpe.esb.component.suit.dto.req.SuitMatchRecordRequest;
import com.cairh.cpe.esb.component.suit.dto.req.SuitMatchSuitRequest;
import com.cairh.cpe.esb.component.suit.dto.resp.SuitMatchRecordResponse;
import com.cairh.cpe.esb.component.suit.dto.resp.SuitMatchSuitResponse;

import java.util.List;

/**
 * comp.suit.matchSuit(详见:http://cf.cairenhui.com/display/DevelopKS/comp.suit.matchSuit)
 *
 * <AUTHOR>
 */
public interface IEsbComponentSuitDubboService {

    /**
     * 适当性匹配
     *
     * @param request {@link SuitMatchSuitRequest}
     * @return 匹配结果
     */
    SuitMatchSuitResponse suitMatchSuit(SuitMatchSuitRequest request);

    List<SuitMatchRecordResponse> suitMatchRecordPage(SuitMatchRecordRequest recordRequest);
}
