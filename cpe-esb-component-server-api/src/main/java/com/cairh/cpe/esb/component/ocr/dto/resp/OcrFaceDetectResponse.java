package com.cairh.cpe.esb.component.ocr.dto.resp;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class OcrFaceDetectResponse implements Serializable {
    private static final long serialVersionUID = -1986050359834182753L;
    /**
     * 照片可信度分数 0~1	是
     */
    private String score;

    /**
     * 照片可信度分数阈值 0~1	是
     */
    private String pass_score;

    /**
     * 比对结果	是	1：通过 0：不通过
     */
    private String detect_result;

    /**
     * 人脸数量
     */
    private String count;

    /**
     * 人脸坐标位置 列表
     */
    private List<FacePosition> facePositions;


    @Data
    public static final class FacePosition implements Serializable {

        /**
         * 左上 横坐标
         */
        private Integer coord_x;

        /**
         * 左上 纵坐标
         */
        private Integer coord_y;

        /**
         * 长度
         */
        private Integer width;

        /**
         * 高度
         */
        private Integer height;
    }
}
