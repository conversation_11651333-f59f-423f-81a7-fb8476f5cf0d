package com.cairh.cpe.esb.component.elect.dto.req;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@Accessors(chain = true)
public class ElectDownloadImageRequest implements Serializable {

    private static final long serialVersionUID = 8330207926504978368L;
    /**文件记录ID*/
    @NotNull
    private String filerecord_id;

    /**
     * 服务厂商定制, 非业务参数
     */
    private String service_vender;
}
