package com.cairh.cpe.esb.component.risk.dto.req;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Map;

@Data
public class ExamQryExamQuestionsRequest implements Serializable {
    private static final long serialVersionUID = -7318475926694650816L;
    @NotNull
    private String paper_type;
    private String sub_paper_type;
    @NotNull
    private String organ_flag;
    private String render_data;

    /**
     * 客户编号
     */
    private String client_id;

    /**
     * 扩展参数
     * 金正直销柜台参数：ltoken， device_type， ip_address， mac_address， device_id， uuid， imei
     */
    private Map<String, Object> ext_params;

    /**
     * 试题分组
     */
    private String question_group;

    /**
     * 2 -> 随机返回，携带默认组；1 -> 随机返回，不携带默认组； 0 -> 不随机
     */
    private String random_group;

    /**
     * 产品TA编号
     */
    private String prodta_no;

    /**
     * 规则表达式
     */
    private String regular_expre;
}
