package com.cairh.cpe.esb.component.archive.dto.req;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-03-24
 */
@Data
public class MergeToTiffRequest implements Serializable {

    private static final long serialVersionUID = -7894436637730184616L;

    /**
     * 图片文件id列表
     */
    @NotEmpty(message = "invalid parameter filerecord_id_list")
    private List<String> filerecord_id_list;
}