package com.cairh.cpe.esb.component.notice;

import com.cairh.cpe.esb.component.notice.dto.req.*;
import com.cairh.cpe.esb.component.notice.dto.resp.NoticeSendEmailResponse;
import com.cairh.cpe.esb.component.notice.dto.resp.NoticeSendSmsResponse;
import com.cairh.cpe.esb.component.notice.dto.resp.NoticeValidSmsResponse;
import com.cairh.cpe.esb.component.notice.dto.resp.QryNoticeModelResponse;

import java.util.List;

public interface IEsbComponentSmsDubboService {
    /**
     * noticeSendSms 短信发送(dubbo)
     *
     * @param request {@link NoticeSendSmsRequest}
     * @return {@link NoticeSendSmsResponse}
     */
    NoticeSendSmsResponse noticeSendSms (NoticeSendSmsRequest request);

    /**
     * noticeValidSms 短信验证码验证(dubbo)
     *
     * @param request {@link NoticeValidSmsRequest}
     * @return {@link NoticeValidSmsResponse}
     */
    NoticeValidSmsResponse noticeValidSms (NoticeValidSmsRequest request);

    /**
     * 发送邮件
     * @param request
     * @return
     */
    NoticeSendEmailResponse noticeSendEmail (NoticeSendEmailRequest request);

    /**
     * 查询消息模板
     * @param request
     * @return
     */
    List<QryNoticeModelResponse> qryNoticeModel (QryNoticeModelRequest request);
}
