package com.cairh.cpe.esb.component.elect.dto.resp;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@Accessors(chain = true)
public class ElectDownloadThirdFileResponse implements Serializable {
    private static final long serialVersionUID = 4805000956147164168L;
    /**文件流*/
    @NotNull
    private byte[] file;
}
