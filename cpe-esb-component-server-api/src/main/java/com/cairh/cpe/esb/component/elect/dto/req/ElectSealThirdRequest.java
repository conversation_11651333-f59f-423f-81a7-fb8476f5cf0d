package com.cairh.cpe.esb.component.elect.dto.req;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@Accessors(chain = true)
public class ElectSealThirdRequest implements Serializable {

    private static final long serialVersionUID = 0x3f59d05275b26680L;

    @NotNull(message = "filerecord_id 不能为空")
    private String filerecord_id;

    @NotNull(message = "seal_position_info 不能为空")
    private String seal_position_info;

    @NotNull(message = "seal_base64 不能为空")
    private String seal_base64;

    /**
     *  业务流水号(新意时必传)
     */
    private String src_order_no;
}
