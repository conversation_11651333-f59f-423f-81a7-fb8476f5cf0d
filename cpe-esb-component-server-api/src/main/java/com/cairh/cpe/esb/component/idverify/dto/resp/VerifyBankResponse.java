package com.cairh.cpe.esb.component.idverify.dto.resp;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc 银行卡认证出参
 * @date 2022-05-12
 */
@Data
public class VerifyBankResponse implements Serializable {

    /**
     * 业务错误码，成功情况返回Success, 错误情况请参考下方错误码 列表中FailedOperation部分
     */
    private String result;

    /**
     * 业务结果描述。
     */
    private String description;

    /**
     * 证件号
     */
    private String id_card;

    /**
     * 姓名
     */
    private String name;

    /**
     * 银行卡号
     */
    private String bank_card;

    /**
     * 公安认证表id
     */
    private String idverifyrecord_id;

}