package com.cairh.cpe.esb.component.elect.dto.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class ElectRotateImageRequest implements Serializable {
    private static final long serialVersionUID = 4966837385866478307L;
    /**image_data、filerecord_id二者必传其一、
     * 返回值与传值对应，传文件ID时，自行根据文件ID取数据做旋转，
     * 旋转完成后根据文件ID获取数据应为旋转后的结果数据*/

    /**图片base64字符串*/
    private String image_data;

    /*filerecord_id不传image_data传了的时候必须传image_type*/
    private String image_type;
    /**图片类型*/
    private String filerecord_id;
    /**旋转角度 正数顺时针旋转，负数逆时针旋转*/
    @NotNull
    private String rotate_angle;

    /**
     * 是否需要新id
     *
     * <AUTHOR>
     * @since 2023/5/27 19:00
     */
    private boolean needNewFileId;

    /**
     * 服务厂商定制, 非业务参数
     */
    private String service_vender;
}
