package com.cairh.cpe.esb.component.idverify.dto.resp;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2022-11-07
 */
@Data
@Accessors(chain = true)
public class VerifyResp implements Serializable {

    private static final long serialVersionUID = -6394031905113594473L;

    /**
     * ID
     */
    private String serial_id;

    /**
     * 客户证件类型
     */
    private String id_kind;

    /**
     * 客户证件号码
     */
    private String id_no;

    /**
     * 账户全称
     */
    private String full_name;

    /**
     * 手机号码
     */
    private String mobile_tel;

    /**
     * 认证类型
     */
    private String verify_type;

    /**
     * 状态 是否实名认证  0：否  1：是
     */
    private String status;

    /**
     * 公安接口信息
     */
    private String result_info;

    /**
     * 文件记录ID
     */
    private String filerecord_id;

    /**
     * 实现厂商
     */
    private String factory_name;

    /**
     * 创建时间
     */
    private Date create_datetime;

    /**
     * 归历史标志
     */
    private String tohis_flag;

    /**
     * 归历史时间
     */
    private Date tohis_datetime;

    /**
     * 运营商
     */
    private String operator;

    /**
     * 中登业务类型 01：手机号码当前状态查询  02：手机号码与客户名称对应关系核查03：手机号码与身份证明文件对应关系核查
     */
    private String csdc_busi_kind;

    /**
     * 清算日期
     */
    private Integer date_clear;
}