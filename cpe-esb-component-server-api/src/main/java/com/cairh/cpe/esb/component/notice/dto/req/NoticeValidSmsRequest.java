package com.cairh.cpe.esb.component.notice.dto.req;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 	短信验证码验证入参(详情:http://cf.cairenhui.com/display/DevelopKS/comp.notice.validSms)
 *
 */
@Data
public class NoticeValidSmsRequest implements Serializable {
    private static final long serialVersionUID = -1824980850501031436L;
    /**发送记录ID*/
    private String noticerecord_id;
    /**手机号码*/
    @NotNull
    private String mobile_tel;
    /**验证码*/
    @NotNull
    private String valid_code;

    /**
     * 服务厂商定制, 非业务参数
     */
    private String service_vender;
}
