package com.cairh.cpe.esb.component.ocr.dto.req;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class OcrParseBusinessLisenceRequest implements Serializable {

    private static final long serialVersionUID = 4164353532696193909L;
    /**营业执照图片base64字符串*/
    @NotNull
    private String base64_image;

    /**备注，建议业务系统调用传业务ID或系统编号，否则不支持细化统计*/
    private String remark;

    /**
     * 服务厂商定制, 非业务参数
     */
    private String service_vender;
}
