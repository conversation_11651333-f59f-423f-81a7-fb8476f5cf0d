package com.cairh.cpe.esb.component.elect.dto.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023-04-07
 */
@Data
public class UploadImageByDiyConfRequest implements Serializable {

    private static final long serialVersionUID = -6332350178989123530L;

    /**
     * 文件流
     */
    @NotEmpty(message = "invalid parameter file!")
    private byte[] file;

    /**
     * 文件名
     */
    @NotBlank(message = "invalid parameter name!")
    private String name;

    /**
     * 文件夹
     */
    @NotBlank(message = "invalid parameter dir!")
    private String dir;

    /**
     * 质量
     */
    @NotNull(message = "invalid parameter quality!")
    private Double quality;

    /**
     * 最大宽度
     */
    @NotNull(message = "invalid parameter maxWidth!")
    private Integer maxWidth;

    /**
     * 文件服务配置
     */
    @NotBlank(message = "invalid parameter param_json!")
    private String param_json;

    /**
     * 服务厂商定制, 非业务参数
     */
    @NotBlank(message = "invalid parameter service_vender!")
    private String service_vender;


}