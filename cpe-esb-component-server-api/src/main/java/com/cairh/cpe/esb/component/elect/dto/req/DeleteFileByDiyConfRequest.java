package com.cairh.cpe.esb.component.elect.dto.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023-04-07
 */
@Data
public class DeleteFileByDiyConfRequest implements Serializable {

    private static final long serialVersionUID = -4813487434691568555L;

    /**
     * 文件地址，以.或者/结尾代表删除文件夹，否则就是删除对象
     * 服务器内部路径 eg：/elect/dubbo/service2/zhhtest.txt
     */
    @NotBlank(message = "invalid parameter httpaddress!")
    private String httpaddress;

    /**
     * 文件服务配置，可传递bucketName等
     */
    @NotBlank(message = "invalid parameter param_json!")
    private String param_json;

    /**
     * 服务厂商定制, 非业务参数
     */
//    @NotBlank(message = "invalid parameter service_vender!")
    private String service_vender;
}