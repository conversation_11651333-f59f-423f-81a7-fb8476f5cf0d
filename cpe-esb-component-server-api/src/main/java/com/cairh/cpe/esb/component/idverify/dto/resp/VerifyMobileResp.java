package com.cairh.cpe.esb.component.idverify.dto.resp;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc 手机实名认证返回
 * @date 2022-05-13
 */
@Data
@Accessors(chain = true)
public class VerifyMobileResp implements Serializable {

    private static final long serialVersionUID = 8007750091622474684L;
    /**
     * 运营商  01：移动 02：联通 03：电信 04：携号转网-移动 05：携号转网-联通 06：携号转网-电信
     */
    private String operator;

    /**
     * 认证结果 认证是否通过：0：否，1：是，2：服务调用失败
     */
    private String auth_result;

    /**
     * 认证结果信息
     */
    private String result_info;

    /**
     * 公安认证表id
     */
    private String idverifyrecord_id;

}