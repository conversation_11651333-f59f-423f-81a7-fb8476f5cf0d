package com.cairh.cpe.esb.component.csdc.dto.request;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022-11-07
 */
@Data
public class TaAcctAndSecAcctLnkMgrReq implements Serializable {

    private static final long serialVersionUID = 3633284962191876817L;

    /**
     * 业务类别
     * 01：TA账户与场内账户对应关系新增
     * 02：TA账户与场内账户对应关系撤销
     */
    private String ywlb;

    /**
     * 一码通账户号码
     */
    private String ymth = "";

    /**
     * 场内证券账户类别
     */
    private String zhlb = "";

    /**
     * 场内证券账户号码
     */
    private String zqzh;

    /**
     * TA账户类别
     */
    private String tazhlb = "";

    /**
     * TA账户号码
     */
    private String tazh;

    /**
     * 备用字段
     */
    private String byzd = "";

    /**
     * 用户名
     */
    private String user_name;

    /**
     * 证件号
     */
    private String id_no;

    /**
     * 营业部编号
     */
    private String branch_no;

    /**
     * 证件类型，预留参数
     */
    private String id_kind = "0";
}