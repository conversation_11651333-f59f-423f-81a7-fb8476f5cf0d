package com.cairh.cpe.esb.component.csdc.dto.request;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022-11-07
 */
@Data
public class TaAcctAndOrgLnkQryReq implements Serializable {

    private static final long serialVersionUID = -6619690464547439170L;

    /**
     * TA账户类别
     */
    private String tazhlb = "";

    /**
     * TA账户号码
     */
    private String tazh;

    /**
     * 备用字段
     */
    private String byzd = "";

    /**
     * 证件号
     */
    private String id_no;

    /**
     * 营业部编号
     */
    private String branch_no;

    /**
     * 证件类型，预留参数
     */
    private String id_kind = "0";

    /**
     * 账户全称
     */
    private String user_name;
}