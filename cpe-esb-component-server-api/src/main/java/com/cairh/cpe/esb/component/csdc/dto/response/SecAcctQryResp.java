package com.cairh.cpe.esb.component.csdc.dto.response;

import com.cairh.cpe.esb.component.csdc.dto.response.entity.Csdcholder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-11-07
 */
@Data
public class SecAcctQryResp implements Serializable {

    private static final long serialVersionUID = -4149667163536038781L;

    /**
     *错误编码
     */
    private String error_no;

    /**
     * 错误信息
     */
    private String error_info;

    /**
     * 错误信息
     */
    private String deal_info;

    /**
     * 数据体
     *[{
     * YWLSH: "" 业务流水号
     * KHMC: "" 客户名称
     * ZJLB: "" 主要身份证明文件类别
     * ZJDM: "" 主要身份证明文件代码
     * YMTH: "" 一码通账户号码
     * YMTZT: "" 一码通账户状态
     * ZHLB: "" 证券账户类别
     * ZQZH: "" 证券账户号码
     * ZQZHZT: "" 证券账户状态
     * KHFS: "" 证券账户开户方式
     * KHRQ: "" 证券账户开户日期
     * KHJGMC: "" 证券账户开户代理机构名称
     * XHRQ: "" 证券账户销户日期
     * XHJGMC: "" 证券账户销户代理机构名称
     * GLGXBS: "" 关联关系确认标识
     * QRJGMC: "" 关联关系确认机构名称
     * BHGBS: "" 不合格标识
     * BHGJYXZ: "" 不合格导致的交易限制
     * BHGYYLB: "" 不合格原因类别
     * KHJGDM: "" 业务发起开户代理机构代码
     * KHWDDM: "" 业务发起开户代理网点代码
     * SQRQ: "" 申请日期
     * YWRQ: "" 业务日期
     * YWPZBS: "" 业务凭证报送标识
     * JGDM: "" 结果代码
     * JGSM: "" 结果说明
     * }]
     */
    private String result_list;

    private List<Csdcholder> data;
}