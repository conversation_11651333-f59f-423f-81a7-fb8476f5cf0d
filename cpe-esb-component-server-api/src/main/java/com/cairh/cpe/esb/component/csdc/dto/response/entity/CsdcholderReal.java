package com.cairh.cpe.esb.component.csdc.dto.response.entity;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 中登返回股东账户数据真实实体 */
@Data
public class CsdcholderReal implements Serializable {

    private static final long serialVersionUID = 6081651447334625006L;
    /**
     * 业务流水号
     */
    private String ywlsh;
    /**
     * 客户名称
     */
    private String khmc;
    /**
     * 主要身份证明文件类别
     */
    private String zjlb;
    /**
     * 主要身份证明文件代码
     */
    private String zjdm;
    /**
     * 一码通账户号码
     */
    private String ymth;
    /**
     * 一码通账户状态
     */
    private String ymtzt;
    /**
     * 证券账户类别
     */
    private String zhlb;
    /**
     * 证券账户号码
     */
    private String zqzh;
    /**
     * 证券账户状态
     */
    private String zqzhzt;
    /**
     * 证券账户开户方式
     */
    private String khfs;
    /**
     * 证券账户开户日期
     */
    private String khrq;
    /**
     * 证券账户开户代理机构名称
     */
    private String khjgmc;
    /**
     * 证券账户销户日期
     */
    private String xhrq;
    /**
     * 证券账户销户代理机构名称
     */
    private String xhjgmc;
    /**
     * 关联关系确认标识
     */
    private String glgxbs;
    /**
     * 关联关系确认机构名称
     */
    private String qrjgmc;
    /**
     * 不合格标识
     */
    private String bhgbs;
    /**
     * 不合格导致的交易限制
     */
    private String bhgjyxz;
    /**
     * 不合格原因类别
     */
    private String bhgyylb;
    /**
     * 业务发起开户代理机构代码
     */
    private String khjgdm;
    /**
     * 业务发起开户代理网点代码
     */
    private String khwddm;
    /**
     * 申请日期
     */
    private String sqrq;
    /**
     * 业务日期
     */
    private String ywrq;
    /**
     * 业务凭证报送标识
     */
    private String ywpzbs;
    /**
     * 结果代码
     */
    private String jgdm;
    /**
     * 结果说明
     */
    private String jgsm;
}