package com.cairh.cpe.esb.component.notice.dto.resp;

import lombok.Data;

import java.io.Serializable;

/**
 * 	短信验证码发送出参(详情:http://cf.cairenhui.com/display/DevelopKS/comp.notice.sendSms)
 *
 */
@Data
public class NoticeSendSmsResponse implements Serializable {
    private static final long serialVersionUID = -2429816224938578251L;
    /**发送记录ID (不传则按mobile_tel取最新一条未验证记录)*/
    private String noticerecord_id;
}
