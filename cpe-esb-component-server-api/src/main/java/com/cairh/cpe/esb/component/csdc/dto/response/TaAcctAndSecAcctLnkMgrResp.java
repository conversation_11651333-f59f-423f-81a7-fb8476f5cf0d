package com.cairh.cpe.esb.component.csdc.dto.response;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022-11-07
 */
@Data
public class TaAcctAndSecAcctLnkMgrResp implements Serializable {

    private static final long serialVersionUID = -4289857205663018023L;

    /**
     *错误编码
     */
    private String error_no;

    /**
     * 错误信息
     */
    private String error_info;

    /**
     * 错误信息
     */
    private String deal_info;

    /**
     * 数据体
     *[{
     * YWLSH: "" 业务流水号
     * YWLB: "" 业务类别
     * YMTH: "" 一码通账户号码
     * ZHLB: "" 场内证券账户类别
     * ZQZH: "" 场内证券账户号码
     * TAZHLB: "": TA账户类别
     * TAZH: "": TA账户号码
     * KHJGDM: "" 业务发起开户代理机构代码
     * KHWDDM: "" 业务发起开户代理网点代码
     * SQRQ: "" 申请日期
     * BYZD: "" 备用字段
     * JGDM: "" 结果代码
     * JGSM: "" 结果说明
     * }]
     */
    private String result_list;
}