package com.cairh.cpe.esb.component.ocr.dto.resp;

import lombok.Data;

import java.io.Serializable;

@Data
public class OcrParseNormalResponse implements Serializable {

    private static final long serialVersionUID = 1690838948624712969L;

    /**识别结果 识别结果*/
    private String words;
    /**类型 类型（print：机打，handwriting：手写）*/
    private String type;
    /**照片可信度 照片可信度，取值范围为0~1，例如0.98476171*/
    private String score;
    /**
     * 结果描述
     */
    private String description;
}
