package com.cairh.cpe.esb.component.idverify;

import com.cairh.cpe.esb.component.idverify.dto.req.*;
import com.cairh.cpe.esb.component.idverify.dto.resp.*;

/**
 * <AUTHOR>
 * @desc
 * @date 2022-04-28
 */
public interface IEsbComponentIdVerifyDubboService {

    /**
     * @desc 公安认证 身份认证
     * @param verifyPoliceRequest
     * @return com.cairh.cpe.esb.component.idverify.dto.resp.VerifyPoliceResponse
     * <AUTHOR>
     * @time 2022/4/29 10:43
     */
    VerifyPoliceResponse verifyPolice(VerifyPoliceRequest verifyPoliceRequest);

    /**
     *  公安认证 手机号认证
     * @param verifyPoliceRequest
     * @return
     */
    VerifyMobileResp verifyMobile(VerifyMobileRequest verifyPoliceRequest);

    /**
     * 全要素公安认证
     * @param request
     * @return
     */
    VerifyPoliceAllResp verifyPoliceAll(VerifyPoliceAllReq request);

    /**
     * 出入境证件信息核查
     * @param request
     * @return
     */
    VerifyPassPortResp verifyPassPort(VerifyPassPortReq request);

    VerifyResp queryVerify(VerifyReq request);

    /**
     * 修改公安认证历史标志，归档时间
     */
    void modVerifyHisFlag(VerifyArchiveReq verifyArchiveReq);

    /**
     *  公安认证 银行卡认证
     */
    VerifyBankResponse verifyBankCard(VerifyBankRequest verifyBankRequest);
}
