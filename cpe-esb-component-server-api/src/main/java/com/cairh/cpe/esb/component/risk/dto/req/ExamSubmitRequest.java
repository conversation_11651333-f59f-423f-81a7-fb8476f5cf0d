package com.cairh.cpe.esb.component.risk.dto.req;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class ExamSubmitRequest implements Serializable {
    private static final long serialVersionUID = 866527030267771152L;
    private String exampaper_id;
    private List answer_list;
    private String render_data;
    private String full_name; // 客户全称
    private String id_kind_gb; // 证件类型
    private String id_no; // 证件号码

    /**
     * 客户编号
     */
    private String client_id;
    /**
     * 机构标识
     */
    private String organ_flag;

    /**
     * 扩展参数
     * 金正直销柜台参数：ltoken， device_type， ip_address， mac_address， device_id， uuid， imei
     */
    private Map<String, Object> ext_params;
}
