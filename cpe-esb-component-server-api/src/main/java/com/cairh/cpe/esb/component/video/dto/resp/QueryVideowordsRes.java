package com.cairh.cpe.esb.component.video.dto.resp;

import com.cairh.cpe.esb.component.video.dto.resp.support.VideowordsConfig;
import com.cairh.cpe.esb.component.video.dto.resp.support.VideowordsModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
public class QueryVideowordsRes implements Serializable {
    private static final long serialVersionUID = -1042627106170275926L;

    //视频话术模板表数据
    List<VideowordsModel> Videomodel_resultlist;

    //视频话术配置表数据
    List<VideowordsConfig> Videoconfig_resultlist;

    //返回信息
    Map<String,Object> infomation;

}
