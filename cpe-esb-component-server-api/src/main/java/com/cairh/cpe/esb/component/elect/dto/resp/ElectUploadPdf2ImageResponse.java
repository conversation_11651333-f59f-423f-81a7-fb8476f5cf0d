package com.cairh.cpe.esb.component.elect.dto.resp;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@Accessors(chain = true)
public class ElectUploadPdf2ImageResponse implements Serializable {

    private static final long serialVersionUID = 70222793085412354L;

    /** 文件名，多个用逗号隔开 **/
    private String image_names;

    /** 转换后的总页数 **/
    private int page_num;
}
