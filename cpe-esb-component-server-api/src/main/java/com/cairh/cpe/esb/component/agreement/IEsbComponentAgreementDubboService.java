package com.cairh.cpe.esb.component.agreement;

import com.cairh.cpe.esb.component.agreement.dto.req.*;
import com.cairh.cpe.esb.component.agreement.dto.resp.*;
import com.cairh.cpe.esb.component.elect.dto.req.ElectSealThirdRequest;
import com.cairh.cpe.esb.component.elect.dto.resp.ElectSealThirdResponse;

import java.util.List;

public interface IEsbComponentAgreementDubboService {

    CompElectAgainSignAgreeResponse compElectAgainSignAgree(CompElectAgainSignAgreeRequest request);

    CompElectSignAgreeResponse compElectSignAgree(CompElectSignAgreeRequest request);

    List<CompElectQrySignedAgreeResponse> compElectQrySignedAgree(CompElectQrySignedAgreeRequest request);

    List<CompElectQryNeedSignListResponse> compElectQryNeedSignList(CompElectQryNeedSignListRequest request);

    CompElectQryAgreeFileViewResponse compElectQryAgreeFileView(CompElectQryAgreeFileViewRequest request);

    CompElectQryAgreeHtmlViewResponse compElectQryAgreeHtmlView(CompElectQryAgreeHtmlViewRequest request);

    /**
     * 协议文件加盖CA证书
     * @param request
     */
    void compElectImprintCaSeal(CompElecImprintCaSealRequest request);

    void compElectSignThirdAgreement(CompElectSignThirdAgreementRequest request);

    //CompElectSyncBopAgreementTemplateResponse compElectSyncBopAgreementTemplate(CompElectSyncBopAgreementTemplateRequest request);

    /**
     * 将三方签署协议与本地协议关联
     *
     * @param request
     * @return List<com.cairh.cpe.esb.component.agreement.dto.resp.CompElectSignAgreeResponse>
     * <AUTHOR>
     * @since 2023/6/28 11:26
     */
    List<CompElectSignAgreeResponse> compElectBindSignThirdAgreement(List<CompElectBindSignThirdAgreementRequest> request);

    /**
     * 通过协议序列号查询协议模板信息
     */
    CompQueryElectAgreeModelResponse compQueryElectAgreeModel(CompQueryElectAgreeModelRequest request);


    /**
     * 通过协议签署id查询协议模板信息
     */
    List<CompQueryElectAgreeModelResponse> compQueryElectAgreeModelByAgreementSignIds(CompQueryElectByAgreementSignIdsRequest request);

    /**
     * 协议签署留痕柜台
     */
    void syncElecsignToCounter(CompElecsignSyncToCounterRequest request);

    /**
     * 通过第三方给PDF加盖印章
     * @param request
     * @return
     */
    ElectSealThirdResponse compElectSealFdfByThird(ElectSealThirdRequest request);


    /**
     * 商兆CA，获取签名标识，签名授权
     */
    CompElecGetAuthorizationResponse compElectGetAuthorization(CompElecGetAuthorizationRequest request);

    /**
     * 商兆CA,通过签名标识更新协议文件
     */
    CompElecUpdateAuthorFileResponse compElectUpdateAuthorFile(CompElecUpdateAuthorFileRequest request);
}
