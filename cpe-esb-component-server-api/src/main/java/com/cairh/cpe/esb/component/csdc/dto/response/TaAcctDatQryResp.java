package com.cairh.cpe.esb.component.csdc.dto.response;

import com.cairh.cpe.esb.component.csdc.dto.response.entity.CsdcTaAcctDatQry;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-11-07
 */
@Data
public class TaAcctDatQryResp implements Serializable {

    private static final long serialVersionUID = -4289857205663018023L;

    /**
     *错误编码
     */
    private String error_no;

    /**
     * 错误信息
     */
    private String error_info;

    /**
     * 错误信息
     */
    private String deal_info;

    /**
     * 数据体
     [{
     YWLSH: "" 业务流水号
     YWLB: "" 业务类别
     ZHLB: "" 证券账户类别
     ZQZH: "" 证券账户号码
     KHJGDM: "" 业务发起开户代理机构代码
     KHWDDM: "" 业务发起开户代理网点代码
     SQRQ: "" 申请日期
     BYZD: "" 备用字段
     DYZH: "" 对应证券账户号码
     DYZHLB: "" 对应证券账户类别
     KHMC: "" 客户名称
     KHLB: "" 客户类别
     GJDM: "" 国籍/地区代码
     ZJLB: "" 主要身份证明文件类别
     ZJDM: "" 主要身份证明文件代码
     CPBM: "" 产品编码
     ZQZHZT: "" 证券账户状态
     JGDM: "" 结果代码
     JGSM: "" 结果说明
     }]
     */
    private String result_list;

    List<CsdcTaAcctDatQry> data;
}