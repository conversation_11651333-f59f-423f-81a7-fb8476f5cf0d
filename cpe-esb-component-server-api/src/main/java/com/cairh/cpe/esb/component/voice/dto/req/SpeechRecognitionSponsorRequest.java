package com.cairh.cpe.esb.component.voice.dto.req;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 异步语音识别 发起 request
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SpeechRecognitionSponsorRequest implements Serializable {

    /**
     * 文件id Y
     *
     * 语音文件标识
     */
    private String filerecord_id;

    /**
     * 服务厂商定制, 非业务参数
     */
    private String service_vender;
}