package com.cairh.cpe.esb.component.archive.dto.resp;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023-03-08
 */
@Data
public class QyrFileRecordResponse implements Serializable {

    private static final long serialVersionUID = 3121789069422015463L;

    /**档案文件类型(视频、图片、文档等)*/
    private String file_type;
    /**文件时长(视频文件属性)*/
    private Integer file_time;
    /**档案文件大小*/
    private Integer file_size;
    /**档案文件路径*/
    private String file_path;
    /**加密类型*/
    private String encrypt_type;
    /**加密值*/
    private String encrypt_content;
    /**状态(0初始 1删除)*/
    private String status;
    /**remark*/
    private String remark;
    /**文件存储方式*/
    private String storage_type;
}