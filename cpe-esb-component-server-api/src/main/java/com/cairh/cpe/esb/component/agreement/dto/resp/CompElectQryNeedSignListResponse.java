package com.cairh.cpe.esb.component.agreement.dto.resp;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class CompElectQryNeedSignListResponse implements Serializable {
    private static final long serialVersionUID = -7772295958186829254L;
    private String elecagreemodel_id;

    private String agreement_no;

    private String  agreement_name;

    private String agreement_version;

    private String agreement_file_type;

    private String agreement_file_id;

    private String force_read_time;

    private String agreement_type;

    /** pdf转图片页数 **/
    private int page_num;

    /**
     * 签署方式
     */
    private String agreement_sign_type;

    /**
     * 外部接入参数
     */
    private String ex_param;

    /**
     * 三方接入方式
     */
    private String third_source_type;

    /**
     * 协议扩展名称
     */
    private String ex_name;
    /**
     * 签章位置
     */
    private String seal_pos;
    /**
     * 签字位置
     */
    private String sign_pos;

    /**
     * 协议内容
     */
    private String agreement_content;

    /**
     * 协议状态
     */
    private String agreement_status;

    /**
     * 加密类型
     */
    private String encrypt_type;

    /**
     * 解密值
     */
    private String encrypt_content;

    /**
     * 生效时间
     */
    private Date effective_datetime;

    /**
     * 失效时间
     */
    private Date invalid_datetime;

    /**
     * 三方协议id
     */
    private String third_agreement_id;

    /**
     * 三方协议内容
     */
    private String third_agreement_str;

    /**
     * 子协议id列表
     */
    private List<String> sub_elecagreemodel_id;


    private String subsys_no;

    private String busin_type;

    private String organ_flag;

    /**
     * 阅读方式
     */
    private String agreement_read_type;

    /**
     * 自动阅读时间
     */
    private String auto_read_type;

    /**
     * 排序
     */
    private String order_no;

    /**
     * 是否抄写
     */
    private String transcribe_flag;

    /**
     * 抄写内容
     */
    private String transcribe_content;

}
