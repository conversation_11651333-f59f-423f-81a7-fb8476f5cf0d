package com.cairh.cpe.esb.component.idverify;

import com.cairh.cpe.esb.component.idverify.dto.req.yiqian.YQFirstRequest;
import com.cairh.cpe.esb.component.idverify.dto.req.yiqian.YQSecondRequest;
import com.cairh.cpe.esb.component.idverify.dto.resp.yiqian.YQFirstResponse;
import com.cairh.cpe.esb.component.idverify.dto.resp.yiqian.YQSecondResponse;

public interface IYiQianComponentIdVerifyDubboService {
    /**
     * 两步式首次请求
     */
    YQFirstResponse firstVerify(YQFirstRequest request);
    /**
     * 两步式二次请求（原ID9008接口）
     */
    YQSecondResponse secondVerify(YQSecondRequest request);
}
