package com.cairh.cpe.esb.component.video.dto.req;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * comp.video.answerUserVideo(详见http://cf.cairenhui.com/display/DevelopKS/comp.video.answerUserVideo)
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class VideoAnswerUserRequest implements Serializable {

    private static final long serialVersionUID = -7289804726363509344L;
    /**
     * 系统编号 Y
     */
    @NotNull
    private String subsys_no;

    /**
     * 业务流水号(需保证唯一) Y
     */
    @NotBlank
    private String unique_id;

    /**
     * 操作员编号 Y
     */
    private String operator_no;

    /**
     * 操作分支机构 Y
     */
    private String op_branch_no;

    /**
     * 机构标志 N
     */
    private String organ_flag;

    /**
     * 账户全称 N
     */
    private String full_name;

    /**
     * 证件类型 N
     */
    private String id_kind;

    /**
     * 证件号码 N
     */
    private String id_no;

    /**
     * 智能派单的任务id
     */
    private String task_id;

}
