package com.cairh.cpe.esb.component.business.dto.req;

import lombok.Data;

import java.io.Serializable;

/**
 * 账户标签字典信息查询
 *
 * <AUTHOR>
 * @since 2025/8/27 14:46
 */
@Data
public class BusinessQueryAccountDictInfoReq implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 操作动作
     */
    private String auditAction = "";
    /**
     * 字典类别
     * 一级标签传590349、二级标签传590350
     */
    private String dictEntry;

}
