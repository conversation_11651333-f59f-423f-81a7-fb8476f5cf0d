package com.cairh.cpe.esb.component.idverify.dto.resp.tencentcloud;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 腾讯云-特化 获取核身信息 response
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class TencentCloudInvokeGetDetectInfoResponse implements Serializable {

    /**
     * 活体检测错误码
     *
     * 0为成功
     */
    private String live_code;

    /**
     * 活体检测错误信息
     */
    private String live_msg;

    /**
     * 视频最佳帧id
     *
     * 记载于本地档案
     */
    private String framerecord_id;

    /**
     * 活体视频id
     *
     * 记载于本地档案
     */
    private String livevideorecord_id;

    /**
     * 人证比对错误码
     *
     * 仅活体检测成功情形有结果, 0为成功
     */
    private Long compare_code;

    /**
     * 人证比对得分
     *
     * 仅活体检测成功情形有结果, 0.00 ~ 100.00
     */
    private Float compare_score;

    /**
     * 人证比对结果描述
     *
     * 仅活体检测成功情形有结果
     */
    private String compare_msg;

    /**
     * 请求标识
     *
     * 每次请求都会返回
     */
    private String request_id;
}
