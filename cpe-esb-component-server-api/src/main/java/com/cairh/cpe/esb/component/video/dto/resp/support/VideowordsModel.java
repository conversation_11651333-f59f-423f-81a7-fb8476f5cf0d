package com.cairh.cpe.esb.component.video.dto.resp.support;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 视频话术模板
 */
@Data
public class VideowordsModel implements Serializable {
        private static final long serialVersionUID = -7550633974962929311L;
        /**
         * id
         */
        private String serial_id;

        /**
         * 模板名称
         */
        private String model_name;

        /**
         * 模板类型
         */
        private String model_type;

        /**
         * 视频方式  1-双向坐席;2-单项自助
         */
        private String video_type;

        /**
         * 状态
         */
        private String status;

        /**
         * 创建人
         */
        private String create_by;

        /**
         * 创建时间
         */
        private Date create_datetime;

        /**
         * 修改人
         */
        private String modify_by;

        /**
         * 修改时间
         */
        private Date modify_datetime;

        /**
         * 发送消息话术
         */
        private String send_msg_words;

        /**
         * 用户提示话术
         */
        private String client_tips_words;

        /**
         * 适用性别
         */
        private String sex_apply;

}
