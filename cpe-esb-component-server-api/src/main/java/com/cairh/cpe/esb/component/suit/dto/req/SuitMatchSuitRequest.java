package com.cairh.cpe.esb.component.suit.dto.req;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * comp.suit.matchSuit(详见:http://cf.cairenhui.com/display/DevelopKS/comp.suit.matchSuit)
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SuitMatchSuitRequest implements Serializable {

    private static final long serialVersionUID = -5868562932667362651L;
    //机构标志 Y
    private String organ_flag;

    //账户全称 Y
    private String full_name;

    //证件类别 Y
    private String id_kind;

    //证件号码 Y
    private String id_no;

    //客户风险等级 Y
    private String corp_risk_level;

    //客户拟投资品种 Y
    private String en_invest_kind;

    //客户拟投资期限 Y
    private String en_invest_term;

    //客户允许最大亏损率 N
    private BigDecimal en_maxdeficit_rate;

    //客户收益类型 N
    private String client_income_type;

    //产品TA编号 Y
    private String prodta_no;

    //产品代码 Y
    private String prod_code;

    //规则数据
    private String regular_data;

    private String op_entrust_way;

    private String operator_no;

    private String client_id;

    private String op_station;

    private String channel_code;
    //操作分支机构
    private String op_branch_no;
    //分支机构
    private String branch_no;
    //交易密码
    private String password;
    //资产账户
    private String fund_account;
    // 客戶類型
    private String client_type;

}
