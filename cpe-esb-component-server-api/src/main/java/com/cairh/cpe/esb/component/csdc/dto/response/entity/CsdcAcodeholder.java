package com.cairh.cpe.esb.component.csdc.dto.response.entity;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 中登返回数据包装实体
 */
@Data
public class CsdcAcodeholder implements Serializable {

    private static final long serialVersionUID = 7981279005911987464L;
    /**
     * 证件类别
     */
    private String id_kind;
    /**
     * 证件号码
     */
    private String id_no;
    /**
     * 客户姓名
     */
    private String client_name;
    /**
     * 一码通账号
     */
    private String acode_account;
    /**
     * 一码通状态 0正常 1注销
     */
    private String csdc_acode_status;
    /**
     * 开户方式 2 网上开户 1 见证开户 0 临柜开户
     */
    private String csdc_open_type;
    /**
     * 开户日期
     */
    private LocalDate open_date;
    /**
     * 证券账户开户代理机构名称
     */
    private String open_organization;
    /**
     * 销户日期
     */
    private LocalDate cancel_date;
    /**
     * 证券账户销户代理机构名称
     */
    private String cancel_organization;
}