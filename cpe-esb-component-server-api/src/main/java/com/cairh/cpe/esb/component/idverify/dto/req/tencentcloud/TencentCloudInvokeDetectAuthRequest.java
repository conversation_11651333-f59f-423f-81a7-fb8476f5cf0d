package com.cairh.cpe.esb.component.idverify.dto.req.tencentcloud;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 腾讯云-特化 核身鉴权 request
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class TencentCloudInvokeDetectAuthRequest implements Serializable {

    /**
     * 客户场景 N
     *
     * 于https://console.cloud.tencent.com/faceid创建, 不传则走配置
     */
    private String rule_id;

    /**
     * 身份标识 N
     *
     * 未使用ocr服务时必传
     */
    private String id_card;

    /**
     * 姓名 N
     *
     * 未使用ocr服务时必传
     */
    private String name;

    /**
     * 回调地址 N
     *
     * 认证结束后重定向的地址
     */
    private String redirect_url;

    /**
     * 透传字段 N
     *
     * 获取验证结果时返回
     */
    private String extra;

    /**
     * 照片id N
     *
     * 用于人脸比对的照片, 仅支持jpg/png格式
     */
    private String filerecord_id;
}
