package com.cairh.cpe.esb.component.elect.dto.resp;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class UploadFileByDiyConfResponse implements Serializable {

    private static final long serialVersionUID = 4839533249517306899L;

    /**
     * 文件id
     */
    private String filerecord_id;

    /**
     * 原文件名
     */
    private String name;

    /**
     * 服务器文件名
     */
    private String serverFileName;

    /**
     * 文件地址
     */
    private String url;

    /**
     * 文件夹地址
     */
    private String purl;
}
