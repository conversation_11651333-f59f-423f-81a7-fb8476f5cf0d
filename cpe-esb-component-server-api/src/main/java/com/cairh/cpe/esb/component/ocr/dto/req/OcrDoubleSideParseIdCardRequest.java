package com.cairh.cpe.esb.component.ocr.dto.req;

import lombok.Data;
import java.io.Serializable;
import java.util.Map;

/**
 * 国联安ocr证件识别：http://pingcode.cairenhui.com/wiki/spaces/JSPTYFB/pages/649935fccce8634910c9d690
 */
@Data
public class OcrDoubleSideParseIdCardRequest implements Serializable {
    //正面图像base64
    private String face_base64_image;

    //反面图像base64
    private String back_base64_image;

    //备注
    private String remark;

    private String client_id;
}
