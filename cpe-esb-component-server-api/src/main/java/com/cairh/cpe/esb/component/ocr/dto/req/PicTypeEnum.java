package com.cairh.cpe.esb.component.ocr.dto.req;

import java.io.Serializable;

/**
 * pic type(detail see http://cf.cairenhui.com/display/DevelopKS/comp.ocr.parseIdCard), additionally,
 * we add id-card check and bank-card and business-license type in support of {@code OcrServiceFactorySelector}
 *
 * <AUTHOR>
 */
public enum PicTypeEnum implements Serializable {

    SFZ, GATTXZ, GATJZZ, IDCARDCHECK, BANKCARD, BUSINESSLICENSE, NORMAL,PERMANENTRESITENT
}
