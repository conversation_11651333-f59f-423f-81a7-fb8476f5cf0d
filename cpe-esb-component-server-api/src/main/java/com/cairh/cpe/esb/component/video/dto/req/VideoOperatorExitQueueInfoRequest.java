package com.cairh.cpe.esb.component.video.dto.req;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 视频坐席退出队列请求
 *
 * <AUTHOR>
 * @since 2023/8/8 10:50
 */
@Data
@Accessors(chain = true)
public class VideoOperatorExitQueueInfoRequest implements Serializable {

    private static final long serialVersionUID = 3206655205206968058L;
    /**
     * 系统编号 Y
     */
    @NotNull
    private String subsys_no;

    /**
     * 业务类型
     */
    private String busin_type;

    /**
     * 业务流水号(需保证唯一) Y
     */
    @NotNull
    private String unique_id;

    /**
     * 操作员编号
     */
    private String staff_no;

}
