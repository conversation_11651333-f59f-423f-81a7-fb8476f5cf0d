package com.cairh.cpe.esb.component.elect.dto.req;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-09-22
 */
@Data
public class ElecUploadToThirdReq implements Serializable {

    private static final long serialVersionUID = 890894340546339508L;

    /**
     * 客户号
     */
    private String client_id;

    /**
     * 资产账户
     */
    private String fund_account;

    /**
     * 主档案任务标志
     */
    private String file_task_type;

    /**
     * 子档案任务标志
     */
    private String sub_file_task_type;

    /**
     * 操作标志, 同步T2柜台时传入
     */
    private String sub_op_type;

    /**
     * 文件信息列表
     */
    private List<ThirdFileInfo> third_file_info;

    /**
     * 手机号
     */
    private String mobile_tel;

    /**
     * 同步新意时必传，必须为1，2，3，4; 同步T2柜台
     */
    private String id_kind;

    /**
     * 同步新意时必传，证件号码; 同步T2柜台
     */
    private String id_no;

    /**
     * 同步新意时必传，机构标志
     */
    private String organ_flag;

    /**
     * 同步新意时必传
     */
    private String no_submit_flag;

    /**
     * 同步新意时必传
     */
    private String busi_code;

    /**
     * 同步新意时必传，业务流水号
     */
    private String busi_serial_no;

    /**
     * 同步新意时必传，分支机构, 同步T2柜台时传入
     */
    private String branch_no;

    /**
     * 同步新意时必传，APP_ID
     */
    private String app_id;

    /**
     * 同步新意时必传，用户编号
     */
    private String user_id;

    /**
     * 同步新意时传入
     */
    private String user_name;

    /**
     * 同步新意时必传，文件检查 取值：0或1
     */
    private String file_check;

    /**
     * 同步T2柜台时传入, 扫描类型
     */
    private String scan_type;

    /**
     * 同步T2柜台时传入
     */
    private String open_branch_code;

    /**
     * 同步T2柜台时传入
     */
    private String seat_no;

    /**
     * 同步T2柜台时传入
     */
    private String exchange_type;

    /**
     * 同步T2柜台时传入
     */
    private String task_flag;

    /**
     * 同步T2柜台时传入
     */
    private String stock_account;

    /**
     * 同步T2柜台时传入
     */
    private String acode_account;

    /**
     * 同步T2柜台时传入
     */
    private String remark;

    /**
     * 同步T2柜台时传入(必传), 档案存储分支机构,
     */
    private String store_branch_no;


    /**
     * 同步BOP传入(必传), 受理单编号
     */
    private String acpt_id;

    /**
     * 同步BOP传入(非必传), 图片页面数
     */
    private String page_num;

    /**
     * 用户站点地址 必传
     */
    private String op_station;

    /**
     * 用户委托方式 必传
     */
    private String  op_entrust_way;

    /**
     * 证件类型
     */
    private String  main_id_kind;
}