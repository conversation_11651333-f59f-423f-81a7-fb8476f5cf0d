package com.cairh.cpe.esb.component.elect.dto.resp;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@Accessors(chain = true)
public class ElectUploadFileResponse implements Serializable {
    private static final long serialVersionUID = -7723382807049460943L;
    /**文件记录ID*/
    @NotNull
    private String filerecord_id;
    /**
     * 文件地址
     */
    @NotNull
    private String httpUrl;
//
//    /**
//     * 下载地址
//     */
//    private String downloadUrl;
//
//    /**
//     * 视频预览地址
//     */
//    private String previewUrl;
}
