package com.cairh.cpe.esb.component.risk.dto.req;

import lombok.Data;

import java.io.Serializable;

@Data
public class ExamConfirmJourRequest implements Serializable {

    private static final long serialVersionUID = 8810986274381379855L;
    /**
     * 测评结果id
     */
    private String examtestresult_id;
    /**
     * 客户号
     */
    private String client_id;
    /**
     * 客户姓名
     */
    private String client_name;
    /**
     * 机构标志
     */
    private String organ_flag;
    /**
     * 证件类别
     */
    private String id_kind;
    /**
     * 证件号码
     */
    private String id_no;
    /**
     * 操作类型 1：新增，2：更新，3：删除
     * 页面展示为新增或更新操作，不传默认为更新
     */
//    private String operator_action;

}
