package com.cairh.cpe.esb.component.voice.dto.resp;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 异步语音识别 结果 response
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SpeechRecognitionResultResponse implements Serializable {

    /**
     * 句起始时间
     *
     * 单位: 秒
     */
    private Float start_time;

    /**
     * 句结束时间
     *
     * 单位: 秒
     */
    private Float end_time;

    /**
     * 句内容
     *
     * 识别到的文本内容, 可蕴含标点
     */
    private String content;
}
