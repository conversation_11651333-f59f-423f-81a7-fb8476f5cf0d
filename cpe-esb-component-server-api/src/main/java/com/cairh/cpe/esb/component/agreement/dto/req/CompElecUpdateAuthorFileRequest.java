package com.cairh.cpe.esb.component.agreement.dto.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
public class CompElecUpdateAuthorFileRequest implements Serializable {
    private static final long serialVersionUID = -1646320990722989019L;

    /**
     * 签名标识
     */
    @NotBlank(message = "qrid could not be blank!")
    private String qrid;

}
