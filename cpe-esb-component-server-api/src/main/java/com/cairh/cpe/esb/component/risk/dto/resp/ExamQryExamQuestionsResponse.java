package com.cairh.cpe.esb.component.risk.dto.resp;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class ExamQryExamQuestionsResponse implements Serializable {
    private static final long serialVersionUID = 6691278224517986595L;
    private String exampaper_id;
    private String paper_name;
    private String min_score;
    private String remark;
    private String status;
    private String paper_type;
    private String organ_flag;
    private String base_score;
    private String max_score;
    private String daily_permit_times;
    private String expire_in;
    private String sub_paper_type;
    private String version_no;
    private String is_from_counter;
    private String counter_paper_no;
    private String prodta_no;
    private String regular_expre;
    private List<Map> question_list;
}
