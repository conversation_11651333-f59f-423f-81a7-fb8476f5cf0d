package com.cairh.cpe.esb.component.ocr.dto.resp;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 图片比对入参(详情:http://cf.cairenhui.com/display/DevelopKS/comp.ocr.compareImages)
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class OcrCompareImagesResponse implements Serializable {

    private static final long serialVersionUID = 4469092524377231185L;
    //比对分数
    private float compare_score;

    //比对通过分数阈值
    private float pass_score;

    //比对结果(1:通过;0:不通过)
    private String compare_result;
}
