package com.cairh.cpe.esb.component.idverify.dto.resp;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc
 * @date 2022-04-29
 */
@Data
@Accessors(chain = true)
public class VerifyPoliceResponse implements Serializable {

    private static final long serialVersionUID = 7017300792472866767L;
    /**
     * 公安认证id
     */
    private String idverifyrecord_id;

    /**
     * 文件记录id
     */
    private String filerecord_id;

    /**
     * 图像数据
     */
    private String image_data;

    /**
     * 公安认证状态  0-失败 1-通过
     */
    private String status;

    /**
     * 认证结果信息
     */
    private String result_info;

    /**
     * 认证额外信息
     */
    private String ext_info;

    /**
     * 生日
     */
    private String birthday;

    /**
     * 性别
     */
    private String gender;

    /**
     *国籍
     */
    private String nationCode;

    /**
     * 性别
     */
    private String usreName;

    /**
     * 证件号码
     */
    private String id_no;

    /*
     * 是否存在公安头像 1: 存在  0: 不存在
     * */
    private Integer is_police_photo;

    /**
     * 机构认证信息 目前仅中登有返回
     */
    private VerifyOrgPoliceResponse verifyOrgPoliceResponse;

    public Integer getIs_police_photo() {
        return is_police_photo;
    }

    public void setIs_police_photo(Integer is_police_photo) {
        this.is_police_photo = is_police_photo;
    }
}