package com.cairh.cpe.esb.component.risk.dto.req;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class ExamCaculateExamRequest implements Serializable {
    private static final long serialVersionUID = 866527030267771152L;
    private String exampaper_id;
    private List answer_list;
    private String render_data;
    private String full_name; // 客户全称
    private String id_kind_gb; // 证件类型
    private String id_no; // 证件号码
    /**
     * 业务范围 海通专用 非必填项
     */
    private String bus_scope;

    /**
     * 流水号 海通专用 非必填项
     */
    private String srcOrderNo;

    // 渠道来源
    private String resType;

    private String request_no;

}
