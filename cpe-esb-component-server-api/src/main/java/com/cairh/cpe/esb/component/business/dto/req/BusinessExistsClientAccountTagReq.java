package com.cairh.cpe.esb.component.business.dto.req;

import lombok.Data;

import java.io.Serializable;

/**
 * 查询账户标签信息是否存在请求信息
 * <AUTHOR>
 * @since 2025/7/9 17:06
 */
@Data
public class BusinessExistsClientAccountTagReq implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 客户编号
     */
    private String client_id;

    /**
     * 机构标识
     * 0-个人 1-机构
     */
    private String organ_flag;

    /**
     * 一户通账户
     */
    private String cif_account;
}
