package com.cairh.cpe.esb.component.csdc.dto.request;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022-11-07
 */
@Data
public class TaAcctDatQryReq implements Serializable {

    private static final long serialVersionUID = -1230511871305209498L;

    /**
     * 业务类别
     * 01： TA账户资料及其对应场内账户情况查询
     * 02：场内账户资料及其对应TA账户情况查询
     */
    private String ywlb;

    /** 证券账户类别 */
    private String zhlb = "";

    /** 证券账户号码 */
    private String zqzh;

    /** 备用字段 */
    private String byzd = "";

    /** 用户名 */
    private String user_name;

    /** 证件号 */
    private String id_no;

    /** 营业部编号 */
    private String branch_no;

    /** 证件类型，预留参数 */
    private String id_kind = "0";
}