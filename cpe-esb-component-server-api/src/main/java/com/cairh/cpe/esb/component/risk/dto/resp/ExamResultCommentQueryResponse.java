package com.cairh.cpe.esb.component.risk.dto.resp;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ExamResultCommentQueryResponse implements Serializable {

    private static final long serialVersionUID = 3786102402913162000L;

    /**
     * ID
     */
    private String serial_id;

    /**
     * 最大分数
     */
    private BigDecimal max_score;

    /**
     * 最低分数
     */
    private BigDecimal min_score;

    /**
     * 结果类型
     */
    private String result_comment;

    /**
     * 风险等级
     */
    private String risk_level;

    private Integer risk_position;

    /**
     * 投资建议
     */
    private String invest_advice;

    /**
     * 数据ID
     */
    private String exampaper_id;

    /**
     * 有效期天数
     */
    private Integer valid_days;

    /**
     * 创建人
     */
    private String create_by;

    /**
     * 修改人
     */
    private String modify_by;

    /**
     * 归历史标识
     */
    private String tohis_flag;

    /**
     * 清算日期
     */
    private Integer date_clear;

}
