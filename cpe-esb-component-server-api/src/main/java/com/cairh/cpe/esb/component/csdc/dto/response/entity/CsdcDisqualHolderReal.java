package com.cairh.cpe.esb.component.csdc.dto.response.entity;

import lombok.Data;

import java.io.Serializable;

/**
 中登返回不合格账户查询真实实体 */
@Data
public class CsdcDisqualHolderReal implements Serializable {

    private static final long serialVersionUID = 2606723968544389602L;
    /**
     * 业务流水号
     */
    private String ywlsh;

    /**
     * 证券账户类别
     */
    private String zhlb;

    /**
     * 证券账户号码
     */
    private String zqzh;

    /**
     * 不合格标识 如为不合格账户，该标识
     * 为“1”，否则该标识为“0”。
     */
    private String bhgbs;

    /**
     * 不合格原因类别
     */
    private String bhgyylb;

    /**
     * 不合格申报机构名称
     */
    private String bhgsbjgmc;

    /**
     * 业务发起开户代理机构代码
     */
    private String khjgdm;

    /**
     * 业务发起开户代理网点代码
     */
    private String khwddm;

    /**
     * 申请日期
     */
    private String sqrq;

    /**
     * 业务日期
     */
    private String ywrq;

    /**
     * 业务凭证报送标识
     */
    private String ywpzbs;

    /**
     * 结果代码
     */
    private String jgdm;

    /**
     * 结果说明
     */
    private String jgsm;
}