package com.cairh.cpe.esb.component.elect.dto.resp;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@Accessors(chain = true)
public class ElectUploadImageResponse implements Serializable {
    private static final long serialVersionUID = 4586020834043716346L;
    /**文件记录ID*/
    @NotNull
    private String filerecord_id;
}
