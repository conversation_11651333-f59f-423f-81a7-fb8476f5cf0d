package com.cairh.cpe.esb.component.ocr.dto.req;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * OCR银行卡识别入参(详情:http://cf.cairenhui.com/display/DevelopKS/comp.ocr.parseBankCard)
 *
 */
@Data
public class OcrParseBankCardRequest implements Serializable {
    private static final long serialVersionUID = -8937838249149997394L;
    /**银行卡图片base64字符串*/
    @NotNull
    private String base64_image;
    /**备注，建议业务系统调用传业务ID或系统编号，否则不支持细化统计*/
    private String remark;

    /**
     * 服务厂商定制, 非业务参数
     */
    private String service_vender;
}
