package com.cairh.cpe.esb.component.csdc.dto.response.entity;

import lombok.Data;

import java.io.Serializable;

/**
 中登存量账户关联关系报送信息查询包装实体 */
@Data
public class CsdcOldAcctLnkQry implements Serializable {

    private static final long serialVersionUID = -8502450237514403397L;
    /**
     * 证件类型
     */
    private String id_kind;
    /**
     * 证件号码
     */
    private String id_no;
    /**
     * 客户名称
     */
    private String client_name;
    /**
     * 一码通账号
     */
    private String acode_account;

    /** 账户类别 */
    private String csdc_account_type;
    /** 证券账户 */
    private String stock_account;
    /** 委托交易券商名称 */
    private String entrust_organ_name;
    /** 关联关系是否报送（0：未报送；1：已报送） */
    private String relative_rptflag;
}