package com.cairh.cpe.esb.component.elect.dto.req;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class FileExistRequest implements Serializable {

    private static final long serialVersionUID = -1L;
    /**
     * 服务器内部路径
     * eg：/elect/dubbo/service2/zhhtest.txt
     */
    private String filePath;
    /**
     * 服务厂商定制, 非业务参数
     */
    private String service_vender;
    /**
     * 文件服务配置
     */
    private String param_json;

}
