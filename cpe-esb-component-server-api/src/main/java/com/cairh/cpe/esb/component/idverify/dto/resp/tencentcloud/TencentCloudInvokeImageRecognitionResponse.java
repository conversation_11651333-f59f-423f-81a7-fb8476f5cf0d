package com.cairh.cpe.esb.component.idverify.dto.resp.tencentcloud;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 腾讯云-特化 高清人像比对公安认证 response
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class TencentCloudInvokeImageRecognitionResponse implements Serializable {

    private static final long serialVersionUID = 8603074207428768368L;

    /**
     * 相似度，取值范围 [0.00, 100.00]。推荐相似度大于等于70时可判断为同一人，可根据具体场景自行调整阈值（阈值70的误通过率为千分之一，阈值80的误通过率是万分之一）
     */
    private Float sim;

    /**
     * 业务错误码，成功情况返回Success, 错误情况请参考下方错误码 列表中FailedOperation部分
     */
    private String result;

    /**
     * 业务结果描述。
     */
    private String description;

    /**
     * 请求标识
     *
     * 每次请求都会返回
     */
    private String request_id;
}
