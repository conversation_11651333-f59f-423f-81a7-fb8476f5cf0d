package com.cairh.cpe.esb.component.notice.dto.req;

import com.cairh.cpe.esb.component.notice.dto.req.support.FileInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 发送邮件dubbo request
 * <AUTHOR>
 * @since 2022-11-16
 */
@Data
public class NoticeSendEmailRequest implements Serializable {

    private static final long serialVersionUID = 6753752873120336905L;

    /**
     * 目标邮箱
     */
    private String e_mail;

    /**
     * 消息标题
     */
    private String msg_title;

    /**
     * 消息内容
     */
    private String msg_content;

    /**
     * 消息模板编号
     */
    private String msg_model_no;

    /**
     * 模板渲染数据
     */
    private String render_data;

    /**
     * 渠道编号
     */
    private String channl_id;

    /**
     * 备注
     */
    private String remark;

    /**
     * 文件列表
     */
    private List<FileInfo> file_list;

    /**
     * 是否使用追保邮箱  1：是  0或不传：否
     */
    private String protect_flag;
}