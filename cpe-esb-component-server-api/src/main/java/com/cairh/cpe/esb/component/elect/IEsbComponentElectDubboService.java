package com.cairh.cpe.esb.component.elect;

import com.cairh.cpe.esb.component.elect.dto.req.*;
import com.cairh.cpe.esb.component.elect.dto.resp.*;

public interface IEsbComponentElectDubboService {
    
    /**
     * electUploadFile 文件上传(dubbo)
     *
     * @param request {@link ElectUploadFileRequest}
     * @return {@link ElectUploadFileResponse}
     */
    ElectUploadFileResponse electUploadFile(ElectUploadFileRequest request);

    /**
     * electUploadFile 文件上传(dubbo)
     *
     * @param request {@link ElectUploadFileByUriRequest}
     * @return {@link ElectUploadFileByUriResponse}
     */
    ElectUploadFileByUriResponse electUploadFileByUri(ElectUploadFileByUriRequest request);

    /**
     * electDownloadFile 文件下载 文件流输出(dubbo)
     *
     * @param request {@link ElectDownloadFileRequest}
     * @return {@link ElectDownloadFileResponse}
     */
    ElectDownloadFileResponse electDownloadFile(ElectDownloadFileRequest request);

    /**
     * electUploadImage 图片上传(dubbo)
     *
     * @param request {@link ElectUploadImageRequest}
     * @return {@link ElectUploadImageResponse}
     */
    ElectUploadImageResponse electUploadImage(ElectUploadImageRequest request);

    /**
     * electDownloadImage 图片下载(dubbo)
     *
     * @param request {@link ElectDownloadImageRequest}
     * @return {@link ElectDownloadImageResponse}
     */
    ElectDownloadImageResponse electDownloadImage(ElectDownloadImageRequest request);

    /**
     * electDownloadImage 批量图片下载(dubbo)
     *
     * @param request {@link ElectDownloadImageRequest}
     * @return {@link ElectDownloadImageResponse}
     */
    ElectDownloadImageMultResponse electDownloadImageMult(ElectDownloadImageMultRequest request);


    /**
     * 根据图片路径上传图片
     * @param request
     * @return
     */
    ElectUploadImageByUriResponse electUploadImageByUri(ElectUploadImageByUriRequest request);
    /**
     * electRotateImage(dubbo)
     *
     * @param request {@link ElectRotateImageRequest}
     * @return {@link ElectRotateImageResponse}
     */
    ElectRotateImageResponse electRotateImage(ElectRotateImageRequest request);

    /**
     * electDeleteFile 文件删除(dubbo)
     * @param request
     * @return
     */
    void electDeleteFile(ElectDeleteFileRequest request);

    /**
     * 自定义配置上传文件
     * @param request
     * @return
     */
    UploadFileByDiyConfResponse uploadFileByDiyConf(UploadFileByDiyConfRequest request);


    /**
     * 遍历路径获取所有文件路径
     * @param request
     * @return
     */
    QryFileListByDiyConfResponse qryFileListByDiyConf(QryFileListByDiyConfRequest request);

    /**
     * (自定义配置)复制文件或文件夹
     * @param request
     * @return
     */
    void copyFileByDiyConf(CopyFileByDiyConfRequest request);

    /**
     * 删除文件或文件夹(自定义存储服务配置)
     * @param request
     */
    void deleteFileByDiyConf(DeleteFileByDiyConfRequest request);

    /**
     *
     * @param request
     */
    UploadImageByDiyConfResponse uploadImageByDiyConf(UploadImageByDiyConfRequest request);


    /**
     * 直接上传厂商，不进行FileRecord存储
     */
    ElectUploadFileToVenderResponse electUploadFileToVender(ElectUploadFileToVenderRequest request);

    /**
     * 判断文件是否存在
     */
    FileExistResponse isFileExist(FileExistRequest request);

    /**
     * 根据签名信息查询渲染数据
     */
    QryRenderDataResponse qryRenderDataBySignInfo(QryRenderDataRequest request);
}
