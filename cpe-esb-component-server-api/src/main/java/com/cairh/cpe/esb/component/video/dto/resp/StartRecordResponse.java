package com.cairh.cpe.esb.component.video.dto.resp;

import java.io.Serializable;

/**
 * 开始录制结果
 *
 * <AUTHOR>
 * @since XPE-SP1-PACK7-PATCH3
 */
public class StartRecordResponse implements Serializable {

    /** 厂商原始返回结果 */
    private String rawResult;

    /** 文件路径 */
    private String file_path;

    public String getRawResult() {
        return rawResult;
    }

    public void setRawResult(String rawResult) {
        this.rawResult = rawResult;
    }

    public String getFile_path() {
        return file_path;
    }

    public void setFile_path(String file_path) {
        this.file_path = file_path;
    }

    @Override
    public String toString() {
        return "StartRecordResult{" +
                "rawResult='" + rawResult + '\'' +
                ", file_path='" + file_path + '\'' +
                '}';
    }

}
