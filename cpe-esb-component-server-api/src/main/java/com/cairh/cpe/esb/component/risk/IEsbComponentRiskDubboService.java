package com.cairh.cpe.esb.component.risk;

import com.cairh.cpe.esb.component.risk.dto.req.*;
import com.cairh.cpe.esb.component.risk.dto.resp.*;

import java.util.List;

public interface IEsbComponentRiskDubboService {

    /**
     * 测算问卷
     * @param request
     * @return
     */
    ExamCaculateExamResponse examCaculateExam(ExamCaculateExamRequest request);

    /**
     * 问卷确认
     * @param request
     * @return
     */
    ExamConfirmExamResultResponse examConfirmExamResult(ExamConfirmExamResultRequest request);

    /**
     * 问卷风测留痕
     * @param request
     * @return
     */
    ExamConfirmJourResponse examConfirmJour(ExamConfirmJourRequest request);


    /**
     * 获取测评问卷及题目选项
     * @param request
     * @return
     */
    ExamQryExamQuestionsResponse examQryExamQuestions(ExamQryExamQuestionsRequest request);

    /**
     * 已测问卷查询
     * @param request
     * @return
     */
    ExamQryTestedExamResponse examQryTestedExam(ExamQryTestedExamRequest request);

    /**
     * 问卷等级规则查询
     * @param request
     * @return
     */
    List<ExamScoreLevelResponse> examScoreLevel(ExamScoreLevelRequest request);

    /**
     * 问卷直接提交，暂时只有金正直销柜台使用。
     * @param request
     * @return
     */
    ExamSubmitResponse examSubmit(ExamSubmitRequest request);

    /**
     * 测评规则查询
     */
    List<ExamResultCommentQueryResponse> examResultCommentQuery(ExamResultCommentQueryRequset request);
}
