package com.cairh.cpe.esb.component.csdc;

import com.cairh.cpe.esb.component.csdc.dto.request.*;
import com.cairh.cpe.esb.component.csdc.dto.response.*;

public interface IEsbComponentCsdcDubboService {

    /**
     * 不合格账户查询
     * @param request
     * @return
     */
    DisqualAcctQryResp disqualAcctQry(DisqualAcctQryReq request);

    /**
     * 存量账户关联关系报送信息查询.
     * @param request
     * @return
     */
    OldAcctLnkQryResp oldAcctLnkQry(OldAcctLnkQryReq request);

    /**
     * 证券账户关联关系维护.
     * @param request
     * @return
     */
    SecAcctLnkMgrResp secAcctLnkMgr(SecAcctLnkMgrReq request);

    /**
     * 证券账户查询.
     * @param request
     * @return
     */
    SecAcctQryResp secAcctQry(SecAcctQryReq request);

    /**
     * 证券账户使用信息维护
     * @param request
     * @return
     */
    SecAcctUseInfoMgrResp secAcctUseInfoMgr(SecAcctUseInfoMgrReq request);

    /**
     * TA账户与基金销售机构关联关系查询.
     * @param request
     * @return
     */
    TaAcctAndOrgLnkQryResp taAcctAndOrgLnkQry(TaAcctAndOrgLnkQryReq request);

    /**
     * TA账户与场内证券账户对应关系维护
     * @param request
     * @return
     */
    TaAcctAndSecAcctLnkMgrResp taAcctAndSecAcctLnkMgr(TaAcctAndSecAcctLnkMgrReq request);

    /**
     * TA账户资料查询.
     * @param request
     * @return
     */
    TaAcctDatQryResp taAcctDatQry(TaAcctDatQryReq request);

    /**
     * TA账户查询.
     * @param request
     * @return
     */
    TaAcctQryResp taAcctQry(TaAcctQryReq request);

    /**
     * 一码通账户查询.
     * @param request
     * @return
     */
    YmtAcctQryResp ymtAcctQry(YmtAcctQryReq request);


}
