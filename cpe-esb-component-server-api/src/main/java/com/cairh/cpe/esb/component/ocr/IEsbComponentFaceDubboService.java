package com.cairh.cpe.esb.component.ocr;

import com.cairh.cpe.esb.component.ocr.dto.req.FaceDetectRequest;
import com.cairh.cpe.esb.component.ocr.dto.req.FaceQualityDetectRequest;
import com.cairh.cpe.esb.component.ocr.dto.req.OcrBioassayDetectRequest;
import com.cairh.cpe.esb.component.ocr.dto.req.OcrCompareImagesRequest;
import com.cairh.cpe.esb.component.ocr.dto.resp.FaceQualityDetectResponse;
import com.cairh.cpe.esb.component.ocr.dto.resp.OcrBioassayResponse;
import com.cairh.cpe.esb.component.ocr.dto.resp.OcrFaceDetectResponse;
import com.cairh.cpe.esb.component.ocr.dto.resp.OcrCompareImagesResponse;

public interface IEsbComponentFaceDubboService {
    /**
     * 人脸质量检测(dubbo)
     *
     * @param request {@link FaceDetectRequest}
     * @return {@link OcrFaceDetectResponse}
     */
    OcrFaceDetectResponse faceDetect(FaceDetectRequest request);

    /**
     * 图片比对
     *
     * @param request {@link OcrCompareImagesRequest}
     * @return {@link OcrCompareImagesResponse}
     */
    OcrCompareImagesResponse faceCompare(OcrCompareImagesRequest request);

    /**
     * 人像质量检测
     *
     * @param request {@link FaceQualityDetectRequest}
     * @return {@link FaceQualityDetectResponse}
     */
    FaceQualityDetectResponse faceQualityDetect(FaceQualityDetectRequest request);
}
