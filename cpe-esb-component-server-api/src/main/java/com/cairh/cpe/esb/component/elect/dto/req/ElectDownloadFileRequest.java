package com.cairh.cpe.esb.component.elect.dto.req;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@Accessors(chain = true)
public class ElectDownloadFileRequest implements Serializable {
    private static final long serialVersionUID = 3241567610746386118L;
    /**文件记录ID*/
    @NotNull
    private String filerecord_id;

    /**
     * 是否不下载文件 默认下载文件 即false
     */
    private boolean not_download;

    /**
     * 服务厂商定制, 非业务参数
     */
    private String service_vender;

}
