package com.cairh.cpe.esb.component.ocr.dto.req;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 图片比对入参(详情:http://cf.cairenhui.com/display/DevelopKS/comp.ocr.compareImages)
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class OcrCompareImagesRequest implements Serializable {

    private static final long serialVersionUID = -422254488604575466L;
    //图片base64字符串 Y
    private String base64_imageA;

    //图片base64字符串 Y
    private String base64_imageB;

    //备注 N
    private String remark;

    /**
     * 服务厂商定制, 非业务参数
     */
    private String service_vender;
}
