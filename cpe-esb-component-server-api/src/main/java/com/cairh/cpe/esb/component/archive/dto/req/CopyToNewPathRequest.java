package com.cairh.cpe.esb.component.archive.dto.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023-03-08
 */
@Data
public class CopyToNewPathRequest implements Serializable {

    private static final long serialVersionUID = -4257510250804212959L;

    /**
     * 文件名 格式：80,2133132421314131|80,12314123434234324
     */
    private String filerecord_ids;

    /**
     * 新路径
     */
    private String new_path;
}