package com.cairh.cpe.esb.component.risk.dto.resp;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ExamSubmitResponse implements Serializable {
    private static final long serialVersionUID = -484351991198981984L;
    private String examtestresult_id;
    private BigDecimal score;
    private Integer risk_begin_date;
    private Integer risk_end_date;
    private String corp_risk_level;
    private String invest_term;
    private String en_invest_kind;
    private String cooling_period;
    private String invest_advice;
}
