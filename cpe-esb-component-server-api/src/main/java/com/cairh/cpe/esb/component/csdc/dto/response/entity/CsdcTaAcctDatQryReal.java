package com.cairh.cpe.esb.component.csdc.dto.response.entity;

import lombok.Data;

import java.io.Serializable;

/**
 中登TA账户资料查询真实实体 */
@Data
public class CsdcTaAcctDatQryReal implements Serializable {

	private static final long serialVersionUID = 7156407477525083057L;

	/**
	 * 数据体
	 [{
	 YWLSH; "" 业务流水号
	 YWLB; "" 业务类别
	 ZHLB; "" 证券账户类别
	 ZQZH; "" 证券账户号码
	 KHJGDM; "" 业务发起开户代理机构代码
	 KHWDDM; "" 业务发起开户代理网点代码
	 SQRQ; "" 申请日期
	 BYZD; "" 备用字段
	 DYZH; "" 对应证券账户号码
	 DYZHLB; "" 对应证券账户类别
	 KHMC; "" 客户名称
	 KHLB; "" 客户类别
	 GJDM; "" 国籍/地区代码
	 ZJLB; "" 主要身份证明文件类别
	 ZJDM; "" 主要身份证明文件代码
	 CPBM; "" 产品编码
	 ZQZHZT; "" 证券账户状态
	 JGDM; "" 结果代码
	 JGSM; "" 结果说明
	 }]
	 */
    /**
     * 业务流水号
     */
	private String  ywlsh;

	/**
	 * 业务类别
	 */
	private String  ywlb;

	/**
	 * 证券账户类别
	 */
	private String  zhlb;

	/**
	 * 证券账户号码
	 */
	private String  zqzh;

	/**
	 * 业务发起开户代理机构代码
	 */
	private String  khjgdm;

	/**
	 * 业务发起开户代理网点代码
	 */
	private String  khwddm;

	/**
	 * 申请日期
	 */
	private String  sqrq;

	/**
	 * 备用字段
	 */
	private String  byzd;

	/**
	 * 对应证券账户号码
	 */
	private String  dyzh;

	/**
	 * 对应证券账户类别
	 */
	private String  dyzhlb;

	/**
	 * 客户名称
	 */
	private String  khmc;

	/**
	 * 客户类别
	 */
	private String  khlb;

	/**
	 * 国籍/地区代码
	 */
	private String  gjdm;

	/**
	 * 主要身份证明文件类别
	 */
	private String  zjlb;

	/**
	 * 主要身份证明文件代码
	 */
	private String  zjdm;

	/**
	 * 产品编码
	 */
	private String  cpbm;

	/**
	 * 证券账户状态
	 */
	private String  zqzhzt;

	/**
	 * 结果代码
	 */
	private String  jgdm;

	/**
	 * 结果说明
	 */
	private String  jgsm;



}