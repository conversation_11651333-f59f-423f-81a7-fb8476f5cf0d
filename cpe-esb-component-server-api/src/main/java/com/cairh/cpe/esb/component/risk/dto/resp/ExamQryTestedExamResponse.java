package com.cairh.cpe.esb.component.risk.dto.resp;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class ExamQryTestedExamResponse implements Serializable {
    private static final long serialVersionUID = 2075475189488589886L;
    private String paper_name;
    private String counter_perper_no;
    private BigDecimal score;
    private Integer risk_begin_date;
    private Integer risk_end_date;
    private List question_list;
}
