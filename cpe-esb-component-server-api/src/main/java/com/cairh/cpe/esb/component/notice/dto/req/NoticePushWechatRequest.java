package com.cairh.cpe.esb.component.notice.dto.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 	微信公众号消息推送入参
 *
 */
@Data
public class NoticePushWechatRequest implements Serializable {

    private static final long serialVersionUID = 6960890987991110924L;

    /** 消息模板id 不能为空**/
    private String template_id;

    /** 渲染数据map，key与公众号消息模板配置的key保持一致，
     * value为渲染的值 不能为空**/
    private Map<String, Object> valueMap;

    /** 发送用户的openid 不能为空**/
    private String openid;

    /** 详情跳转url **/
    private String url;

    /**渠道来源 不能为空*/
    private String channel_type;

    /**备注，建议业务系统调用传业务ID或系统编号，否则不支持细化统计*/
    private String remark;

    /** 业务类型 **/
    private String busin_type;

    /** 子业务类型 **/
    private String sub_busin_type;

}
