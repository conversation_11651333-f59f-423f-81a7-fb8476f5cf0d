package com.cairh.cpe.esb.component.idverify.dto.req.yiqian;

import lombok.Data;
import java.io.Serializable;

/**
 * comp.idVerify.yqSecondVerify(详见http://cf.cairenhui.com/display/DevelopKS/comp.idVerify.yqSecondVerify)
 */
@Data
public class YQSecondRequest implements Serializable {
    private String dep_code; // 营业部参数
    private String transid; // 业务流水号
    private String channel; // 渠道号
    private String flowid; // 流程号
    private String zjlx; // 证件类型
    private String cardName; // 姓名
    private String zjhm; // 证件号码
    private String serial; // 公安活体检测序列号（需SDK提供）
    private String reqData; // 认证数据
    private String reqHash; // 认证数据HASH
    private String reqExtra; // 认证扩展数据
    private String period; // 身份证有效期
    private String buss_code; // 业务编码
    private String buss_name; // 业务名称
    private String picture_data;
    private String picture_hash;
    private String birthday;
    private String id_enddate;
    private String gender;
    private String bizSerialNum;
    private String bizRandNum;
    private String nationality; // 国籍
}
