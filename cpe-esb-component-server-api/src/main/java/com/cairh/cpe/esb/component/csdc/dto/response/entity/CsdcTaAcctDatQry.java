package com.cairh.cpe.esb.component.csdc.dto.response.entity;

import lombok.Data;

import java.io.Serializable;

/**
 中登TA账户资料查询包装实体 */
@Data
public class CsdcTaAcctDatQry implements Serializable {

    private static final long serialVersionUID = -7208781607910811043L;
    /**
     * 业务类别
     */
    private String csdc_busi_kind;
    /**
     * TA证券账号
     */
    private String ta_stock_account;
    /**
     * TA账户类别
     */
    private String ta_holder_kind;
    /**
     * TA账户状态
     */
    private String ta_holder_status;

    /**
     * 对应证券账户类别
     */
    private String csdc_holder_kind;
    /**
     * 中登股东账户状态
     */
    private String csdc_holder_status;
    /**
     * 中登证券账号
     */
    private String csdc_stock_account;
    /**
     * 客户姓名
     */
    private String client_name;
    /**
     * 证件类别
     */
    private String id_kind;
    /**
     * 证件号码
     */
    private String id_no;
}