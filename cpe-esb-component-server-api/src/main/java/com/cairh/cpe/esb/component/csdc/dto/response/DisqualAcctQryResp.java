package com.cairh.cpe.esb.component.csdc.dto.response;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022-11-07
 */
@Data
public class DisqualAcctQryResp implements Serializable {

    private static final long serialVersionUID = 5694508685289289555L;


    /**
     *错误编码
     */
    private String error_no;

    /**
     * 错误信息
     */
    private String error_info;

    /**
     * 错误信息
     */
    private String deal_info;

    /**
     * 数据体
     *[{
     *
     * YWLSH: "" 业务流水号
     * ZHLB: "" 证券账户类别
     * ZQZH: "" 证券账户号码
     * BHGBS: "" 不合格标识
     * BHGYYLB: "" 不合格原因类别
     * BHGSBJGMC: "" 不合格申报机构名称
     * BYZD1:"" 备用字段1
     * BYZD2:"" 备用字段2
     * BYZD3:"" 备用字段3
     * KHJGDM: "" 业务发起开户代理机构代码
     * KHWDDM: ""业务发起开户代理网点代码
     * SQRQ: ""申请日期
     * YWRQ: ""业务日期
     * YWPZBS: ""业务凭证报送标识
     * JGDM: ""结果代码
     * JGSM: ""结果说明
     *
     * }]
     */
    private String result_list;
}