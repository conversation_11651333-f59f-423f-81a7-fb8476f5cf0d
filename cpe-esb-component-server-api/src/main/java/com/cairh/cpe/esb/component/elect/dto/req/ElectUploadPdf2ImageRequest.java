package com.cairh.cpe.esb.component.elect.dto.req;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@Accessors(chain = true)
public class ElectUploadPdf2ImageRequest implements Serializable {

    private static final long serialVersionUID = -3228233514676615073L;

    /** 协议模板id **/
    @NotNull
        private String filerecord_id;

    /**
     * 服务厂商定制, 非业务参数
     */
    private String service_vender;

}
