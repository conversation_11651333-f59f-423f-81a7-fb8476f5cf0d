package com.cairh.cpe.esb.component.csdc.dto.request;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022-11-07
 */
@Data
public class OldAcctLnkQryReq implements Serializable {

    private static final long serialVersionUID = -4765793921473150192L;

    /**
     * 用户名
     */
    private String user_name;

    /**
     * 证件号
     */
    private String id_no;

    /**
     * 营业部编号
     */
    private String branch_no;

    /**
     * 证件类型，预留参数
     */
    private String id_kind = "0";

    /**
     * 一码通号码
     */
    private String ymth;


    /**
     * 证券账户类别
     */
    private String zhlb;

    /**
     * 证券账户号码
     */
    private String zqzh;
}