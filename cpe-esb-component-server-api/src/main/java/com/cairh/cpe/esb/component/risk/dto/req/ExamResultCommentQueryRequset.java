package com.cairh.cpe.esb.component.risk.dto.req;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ExamResultCommentQueryRequset implements Serializable {
    private static final long serialVersionUID = 4201276612330445699L;

    private String paper_type;//试卷类别 Y
    private String organ_flag;//机构标志 Y
    private String sub_paper_type;//子试卷类型 N
    private String prodta_no;//ta编号 N
    private String paper_name;//试卷名称 N
    private BigDecimal score;//分数 N
    private String risk_level;//风险等级 N

}
