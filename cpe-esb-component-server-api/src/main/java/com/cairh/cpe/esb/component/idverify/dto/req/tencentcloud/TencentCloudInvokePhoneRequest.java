package com.cairh.cpe.esb.component.idverify.dto.req.tencentcloud;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 腾讯云-特化 手机号三要素核验 request
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class TencentCloudInvokePhoneRequest implements Serializable {

    /**
     * 身份证号 Y
     */
    @NotBlank
    private String id_card;

    /**
     * 姓名 Y
     */
    @NotBlank
    private String name;

    /**
     * 手机号 Y
     */
    @NotBlank
    private String phone;

    /**
     * 验证模式 N
     * 验证模式（详版/简版）。简版与详版价格不一致，详见价格说明。
     * 枚举值：0（简版），1（详版）。默认值为0。
     */
    private String verify_mode;

    /**
     * 数据加密 N
     * 有加密需求的用户，传入kms的CiphertextBlob，关于数据加密可查阅 数据加密 文档。
     * 示例值：iyuhfafhsad72bd
     */
    private String cipher_textblob;

    /**
     * 加密字段 N
     * 在使用加密服务时，填入要被加密的字段。本接口中可填入加密后的IdCard，Name，Phone中的一个或多个。
     * 示例值：[IdCard]
     */
    private String[] encrypt_list;

    /**
     * 初始向量 N
     * 有加密需求的用户，传入CBC加密的初始向量。
     * 示例值：ihds8d0s9fhs8fud0f
     */
    private String iv;
}
