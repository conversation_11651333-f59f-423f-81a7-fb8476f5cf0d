package com.cairh.cpe.esb.component.idverify.dto.resp;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 机构信息
 *
 * <AUTHOR>
 * @since 2023/8/17 14:30
 */
@Data
@Accessors(chain = true)
public class VerifyOrgPoliceResponse implements Serializable {

    private static final long serialVersionUID = 1238224384687011381L;

    /**
     * 客户名称
     */
    private String customer_name;

    /**
     * 统一社会信用代码
     */
    private String usc_code;

    /**
     * 组织机构代码
     */
    private String organ_code;

    /**
     * 注册资本
     */
    private String register_capital;

    /**
     * 注册地址
     */
    private String register_address;

    /**
     * 机构类型
     */
    private String branch_type;

    /**
     * 经济类型
     */
    private String economic_type;

    /**
     * 行政区域
     */
    private String administrative_area;

    /**
     * 登记注册机关
     */
    private String register_depart;

    /**
     * 经营范围
     */
    private String business_scope;

    /**
     * 营业状态
     */
    private String business_status;

    /**
     * 经营期限开始日期 如：2020-01-01
     */
    private String business_begin_date;

    /**
     * 经营期限结束日期 如：2020-01-01或长期
     */
    private String business_end_date;

    /**
     * 经营地址
     */
    private String business_address;

    /**
     * 上级主管部门
     */
    private String upstream_authorities;

    /**
     * 质疑标志 “1”为正常，“2”为质疑
     */
    private String question_mark;

    /**
     * 校准标志
     */
    private String calibration_mark;

    /**
     * 法定代表人
     */
    private String legal_representative;

    /**
     * 职工人数
     */
    private String emp_num;

}