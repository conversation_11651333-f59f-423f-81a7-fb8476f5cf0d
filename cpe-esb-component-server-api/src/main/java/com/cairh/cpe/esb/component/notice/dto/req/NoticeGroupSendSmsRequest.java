package com.cairh.cpe.esb.component.notice.dto.req;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-08-12
 */
@Data
public class NoticeGroupSendSmsRequest implements Serializable {
    private static final long serialVersionUID = 2316698566273471791L;
    /**手机号码*/
    @NotNull
    private List<String> mobile_tel;
    /**短信内容*/
    private String msg_content;
    /**短信模板编号*/
    private String msg_model_no;
    /**模板渲染数据 以JSON形式传递*/
    private String render_data;
    /**短信方式 0 短信 1 语音 2 邮件  3 微信 4 app推送*/
    @NotNull
    private String send_type;
    /**渠道来源*/
    @NotNull
    private String channel_type;
    /**备注，建议业务系统调用传业务ID或系统编号，否则不支持细化统计*/
    private String remark;
    /**
     * 服务厂商定制, 非业务参数
     */
    private String service_vender;
}