package com.cairh.cpe.esb.component.elect.dto.req;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
@Accessors(chain = true)
public class ElectUploadImageRequest implements Serializable {
    private static final long serialVersionUID = 8141269737422562094L;
    /**图片base64*/
    private String base64_image;
    /**图片名称*/
    private String image_name;
    /**备注*/
    private String remark;

    /**
     * 文文件归档类型(1-资源文件；2-普通文件；3-公告文件 默认值：2)
     */
    private String file_arch_type;

    /**
     * 服务厂商定制, 非业务参数
     */
    private String service_vender;
}
