package com.cairh.cpe.esb.component.elect.dto.resp;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
@Accessors(chain = true)
public class ElectDownloadImageMultResponse implements Serializable {
    private static final long serialVersionUID = 6104176745624840843L;
    /**图片base64 list*/
    private List<String> base64_images;

    /**图片http_url list*/
    private List<String> http_urls;

    /** 图片名 **/
    private String image_names;
}
