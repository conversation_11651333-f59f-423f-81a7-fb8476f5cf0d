package com.cairh.cpe.esb.component.video.dto.resp.support;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 视频话术配置
 */
@Data
public class VideowordsConfig implements Serializable {
        private static final long serialVersionUID = -8131381564552983167L;
        /**
         * id
         */
        private String serial_id;

        /**
         * 所属视频话术模板id
         */
        private String videowordsmodel_id;

        /**
         * 话术类型
         */
        private String words_type;

        /**
         * 话术内容
         */
        private String words_content;

        /**
         * 状态
         */
        private String status;

        /**
         * 音频文件id
         */
        private String voice_fileid;

        /**
         * 排序
         */
        private Long order_no;

        /**
         * 创建人
         */
        private String create_by;

        /**
         * 创建时间
         */
        private Date create_datetime;

        /**
         * 修改人
         */
        private String modify_by;

        /**
         * 答案黑名单
         */
        private String error_answer;

        /**
         * 答案白名单
         */
        private String correct_answer;

        /**
         * 修改时间
         */
        private Date modify_datetime;

}
