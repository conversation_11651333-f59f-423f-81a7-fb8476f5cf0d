package com.cairh.cpe.esb.component.csdc.dto.request;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022-11-07
 */
@Data
public class SecAcctLnkMgrReq implements Serializable {

    private static final long serialVersionUID = -5452674261015331747L;

    /**
     * 用户名
     */
    private String user_name;

    /**
     * 证件号
     */
    private String id_no;

    /**
     * 营业部编号
     */
    private String branch_no;

    /**
     * 证件类型，预留参数
     */
    private String id_kind = "0";

    /**
     * 一码通号码
     */
    private String ymth;


    /**
     * 证券账户类别
     */
    private String zhlb;

    /**
     * 证券账户号码
     */
    private String zqzh;

    /**
     * 业务类别 01：关联关系确认 02：关联关系转挂
     */
    private String ywlb;

    /**
     * 新一码通账户号码 关联关系转挂必填, 关联关系确认不填  关联关系转挂必填, 关联关系确认不填
     */
    private String xymth;
}