package com.cairh.cpe.esb.component.idverify.dto.req;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc 银行卡认证入参
 * @date 2022-05-12
 */
@Data
public class VerifyBankRequest implements Serializable {

    /**
     * 证件号
     */
    @NotNull(message = "证件号不能为空")
    private String id_no;

    /**
     * 证件类别
     */
    private String id_kind;

    /**
     * 姓名
     */
    @NotNull(message = "姓名不能为空")
    private String name;

    /**
     * 银行卡号
     */
    @NotNull(message = "银行卡号不能为空")
    private String bank_card;

    /**
     * 实时标志 默认为0。传入1时实时调用底层公安接口，不取本地数据
     */
    private String realtime_flag;

    /**
     * 操作员营业部编号
     */
    private String op_branch_no;

    /**
     * 操作员营业部名称
     */
    private String op_branch_name;

    /**
     * 用户营业部名称
     */
    private String branch_name;

    /**
     * 操作员编号
     */
    private String operator_no;

    /**
     * 操作员姓名
     */
    private String operator_name;

    /**
     * 营业部编号
     */
    private String branch_no = "0";
}