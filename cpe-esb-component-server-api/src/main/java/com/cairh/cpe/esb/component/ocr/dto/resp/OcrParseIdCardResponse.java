package com.cairh.cpe.esb.component.ocr.dto.resp;

import lombok.Data;

import java.io.Serializable;

/**
 * OCR识别出参(详情:http://cf.cairenhui.com/display/DevelopKS/comp.ocr.parseIdCard)
 *
 * <AUTHOR>
 */
@Data
public class OcrParseIdCardResponse implements Serializable {

    private static final long serialVersionUID = 8939336966395476836L;
    //客户姓名
    private String client_name;

    //客户性别
    private String client_gender;

    //证件号码
    private String id_no;

    //起始日期
    private Integer id_begindate;

    //结束日期
    private Integer id_enddate;

    //签发机关
    private String issued_depart;

    //证件地址
    private String id_address;

    //出生日期
    private Integer birthday;

    //民族
    private String nation_id;

    //国籍地区
    private String nationality;

    //换证次数
    private Integer exchange_times;

    //通行证号码
    private String pass_number;

    //港澳台身份证号码
    private String gat_id_no;

    //签发地点
    private String issued_place;

    //MRZ码
    private String mrz_no;

    //名字中文拼音
    private String name_pinyin;

    //发行日期
    private Integer release_date;

    //职业
    private String occupation;

    /**
     * 质量类型 [0:无，1：复印件，2：拍屏，3：假证件，4：有水印，5：遮挡，6：切边，7：卡变形，
     *         8:有光斑 9:临时身份证 10:模糊 11:非二代身份证 12:身份证图片颠倒 13:信息不合法
     *         14:卡片正反面参数与实际传入图片不匹配 ]
     */
    private String quality_kind;

    /**
     * 质量描述
     */
    private String quality_desc;

    /**
     * 转换后的身份证base64
     */
    private String crop_image;

    /**
     * 正反面 1:人像面，2:国徽面
     */
    private String face;
    /**
     * 英文姓名
     */
    private String english_name;
    /**
     * 持证人曾持有号码
     */
    private String previous_holder_number;
}
