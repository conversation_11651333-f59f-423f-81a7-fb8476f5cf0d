package com.cairh.cpe.esb.component.elect.dto.resp;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@Accessors(chain = true)
public class ElectDownloadImageResponse implements Serializable {
    private static final long serialVersionUID = 6104176745624840843L;
    /**图片base64*/
    @NotNull
    private String base64_image;

    private String image_name;

    private String http_url;
}
