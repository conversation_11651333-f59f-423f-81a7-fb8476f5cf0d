package com.cairh.cpe.esb.component.voice;

import com.cairh.cpe.esb.component.voice.dto.req.TtsRequest;
import com.cairh.cpe.esb.component.voice.dto.req.AsrRequest;

/**
 * <AUTHOR>
 * @since 2022-07-12
 */
public interface IEsbComponentVoiceDubboService {

    /**
     * tts function
     *
     * @param request {@link TtsRequest}
     * @return bytes of speech
     */
    byte[] tts(TtsRequest request);

    /**
     * asr function
     *
     * @param request {@link AsrRequest}
     * @return recognition content
     */
    String asr(AsrRequest request);
}
