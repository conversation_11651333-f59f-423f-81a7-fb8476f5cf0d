package com.cairh.cpe.esb.component.csdc.dto.response;

import com.cairh.cpe.esb.component.csdc.dto.response.entity.CsdcOldAcctLnkQry;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-11-07
 */
@Data
public class SecAcctLnkMgrResp implements Serializable {

    private static final long serialVersionUID = 7278476376909693860L;

    /**
     *错误编码
     */
    private String error_no;

    /**
     * 错误信息
     */
    private String error_info;

    /**
     * 错误信息
     */
    private String deal_info;

    /**
     * 数据体
     *[{
     *
     * YWLSH: "" 业务流水号
     * YWLB: "" 业务类别
     * YMTH: "" 一码通账户号码
     * XYMTH: "" 新一码通账户号码
     * ZHLB: "" 证券账户类别
     * ZQZH: "" 证券账户号码
     * ZJLB: "" 主要身份证明文件类别
     * ZJDM: "" 主要身份证明文件代码
     * GLGXBS: "" 关联关系确认标识
     * KHJGDM: "" 业务发起开户代理机构代码
     * KHWDDM: "" 业务发起开户代理网点代码
     * SQRQ: "" 申请日期
     * YWRQ: "" 业务日期
     * YWPZBS: "" 业务凭证报送标识
     * JGDM: "" 结果代码
     * JGSM: "" 结果说明
     *
     * }]
     */
    private String result_list;

    private List<CsdcOldAcctLnkQry> data;
}