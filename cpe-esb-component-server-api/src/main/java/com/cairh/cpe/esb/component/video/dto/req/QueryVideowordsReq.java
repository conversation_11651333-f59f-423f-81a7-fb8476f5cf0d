package com.cairh.cpe.esb.component.video.dto.req;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class QueryVideowordsReq implements Serializable {
    private static final long serialVersionUID = -5994810081572024383L;

    //视频方式 Y
    private String video_type;

    //规则表达式 N
    private String regular_data;

    //业务类型 Y
    private Integer busin_type;

    //业务系统 Y
    private Integer subsys_no;
}
