package com.cairh.cpe.esb.component.elect.dto.req;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022-06-02
 */
@Accessors(chain = true)
@Data
public class ElectUploadImageByUriRequest implements Serializable {

    private static final long serialVersionUID = 3932276625313831903L;
    /**图片地址 图片地址可能是磁盘路径或网络地址*/
    @NotNull
    private String file_path;

    /**
     * 备注
     */
    private String remark;

    /**
     * 文文件归档类型(1-资源文件；2-普通文件 默认值：2)
     */
    private String file_arch_type;

    /**
     * 当文件不存在时是否创建文件 默认为false
     */
    private boolean created_file_flag;

    /**
     * 服务厂商定制, 非业务参数
     */
    private String service_vender;
}