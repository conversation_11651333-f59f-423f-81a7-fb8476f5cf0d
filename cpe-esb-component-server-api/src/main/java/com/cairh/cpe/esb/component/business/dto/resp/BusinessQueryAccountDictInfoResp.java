package com.cairh.cpe.esb.component.business.dto.resp;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 账户标签字典信息查询
 *
 * <AUTHOR>
 * @since 2025/8/27 14:47
 */
@Data
public class BusinessQueryAccountDictInfoResp implements Serializable {
    private static final long serialVersionUID = 1L;

    List<T2_10231081_Resp> data;

    @Data
    public class T2_10231081_Resp implements Serializable {
        /**
         * 字典类型
         */
        private String dict_type;

        /**
         * 标签级别
         */
        private String access_level;

        /**
         * 子项名称
         */
        private String dict_prompt;

        /**
         * 入参的字典类别
         */
        private String dict_entry;

        /**
         * 营业部编号
         */
        private String branch_no;

        /**
         * 子项编码（对应不规范标签代码）
         */
        private String subentry;

    }

}
