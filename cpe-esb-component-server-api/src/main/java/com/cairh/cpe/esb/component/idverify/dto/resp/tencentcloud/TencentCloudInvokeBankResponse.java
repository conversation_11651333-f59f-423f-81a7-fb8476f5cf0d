package com.cairh.cpe.esb.component.idverify.dto.resp.tencentcloud;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 腾讯云-特化 银行卡三要素核验 response
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class TencentCloudInvokeBankResponse implements Serializable {

    /**
     * 业务错误码，成功情况返回Success, 错误情况请参考下方错误码 列表中FailedOperation部分
     */
    private String result;

    /**
     * 业务结果描述。
     */
    private String description;

    /**
     * 请求标识
     *
     * 每次请求都会返回
     */
    private String request_id;
}
