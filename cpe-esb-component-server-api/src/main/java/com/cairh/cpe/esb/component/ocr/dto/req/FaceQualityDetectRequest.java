package com.cairh.cpe.esb.component.ocr.dto.req;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 人像质量检测
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class FaceQualityDetectRequest implements Serializable {

    /**
     * 人像base64
     */
    @NotNull
    private String base64_image;

    /**
     * 建议业务系统调用传业务ID或系统编号, 否则不支持细化统计
     */
    private String remark;

    /**
     * 服务厂商定制, 非业务参数
     */
    private String service_vender;
}
