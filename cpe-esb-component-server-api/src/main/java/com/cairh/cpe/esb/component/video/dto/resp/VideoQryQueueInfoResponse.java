package com.cairh.cpe.esb.component.video.dto.resp;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * comp.video.qryQueueInfo(详见http://cf.cairenhui.com/display/DevelopKS/comp.video.qryQueueInfo)
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class VideoQryQueueInfoResponse implements Serializable {

    private static final long serialVersionUID = -6494322425267260389L;
    //视频联通参数(status=1时,此字段有值)
    private String json_params;

    //排队总人数
    private Long waitNum;

    //当前排队位置
    private Long waitPosition;

    //用户状态(0:排队等待;1:已匹配;2:视频中)
    private String status;

    /**
     * 任务编号
     */
    private String task_id;

    /**
     * 任务状态
     */
    private String task_status;

    /**
     * 任务类型
     */
    private String task_type;

    /**
     * 处理内容
     */
    private String op_content;
}
