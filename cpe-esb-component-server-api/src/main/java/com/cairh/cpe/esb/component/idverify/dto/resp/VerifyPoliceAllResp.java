package com.cairh.cpe.esb.component.idverify.dto.resp;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022-10-19
 */
@Data
@Accessors(chain = true)
public class VerifyPoliceAllResp implements Serializable {

    private static final long serialVersionUID = 123987595930616663L;

    /**
     * 公安认证记录id
     */
    private String idverifyrecord_id;

    /**
     * 文件记录id
     */
    private String filerecord_id;

    /**
     * 人像图片
     */
    private String image_data;

    /**
     * 认证状态  公安认证状态  0-失败 1-通过
     */
    private String status;

    /**
     * 认证结果信息
     */
    private String result_info;

    /**
     * 认证额外信息
     */
    private String ext_info;

    /**
     * 认证分数
     */
    private String score;

    private String idc_result;
    private String face_result;
    private String auth_result;
    private String auth_info;

    /**
     * 性别
     */
    private String usreName;

    /**
     * 证件号码
     */
    private String id_no;

}