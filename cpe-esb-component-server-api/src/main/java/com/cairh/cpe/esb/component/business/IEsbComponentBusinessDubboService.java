package com.cairh.cpe.esb.component.business;

import com.cairh.cpe.esb.component.business.dto.req.*;
import com.cairh.cpe.esb.component.business.dto.resp.*;

/**
 * 业务组件dubbo接口
 *
 * <AUTHOR>
 * @since 2025/7/9 09:57
 */
public interface IEsbComponentBusinessDubboService {


    /**
     * 查询客户资金账户信息
     *
     * @param request 查询客户资金账户信息请求
     * @return 查询客户资金账户信息响应
     */
    BusinessQueryClientAccountResp businessQueryClientAccount(BusinessQueryClientAccountReq request);

    /**
     * 账户标签信息查询
     *
     * @param request 账户标签信息查询请求
     * @return 账户标签信息响应
     */
    BusinessQueryAccountLabelInfoResp businessQueryAccountLabelInfo(BusinessQueryAccountLabelInfoReq request);

    /**
     * 客户一户通号的适当性信息查询
     *
     * @param request 客户一户通号的适当性信息查询请求
     * @return 客户一户通号的适当性信息查询响应
     */
    BusinessQueryAccountAppropriateInfoResp businessQueryAccountAppropriateInfo(BusinessQueryAccountAppropriateInfoReq request);


    /**
     * 查询账户标签信息是否存在
     *
     * @param request 查询账户标签信息是否存在信息请求
     * @return 查询账户标签信息是否存在响应
     */
    BusinessExistsClientAccountTagResp businessExistsClientAccountTag(BusinessExistsClientAccountTagReq request);

    /**
     * 账户标签字典信息查询
     *
     * @param request 账户标签字典信息查询请求
     * @return 账户标签字典信息查询响应
     */
    BusinessQueryAccountDictInfoResp businessQueryAccountDictInfo(BusinessQueryAccountDictInfoReq request);
}
