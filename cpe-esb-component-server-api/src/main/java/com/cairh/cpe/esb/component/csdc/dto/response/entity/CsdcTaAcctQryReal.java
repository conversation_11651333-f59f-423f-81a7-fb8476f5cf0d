package com.cairh.cpe.esb.component.csdc.dto.response.entity;

import lombok.Data;

import java.io.Serializable;

/**
 中登TA账户查询真实实体 */
@Data
public class CsdcTaAcctQryReal implements Serializable {

	private static final long serialVersionUID = 357850642661182765L;

	/**
	 * 数据体
	 [{
	 YWLSH; "" 业务流水号
	 TAKHMC; ""; TA客户名称
	 TAZJLB; ""; TA身份证明文件类别
	 TAZJDM; ""; TA身份证明文件代码
	 KHJGDM; "" 业务发起开户代理机构代码
	 KHWDDM; "" 业务发起开户代理网点代码
	 SQRQ; "" 申请日期
	 BYZD; "" 备用字段
	 TAZH; ""; TA账户号码
	 TAZHLB; ""; TA账户类别
	 JGDM; "" 结果代码
	 JGSM; "" 结果说明
	 }]
	 */

	/**
	 * 业务流水号
	 */
	private String  ywlsh;
	/**
	 * TA客户名称
	 */
	private String  takhmc;
	/**
	 * TA身份证明文件类别
	 */
	private String  tazjlb;
	/**
	 * TA身份证明文件代码
	 */
	private String  tazjdm;

	/**
	 * 业务发起开户代理机构代码
	 */
	private String  khjgdm;

	/**
	 * 业务发起开户代理网点代码
	 */
	private String  khwddm;

	/**
	 * 申请日期
	 */
	private String  sqrq;

	/**
	 * 备用字段
	 */
	private String  byzd;

	/**
	 * TA账户号码
	 */
	private String  tazh;

	/**
	 * TA账户类别
	 */
	private String  tazhlb;

	/**
	 * 结果代码
	 */
	private String  jgdm;

	/**
	 * 结果说明
	 */
	private String  jgsm;



}