package com.cairh.cpe.esb.component.idverify;

import com.cairh.cpe.esb.component.idverify.dto.req.tencentcloud.*;
import com.cairh.cpe.esb.component.idverify.dto.resp.tencentcloud.*;

/**
 * 组件 腾讯云-特化 调用
 *
 * <AUTHOR>
 */
public interface ITencentCloudComponentIdVerifyDubboService {

    /**
     * 核身鉴权
     *
     * 用于人脸核身前的验证
     *
     * @param request {@link TencentCloudInvokeDetectAuthRequest}
     * @return {@link TencentCloudInvokeDetectAuthResponse}
     */
    TencentCloudInvokeDetectAuthResponse detectAuth(TencentCloudInvokeDetectAuthRequest request);

    /**
     * 获取核身信息
     *
     * 用于获取核身结果
     *
     * @param request {@link TencentCloudInvokeGetDetectInfoRequest}
     * @return {@link TencentCloudInvokeGetDetectInfoResponse}
     */
    TencentCloudInvokeGetDetectInfoResponse getDetectInfo(TencentCloudInvokeGetDetectInfoRequest request);

    /**
     * 传入照片和身份信息，判断该照片与公安权威库的证件照是否属于同一个人
     * @param request
     * @return
     */
    TencentCloudInvokeImageRecognitionResponse ImageRecognition(TencentCloudInvokeImageRecognitionRequest request);

    /**
     * 银行卡三要素核验
     * @param request
     * @return
     */
    TencentCloudInvokeBankResponse bankAuth(TencentCloudInvokeBankRequest request);

    /**
     * 手机号三要素核验
     * @param request
     * @return
     */
    TencentCloudInvokePhoneResponse mobileAuth(TencentCloudInvokePhoneRequest request);
}
