package com.cairh.cpe.esb.component.voice.dto.resp;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 异步语音识别 发起 response
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SpeechRecognitionSponsorResponse implements Serializable {

    /**
     * 文件标识
     *
     * 服务方 语音文件标识, 用于服务方唯一标识语音文件 取得识别文本的依赖
     */
    private String task_identity;

    /**
     * 本地音频位置
     *
     * 某些厂商需要提供音频地址以进行识别, 在得到结果后需要进行删除
     */
    private String audio_path;
}
