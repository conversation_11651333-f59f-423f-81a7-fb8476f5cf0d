package com.cairh.cpe.esb.component.agreement.dto.req;

import com.cairh.cpe.esb.component.elect.dto.req.CfcaFileInfo;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

@Data
public class CaRequest implements Serializable {

    private static final long serialVersionUID = 777481460040649856L;

    /**
     * 协议签署编号
     */
    @NotBlank(message = "invalid parameter agreementsign_id")
    private String agreementsign_id;

    /**
     * 客户姓名
     */
    @NotBlank(message = "invalid parameter user_name")
    private String user_name;

    /**
     * 证件号码
     */
    @NotBlank(message = "invalid parameter id_no")
    private String id_no;

    /**
     * 证件类型
     */
    @NotBlank(message = "invalid parameter id_kind")
    private String id_kind;

    /**
     * 电话
     */
    @NotBlank(message = "invalid parameter mobile_tel")
    private String mobile_tel;

    /**
     * 手写印章编号
     */
    private String ca_seal_id;

    /**
     * 用户userId
     */
    private String ca_user_id;

    /**
     * 证件开始日期 （金汇厂商必填）
     */
    private String id_begindate;

    /**
     * 证件失效日期 （金汇厂商必填）
     */
    private String id_enddate;

    /**
     * 认证方式 （金汇厂商必填）
     */
    private String auth_type;

    /**
     * 邮箱 （金汇厂商必填）
     */
    private String e_mail;

    /**
     * ip地址 （金汇厂商必填）
     */
    private String ip_address;

    /**
     * mac地址 （金汇厂商必填）
     */
    private String mac_address;

    /**
     * 终端类型 （金汇厂商必填）
     */
    private String terminal_way;

    /**
     * 补充的签署文件（CFCA产商时传参）
     */
    private List<CfcaFileInfo> cfca_file_infos;

    /**
     * 文件流
     */
    private byte[] file;

    /**
     * 签名区域配置
     */
    private String sign_pos;

    /**
     * 签章区域配置
     */
    private String seal_pos;

    /**
     * 协议名称
     */
    private String agreement_name;

    /**
     * 第三方协议ID
     */
    private String third_agreement_id;

    /**
     * 自定义替换通配符字段 如果有套打字段，此字段需要传json格式业务数据集合，如：{"curr_date":"20190508","op_branch_no_name":"test1"}
     */
    private String replace_str;
}
