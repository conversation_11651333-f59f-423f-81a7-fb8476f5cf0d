package com.cairh.cpe.esb.component.elect.dto.req;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class ElectUploadFileRequest implements Serializable {

    private static final long serialVersionUID = 1649527243434385418L;
    /**
     * 文件流
     */
    private byte[] file;

    /** 视频文件base64串 */
    private String video_data;

    /**
     * 文件名
     */
    private String file_name;

    /**
     * 备注
     */
    private String remark;

    /**
     * 文件归档类型(1-资源文件；2-普通文件 默认值：2)
     */
    private String file_arch_type;

    /**
     * 服务厂商定制, 非业务参数
     */
    private String service_vender;

    /**
     * 是否根据image_name命名
     * 0或空保持之前逻辑，1：根据入参image_name命名
     */
    private String is_use_income;
    /**
     * 文件夹
     */
    private String dir;
}
