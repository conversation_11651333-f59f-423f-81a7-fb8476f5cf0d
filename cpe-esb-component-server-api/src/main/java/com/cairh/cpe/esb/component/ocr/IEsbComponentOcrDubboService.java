package com.cairh.cpe.esb.component.ocr;

import com.cairh.cpe.esb.component.ocr.dto.req.*;
import com.cairh.cpe.esb.component.ocr.dto.resp.*;

import java.util.List;

public interface IEsbComponentOcrDubboService {

    /**
     * OCR识别(dubbo)
     *
     * @param request {@link OcrParseIdCardRequest}
     * @return {@link OcrParseIdCardResponse}
     */
    OcrParseIdCardResponse ocrParse(OcrParseIdCardRequest request);

    /**
     * OCRBankCard识别(dubbo)
     *
     * @param request {@link OcrParseBankCardRequest}
     * @return {@link OcrParseBankCardResponse}
     */
    OcrParseBankCardResponse ocrParseBankCard(OcrParseBankCardRequest request);

    /**
     * OCRBankCheck校验（dubbo）
     * @param request
     * @return
     */
    OcrParseBankCardCheckResponse ocrParseBankCardCheck(OcrParseBankCardCheckRequest request);

    /**
     * OCRBusinessLisence识别(dubbo)
     *
     * @param request {@link OcrParseBusinessLisenceRequest}
     * @return {@link OcrParseBusinessLisenceResponse}
     */
    OcrParseBusinessLisenceResponse ocrParseBusinessLisence(OcrParseBusinessLisenceRequest request);

    /**
     * ocrImageQuality(dubbo)
     *
     * @param request {@link OcrImageQualityRequest}
     * @return {@link OcrImageQualityResponse}
     */
    OcrImageQualityResponse ocrImageQuality(OcrImageQualityRequest request);

    /**
     * 国联安OCR识别
     * @param request
     * @return
     */
    OcrDoubleSideParseIdCardResponse ocrDoubleSideParse(OcrDoubleSideParseIdCardRequest request);

    /**
     * OCR通用识别
     * @param request
     * @return
     */
    List<OcrParseNormalResponse> ocrParseNormal(OcrParseNormalRequest request);

}
