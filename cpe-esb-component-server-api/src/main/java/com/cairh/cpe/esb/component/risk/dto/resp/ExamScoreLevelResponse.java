package com.cairh.cpe.esb.component.risk.dto.resp;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ExamScoreLevelResponse implements Serializable {
    private static final long serialVersionUID = -485258513470939950L;
    /**最低分数*/
    private BigDecimal min_score;
    /**最高分数*/
    private BigDecimal max_score;
    /**等级名称*/
    private String result_comment;
    /**风险等级*/
    private String risk_level;
    private Integer risk_position;
    /**投资建议*/
    private String invest_advice;
    /**有效期*/
    private Integer valid_days;
}
