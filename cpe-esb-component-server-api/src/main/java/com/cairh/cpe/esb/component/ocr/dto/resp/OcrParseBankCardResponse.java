package com.cairh.cpe.esb.component.ocr.dto.resp;

import lombok.Data;

import java.io.Serializable;

/**
 * OCR银行卡识别出参(详情:http://cf.cairenhui.com/display/DevelopKS/comp.ocr.parseBankCard)
 *
 */
@Data
public class OcrParseBankCardResponse implements Serializable {
    private static final long serialVersionUID = 9133240030337929201L;
    /**银行名称，如：农业银行*/
    private String bank_name;

    /**卡片名称，如：牡丹卡普卡*/
    private String card_name;

    /**卡片类型，如：储蓄卡、借记卡*/
    private String card_type;

    /**卡号*/
    private String card_no;

    /**有效期截止月份，如：09*/
    private String exp_month;

    /**有效期截止年份，如：28*/
    private String exp_year;
}
