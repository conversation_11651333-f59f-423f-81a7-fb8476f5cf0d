package com.cairh.cpe.esb.component.ocr.dto.req;

import lombok.Data;

import java.io.Serializable;

@Data
public class OcrImageQualityRequest implements Serializable {

    /**
     * 图片base64字符串
     */
    private String image_data;

    /**
     * 图片类型 .jpg, .png, .gif, .bmp, .jpeg
     */
    private String image_type;

    /**
     * 图片类型	6A:身份证正面，6B:身份证反面
     */
    private String image_no;

    /**
     * 服务厂商定制, 非业务参数
     */
    private String service_vender;

    /**
     * 身份证遮挡检测
     */
    private String border_check;

    /**
     * 翻拍检测
     */
    private String reshoot;

    /**
     * 复印件检测
     */
    private String detect_copy;

    /**
     * PS检测
     */
    private String detect_ps;

    /**
     * 是否返回人像图片
     */
    private String ret_portrait;

    /**
     * 是否开启多卡识别
     */
    private String multicard_det;

    /**
     * 是否返回识别后的切图
     */
    private String ret_image;

    /**
     * 是否返回多重告警码
     */
    private String recognize_warn_code;

    /**
     * 是否返回图片质量数值
     */
    private String quality_value;
}
