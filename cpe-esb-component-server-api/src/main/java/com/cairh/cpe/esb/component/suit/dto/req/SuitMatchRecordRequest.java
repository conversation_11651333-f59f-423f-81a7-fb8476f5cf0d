package com.cairh.cpe.esb.component.suit.dto.req;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class SuitMatchRecordRequest{

    /**
     * 操作时间开始
     */
    private String operator_time_start;
    /**
     * 操作时间结束
     */
    private String operator_time_end;

    /**
     * 客户编号
     */
    private String client_id;
    /**
     * 客户类型
     */
    private String client_type;
    /**
     * 客户姓名
     */
    private String client_name;
    /**
     * 证件类别
     */
    private String id_kind;
    /**
     * 证件号码
     */
    private String id_no;
    /**
     * 修改结果
     */
    private String modify_flag;
}
