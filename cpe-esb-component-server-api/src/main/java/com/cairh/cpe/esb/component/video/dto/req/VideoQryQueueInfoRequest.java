package com.cairh.cpe.esb.component.video.dto.req;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * comp.video.qryQueueInfo(详见http://cf.cairenhui.com/display/DevelopKS/comp.video.qryQueueInfo)
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class VideoQryQueueInfoRequest implements Serializable {

    private static final long serialVersionUID = 7562725927306543916L;
    //唯一ID Y
    private String unique_id;

    //子系统编号 Y
    private String subsys_no;

    //姓名 Y
    private String user_name;

    //证件类型 Y
    private String id_kind;

    //证件号码 Y
    private String id_no;

    //客户号 N
    private String client_id;

    //资金账号 N
    private String fund_account;

    //手机号 N
    private String mobile_tel;
}
