package com.cairh.cpe.esb.component.elect.dto.resp;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@Accessors(chain = true)
public class ElectSealThirdResponse implements Serializable {
    private static final long serialVersionUID = 0412566256454044062010L;
    /**文件流*/
    @NotNull
    private byte[] file;
}
