package com.cairh.cpe.esb.component.voice.dto.req;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022-07-12
 */
@Data
@Accessors(chain = true)
public class TtsRequest implements Serializable {

    // the text
    private String voiceText;
    // 语速
    private Integer speech_rate;

    /**
     * 服务厂商定制, 非业务参数
     */
    private String service_vender;
}
