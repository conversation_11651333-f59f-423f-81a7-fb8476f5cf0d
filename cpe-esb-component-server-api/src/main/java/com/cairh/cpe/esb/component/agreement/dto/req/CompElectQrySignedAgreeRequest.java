package com.cairh.cpe.esb.component.agreement.dto.req;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
@Accessors(chain = true)
public class CompElectQrySignedAgreeRequest implements Serializable {
    private static final long serialVersionUID = -4192139887084576164L;
    @NotBlank(message = "invalid parameter elecagreesign_ids")
    private String elecagreesign_ids;
}
