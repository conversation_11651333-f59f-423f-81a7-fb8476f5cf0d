package com.cairh.cpe.esb.component.risk.dto.req;

import lombok.Data;

import java.io.Serializable;

@Data
public class ExamConfirmExamResultRequest implements Serializable {
    private static final long serialVersionUID = -6067613052219617707L;
    private String examtestresult_id;
    /**
     * 海通专用 风险测评流水号
     */
    private String buz_order_no;
    private String op_entrust_way;
    private String operator_no;
    private String client_id;
    private String op_station;
    private String channel_code;

    /**
     * T2提交柜台参数
     */
    private String branch_no;
    /**
     * T2提交柜台参数
     */
    private String user_name;
    /**
     * T2提交柜台参数
     */
    private String id_kind;
    /**
     * T2提交柜台参数
     */
    private String id_no;
    /**
     * T2提交柜台参数
     */
    private String organ_flag;
    /**
     * T2提交柜台参数
     */
    private String prodta_no;

    /**
     * T2柜台使用，风险问卷是否走适当性接口
     */
    private boolean call_suit;

    // 渠道来源
    private String resType;
}
