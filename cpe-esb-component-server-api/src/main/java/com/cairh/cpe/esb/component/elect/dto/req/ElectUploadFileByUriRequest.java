package com.cairh.cpe.esb.component.elect.dto.req;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@Accessors(chain = true)
public class ElectUploadFileByUriRequest implements Serializable {
    private static final long serialVersionUID = 2236083082777728922L;
    /**文件地址 文件地址可能是磁盘路径或网络地址*/
    @NotNull
    private String file_path;
    /**备注*/
    private String remark;

    /**
     * 文件后缀，当传了该值时下载文件后缀名以该值为准(例子：pdf)
     */
    private String file_name_suffix;

    /**
     * 文件归档类型(1-资源文件；2-普通文件 默认值：2)
     */
    private String file_arch_type;

    /**
     * 当文件不存在时是否创建文件 默认为false
     */
    private boolean created_file_flag;

    /**
     * 不需要下载文件
     */
    private boolean not_down;

    /**
     * 服务厂商定制, 非业务参数
     */
    private String service_vender;
}
