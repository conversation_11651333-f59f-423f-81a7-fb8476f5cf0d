package com.cairh.cpe.esb.component.ocr.dto.req;

import lombok.Data;

import java.io.Serializable;

/**
 * OCR识别入参(详情:http://cf.cairenhui.com/display/DevelopKS/comp.ocr.parseIdCard)
 *
 * <AUTHOR>
 */
@Data
public class OcrParseIdCardRequest implements Serializable {

    private static final long serialVersionUID = 7823043910909035007L;
    //图像类型
    private String pic_type;

    //图像base64
    private String base64_image;

    //备注
    private String remark;

    /**
     * 服务厂商定制, 非业务参数
     */
    private String service_vender;

    /**
     * 图片类型	6A:身份证正面，6B:身份证反面
     */
    private String image_no;

    /**
     * 是否返回切割头像(0:不返回（默认），1：返回)
     */
    private String crop_face_image;

    /**
     * 是否对图像进行合法性校验 0：不校验，1：校验
     */
    private String check_image;

    /**
     * 强制进行所有合法性校验 0：否，1：是
     */
    private String check_all_risks;
}
