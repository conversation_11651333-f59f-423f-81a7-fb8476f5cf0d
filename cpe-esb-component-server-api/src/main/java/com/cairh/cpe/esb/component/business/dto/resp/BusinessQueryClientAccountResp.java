package com.cairh.cpe.esb.component.business.dto.resp;

import lombok.Data;

import java.io.Serializable;

/**
 * 查询客户资金账户信息返回信息
 *
 * <AUTHOR>
 * @since 2025/7/9 10:06
 */
@Data
public class BusinessQueryClientAccountResp implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 客户编号
     */
    private String client_id;

    /**
     * 分支机构
     */
    private String branch_no;

    /**
     * 业务资金账户
     */
    private String busin_account;

    /**
     * 一户通账户
     */
    private String cif_account;

    /**
     * 客户代码状态
     * 0:正常 非0 异常
     */
    private String client_status = "0";

    /**
     * 资金账号状态
     * 0:正常 非0 异常
     */
    private String businacct_status = "0";
}
