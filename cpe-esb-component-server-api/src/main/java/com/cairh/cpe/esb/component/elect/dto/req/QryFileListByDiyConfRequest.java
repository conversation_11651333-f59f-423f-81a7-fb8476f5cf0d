package com.cairh.cpe.esb.component.elect.dto.req;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;


@Data
@Accessors(chain = true)
public class QryFileListByDiyConfRequest implements Serializable {

    private static final long serialVersionUID = -285105746143999866L;

    /**
     * 文件夹地址
     */
    @NotBlank(message = "invalid parameter httpaddress!")
    private String httpaddress;

    /**
     * 是否跳过缩略图(默认跳过)
     */
    private Boolean includeThumbnail = true;

    /**
     * 文件服务配置
     */
    @NotBlank(message = "invalid parameter param_json!")
    private String param_json;

    /**
     * 服务厂商定制, 非业务参数
     */
//    @NotBlank(message = "invalid parameter service_vender!")
    private String service_vender;
}
