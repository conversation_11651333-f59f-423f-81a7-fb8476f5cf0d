package com.cairh.cpe.esb.component.agreement.dto.req;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
@Accessors(chain = true)
public class CompElectAgainSignAgreeRequest implements Serializable {

    private static final long serialVersionUID = -514182170630894624L;
    private String elecagreesign_id;
    private String render_data;
    /*1-异步，0-同步 默认异步*/
    private String async_flag;
}
