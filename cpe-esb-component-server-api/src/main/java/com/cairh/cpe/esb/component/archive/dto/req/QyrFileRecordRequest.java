package com.cairh.cpe.esb.component.archive.dto.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023-03-08
 */
@Data
public class QyrFileRecordRequest implements Serializable {

    private static final long serialVersionUID = 369744865769382456L;

    @NotBlank(message = "invalid parameter filerecord_id")
    private String filerecord_id;
}