package com.cairh.cpe.esb.component.csdc.dto.request;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022-11-07
 */
@Data
public class SecAcctUseInfoMgrReq implements Serializable {

    private static final long serialVersionUID = 3320595101042927557L;

    /**
     * 用户名
     */
    private String user_name;

    /**
     * 证件号
     */
    private String id_no;

    /**
     * 营业部编号
     */
    private String branch_no;

    /**
     * 证件类型，预留参数
     */
    private String id_kind = "0";

    /**
     * 业务类别
     * 01： 使用信息新增
     * 02： 使用信息撤销
     * 03： 使用信息查询
     */
    private String ywlb;

    /**
     * 一码通账户号码
     */
    private String ymth = "";

    /**
     * 证券账户类别
     */
    private String zhlb;

    /**
     * 证券账户号码
     */
    private String zqzh;

    /**
     * 交易单元
     */
    private String jydy = "";

    /**
     * 营业部编码
     */
    private String yybbm = "";

}