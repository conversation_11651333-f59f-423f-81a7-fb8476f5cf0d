package com.cairh.cpe.esb.component.voice;

import com.cairh.cpe.esb.component.voice.dto.req.SpeechRecognitionResultRequest;
import com.cairh.cpe.esb.component.voice.dto.req.SpeechRecognitionSponsorRequest;
import com.cairh.cpe.esb.component.voice.dto.resp.SpeechRecognitionResultResponse;
import com.cairh.cpe.esb.component.voice.dto.resp.SpeechRecognitionSponsorResponse;

import java.util.List;

/**
 * 异步 voice
 *
 * 提供了语音的异步能力, 文本异步支持扩展
 *
 * <AUTHOR>
 */
public interface IEsbComponentAsyncVoiceDubboService {

    /**
     * 语音发起
     *
     * @param request {@link SpeechRecognitionSponsorRequest}
     * @return {@link SpeechRecognitionSponsorResponse}
     */
    SpeechRecognitionSponsorResponse speechRecognitionSponsor(SpeechRecognitionSponsorRequest request);

    /**
     * 语音结果
     *
     * 注: 轮询式, 尚未获取到结果按空返回
     *
     * @param request {@link SpeechRecognitionResultRequest}
     * @return {@link SpeechRecognitionResultResponse}
     */
    List<SpeechRecognitionResultResponse> speechRecognitionResult(SpeechRecognitionResultRequest request);
}
