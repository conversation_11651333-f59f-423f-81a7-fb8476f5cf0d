package com.cairh.cpe.esb.component.ocr.dto.resp;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 人像质量检测
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class FaceQualityDetectResponse implements Serializable {

    /**
     * 眼镜类型
     *
     * 0:未戴眼镜 1:普通眼镜 2:墨镜
     */
    private String glass;

    /**
     * 是否戴帽子
     *
     * 0:未戴 1:戴帽子
     */
    private String hat;

    /**
     * 是否戴口罩
     *
     * 0:未戴 1:戴口罩
     */
    private String mask;

    /**
     * 清晰度
     *
     * 0.0~1.0, 越大表示越清晰 (推荐0.65~1.0)
     */
    private Float clarity;
}
