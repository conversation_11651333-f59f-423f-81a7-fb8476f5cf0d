package com.cairh.cpe.esb.component.voice.dto.req;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 异步语音识别 结果 request
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SpeechRecognitionResultRequest implements Serializable {

    /**
     * 文件标识 Y
     *
     * 服务方 语音文件标识, 语音发起返回值
     */
    private String task_id;

    /**
     * 本地音频位置
     *
     * 正常返回后需要进行删除, 于组件本地删除
     */
    private String audio_path;

    /**
     * 服务厂商定制, 非业务参数
     */
    private String service_vender;
}