package com.cairh.cpe.esb.component.csdc.dto.request;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022-11-07
 */
@Data
public class TaAcctQryReq implements Serializable {


    private static final long serialVersionUID = -8798242045596399209L;
    /** TA客户名称 */
    private String takhmc;

    /** TA身份证明文件类别 */
    private String tazjlb;

    /** TA身份证明文件类别 */
    private String tazjdm;

    /** 备用字段 */
    private String byzd = "";

    /** 用户名 */
    private String user_name;

    /** 证件号 */
    private String id_no;

    /** 营业部编号 */
    private String branch_no;

    /** 证件类型，预留参数 */
    private String id_kind = "0";
}