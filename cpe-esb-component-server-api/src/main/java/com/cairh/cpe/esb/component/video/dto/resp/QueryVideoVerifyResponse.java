package com.cairh.cpe.esb.component.video.dto.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 视频验证
 *
 * <AUTHOR>
 * @since 2023/6/7 13:55
 */
@Data
@Accessors(chain = true)
public class QueryVideoVerifyResponse implements Serializable {

    private String serial_id;

    private String unique_id;

    private String verify_status;

    private String reject_info;

    private String remark;

    private String filerecord_id;

    /** 视频时长 */
    private long recTime;
    /** 录制时间 */
    private long video_time;
    /** 视频见证人员工号 */
    private String witness_staff_no;
    /** 视频见证人名称 */
    private String witness_operator_name;
    /** 双录话术 regular_expre(规则表达式) */
    private String regular_expre;

    /** 视频录制开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date begin_datetime;
}
