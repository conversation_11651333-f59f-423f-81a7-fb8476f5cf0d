package com.cairh.cpe.esb.component.ocr.dto.resp;

import lombok.Data;

import java.io.Serializable;

@Data
public class OcrParseBusinessLisenceResponse implements Serializable {
    private static final long serialVersionUID = -4917231159621258454L;
    /**营业执照统一社会信用代码*/
    private String unified_social_credit_code;

    /**公司名称*/
    private String company_name;

    /**公司类型、企业类型*/
    private String company_type;

    /**法定代表人姓名*/
    private String corporate_name;

    /**注册资本*/
    private String register_capital;

    /**成立日期、创建日期，如：20200101*/
    private String build_date;

    /**住所，经营地址*/
    private String address;

    /**经营期限开始日期 如：20200101*/
    private String operate_begindate;

    /**经营期限结束日期 如：20200101或长期*/
    private String operate_enddate;

    /**经营范围*/
    private String operate_scope;

    /**登记注册机关*/
    private String register_depart;

    /**核准日期，对应盖章处日期 如20200101*/
    private String approval_date;

    /**营业状态*/
    private String business_status;

    /**注册号*/
    private String register_no;
}
