package com.cairh.cpe.esb.component.agreement.dto.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
@Data
public class CompElecGetAuthorizationRequest implements Serializable {

    private static final long serialVersionUID = 9189913883010896796L;

    /**
     * 协议签署序列号（多个逗号分隔）
     */
    @NotBlank(message = "agreementsign_ids could not be blank!")
    private String agreementsign_ids;

    /**
     * 市场主体（商兆必传）
     */
    @NotBlank(message = "usci could not be blank!")
    private String usci;

    /**
     * 授权事项编码（商兆必传）
     */
    @NotBlank(message = "invalid parameter tradeType could not be blank!")
    private String tradeType;

    /**
     * 平台用户的电子营业执照证书（sealSubject参数值为 1时必填）
     */
    @NotBlank(message = "entityUser could not be blank!")
    private String entityUser;

    /**
     * 自定义印章名称（商兆必传）
     */
    @NotBlank(message = "sealNames could not be blank!")
    private String sealNames;
}
