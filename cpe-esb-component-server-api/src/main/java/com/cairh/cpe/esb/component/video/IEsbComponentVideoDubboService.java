package com.cairh.cpe.esb.component.video;

import com.cairh.cpe.esb.component.video.dto.req.*;
import com.cairh.cpe.esb.component.video.dto.resp.*;


import java.util.Map;


/**
 * comp.video.joinQueue(详见http://cf.cairenhui.com/display/DevelopKS/comp.video.joinQueue)
 *
 * <AUTHOR>
 */
public interface IEsbComponentVideoDubboService {

    /**
     *  设置最高级别 优先队列
     * @param request
     * @return
     */
    public boolean handleVideoPriority (VideoQryQueueInfoRequest request);
    /**
     * 加入队列
     *
     * @param request {@link VideoJoinQueueRequest}
     */
    void joinQueue(VideoJoinQueueRequest request);

    /**
     * 查询排队位置信息
     *
     * @param request {@link VideoQryQueueInfoRequest}
     * @return 排队位置信息
     */
    VideoQryQueueInfoResponse qryQueueInfo(VideoQryQueueInfoRequest request);

    /**
     * 视频坐席退出排队
     *
     * @param videoOperatorExitQueueInfoRequest
     * <AUTHOR>
     * @since 2023/8/8 10:55
     */
    void disruptionUserVideo(VideoOperatorExitQueueInfoRequest videoOperatorExitQueueInfoRequest);

    /**
     * 视频用户中断房间
     *
     * @param videoOperatorExitQueueInfoRequest
     */
    void interruptVideoByUser(VideoOperatorExitQueueInfoRequest videoOperatorExitQueueInfoRequest);

    void abnormalAnswer (VideoOperatorExitQueueInfoRequest videoOperatorExitQueueInfoRequest);
    /**
     * 退出排队
     *
     * @param request {@link VideoExitQueueInfoRequest}
     */
    void exitQueueInfo(VideoExitQueueInfoRequest request);

    /**
     * 坐席视频接听
     *
     * @param request {@link VideoAnswerUserRequest}
     */
    void answerUserVideo(VideoAnswerUserRequest request);

    /**
     * 用户状态变更(已配对,视频中,已完成)
     *
     * @param subsys_no      系统编号
     * @param unique_id      唯一标识ID
     * @param status         状态
     * @param remote_user_id 坐席ID, 已匹配时传入
     * @param zego_token     即构token
     */
    @Deprecated
    void updateUserStatus(String subsys_no, String unique_id, String status, String remote_user_id, String zego_token);

    void updateUserStatus(String subsys_no, String unique_id, String status, String remote_user_id, String zego_token, String room_id);

    void videoVerityRecord(String subsys_no, String unique_id, String status, String remark, String filerecord_id);

    /**
     * 查询视频模板与视频规则数据
     */
    QueryVideowordsRes compvideoQueryVideowords(QueryVideowordsReq request);

    /**
     * 将各种话术中的占位符替换成为内容
     *
     * @param subsys_no
     * @param unique_id
     * @param from
     * @param response
     * @return
     */
    QueryVideowordsRes compvideoQueryVideowordsToReplaceDate(Integer subsys_no, String unique_id, Map<String, String> from, QueryVideowordsRes response);

    /**
     * 查询视频验证结果
     */
    QueryVideoVerifyResponse queryVideoVerify(QueryVideoVerifyRequest request);

    /**
     * 依据业务参数请求id查询业务参数
     *
     * @param qryBizParamRequest
     * @return QryBizParamResponse
     * <AUTHOR>
     * @since 2023/7/13 10:20
     */
    QryBizParamResponse qryBizParamInfo(QryBizParamRequest qryBizParamRequest);

    /**
     * 开始录制
     */
    StartRecordResponse startRecord(VideoQryQueueInfoRequest request);

    /**
     * 结束录制
     */
    void stopRecord(VideoQryQueueInfoRequest request);

    /**
     * 获取双向视频的水印文本
     *
     * @param operator_no 当前接听的操作员编号
     * @return 返回转义后的水印。返回空字符串，如果没有配置双向视频水印
     */
    String getWatermarkInfo(String operator_no);

    String refreshUserZegoToken(String subsys_no, String unique_id, String status, String remote_user_id, String zego_token, String room_id);

    /**
     * 即构厂商回调接口
     */
    void callback(String jsonString);

    /**
     * 即构视频 上传
     * @param subsys_no
     * @param unique_id
     * @param file_path
     * @return
     */
    String serverUploadFile(String subsys_no, String unique_id,String file_path);


    Integer queryVideoUserNum(String subsys_no);
}
