package com.cairh.cpe.esb.component.ocr.dto.req;

import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.web.multipart.MultipartFile;

/**
 * 活体检测入参
 */
@Data
@Accessors(chain = true)
public class OcrBioassayDetectRequest {
    /**
     * 视频文件(二选一)  Y
     */
    private MultipartFile file;
    /**
     * 视频字节(二选一)  Y
     */
    private String data;
    /**
     * 动作活体检测的动作序   Y
     * 列，支持眨眼、张嘴、点
     * 头、摇头四个动作的任意
     * 组合  'blink'    blink:眨眼  mouth：张嘴   nod：点头   yaw：摇头
     */
    private String sequence;

    /**
     * 是否对视频旋转  f
     */
    private boolean rotate;
}
