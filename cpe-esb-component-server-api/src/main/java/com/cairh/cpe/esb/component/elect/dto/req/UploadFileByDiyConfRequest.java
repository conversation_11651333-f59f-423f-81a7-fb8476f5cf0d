package com.cairh.cpe.esb.component.elect.dto.req;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

@Data
@Accessors(chain = true)
public class UploadFileByDiyConfRequest implements Serializable {

    private static final long serialVersionUID = 3733343138967573015L;
    /**
     * 文件流
     */
    @NotEmpty(message = "invalid parameter file!")
    private byte[] file;

    /**
     * 文件名
     */
    @NotBlank(message = "invalid parameter name!")
    private String name;

    /**
     * 文件夹
     */
    @NotBlank(message = "invalid parameter dir!")
    private String dir;

    /**
     * 文件服务配置
     */
    @NotBlank(message = "invalid parameter param_json!")
    private String param_json;

    /**
     * 服务厂商定制, 非业务参数
     */
    @NotBlank(message = "invalid parameter service_vender!")
    private String service_vender;
}
