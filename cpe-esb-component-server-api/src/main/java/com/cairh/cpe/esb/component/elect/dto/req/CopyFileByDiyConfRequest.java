package com.cairh.cpe.esb.component.elect.dto.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 自定义存储服务配置复制文件或文件夹接口入参
 * <AUTHOR>
 * @since 2023-04-06
 */
@Data
public class CopyFileByDiyConfRequest implements Serializable {

    private static final long serialVersionUID = -8120718789857707573L;

    /**
     * 目标地址
     * 服务器内部路径eg：/elect/dubbo/service2/zhhtest.txt
     */
    @NotBlank(message = "invalid parameter tohttpaddress!")
    private String tohttpaddress;

    /**
     * 源地址
     * 服务器内部路径eg：/elect/dubbo/service2/zhhtest.txt
     */
    @NotBlank(message = "invalid parameter fromhttpaddress!")
    private String fromhttpaddress;

    /**
     * 文件服务配置
     */
    @NotBlank(message = "invalid parameter param_json!")
    private String param_json;

    /**
     * 服务厂商定制, 非业务参数
     */
//    @NotBlank(message = "invalid parameter service_vender!")
    private String service_vender;
}