package com.cairh.cpe.esb.component.ocr.dto.resp;

import lombok.Data;
import java.io.Serializable;

@Data
public class OcrDoubleSideParseIdCardResponse implements Serializable {
    //客户姓名
    private String client_name;

    //客户性别
    private String client_gender;

    //证件号码
    private String id_no;

    //起始日期
    private Integer id_begindate;

    //结束日期
    private Integer id_enddate;

    //签发机关
    private String issued_depart;

    //证件地址
    private String id_address;

    //出生日期
    private Integer birthday;

    //民族
    private String nation_id;

    //国籍地区
    private String nationality;

    //换证次数
    private Integer exchange_times;

    //通行证号码
    private String pass_number;

    //港澳台身份证号码
    private String gat_id_no;

    //签发地点
    private String issued_place;

    //MRZ码
    private String mrz_no;

    //名字中文拼音
    private String name_pinyin;

    //发行日期
    private Integer release_date;

    //职业
    private String occupation;


    // 质检信息部分

    // 质量类型 [0:无，1：复印件，2：拍屏，3：假证件，4：有水印，5：遮挡，6：切边，7：卡变形，8：有光斑]
    private String quality_kind;

    // 质量描述
    private String quality_desc;

    /**
     * 转换后的身份证base64
     */
    private String crop_image;
}
