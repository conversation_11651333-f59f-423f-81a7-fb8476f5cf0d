package com.cairh.cpe.esb.component.idverify.dto.req;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc
 * @date 2022-04-29
 */
@Data
public class VerifyPoliceRequest implements Serializable {

    private static final long serialVersionUID = -1447793018808395222L;
    /**
     * 机构标志  organ_flag
     */
    @NotNull
    private String organ_flag;

    /**
     * 证件类别
     */
    @NotNull
    private String id_kind;

    /**
     * 证件号码
     */
    @NotNull
    private String id_no;

    /**
     * 姓名
     */
    @NotNull
    private String full_name;

    /**
     * 实时标志  默认为0。传入1时实时调用底层公安接口，不取本地数据
     */
    private String realtime_flag;

    /**
     * 营业部编号
     */
    private String branch_no = "0";

    /**
     * 服务厂商定制, 非业务参数
     */
    private String service_vender;

    /**
     * 大头照base64 csdc_busi_kind=03时必传
     * 当厂商为中登公安时，才会涉及csdc_busi_kind和base64_image字段
     */
    private String base64_image;

    /**
     * 统一社会信用代码  organ_flag为1时 必传
     */
    private String usc_code;

    /**
     * 组织机构代码  organ_flag为1时 必传
     */
    private String organ_code;

    /**
     * 02：简项查询，返回照片， 03：人像比对，默认值为02  ，当csdc_busi_kind=03时，此时机构标志只能传0
     */
    private String csdc_busi_kind;
    /**
     * 操作员营业部编号
     */
    private String op_branch_no;

    /**
     * 操作员营业部名称
     */
    private String op_branch_name;

    /**
     * 用户营业部名称
     */
    private String branch_name;

    /**
     * 操作员编号
     */
    private String operator_no;

    /**
     * 操作员姓名
     */
    private String operator_name;
}