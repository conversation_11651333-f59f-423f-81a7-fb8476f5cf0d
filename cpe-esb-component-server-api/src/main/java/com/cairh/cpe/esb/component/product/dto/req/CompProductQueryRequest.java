package com.cairh.cpe.esb.component.product.dto.req;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class CompProductQueryRequest implements Serializable {

    private static final long serialVersionUID = 3560056515297770599L;

    /**
     * 产品代码
     */
    private String prod_code;

    /**
     * 基金产品简称
     */
    private String product_short_name;

    /**
     * 基金产品全称
     */
    private String product_full_name;

    /**
     * 基金产品类别
     */
    private String fund_product_type;

    /**
     * 基金公司代码
     */
    private String company_code;

}
