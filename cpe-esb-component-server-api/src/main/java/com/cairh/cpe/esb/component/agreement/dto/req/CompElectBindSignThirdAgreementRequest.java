package com.cairh.cpe.esb.component.agreement.dto.req;

import lombok.Data;

import java.io.Serializable;

/**
 * 将已签约的三方模板与本地模板关联
 *
 * <AUTHOR>
 * @since 2023/6/27 20:24
 */
@Data
public class CompElectBindSignThirdAgreementRequest implements Serializable {
    private static final long serialVersionUID = -8952187806106361896L;
    /**
     * 文件类型 1-风险揭示书 2-合同 3-补充协议 4-自定义文件
     */
    private String file_type;
    /**
     * 本地协议文件地址
     */
    private String elecagreemodel_id;
    /**
     * 版本
     */
    private String version;
    /**
     * 签约状态 8:已签约 9:未签约
     */
    private String sign_status;
    /**
     * 三方协议签署id
     */
    private String third_agreement_sign_id;
    /**
     * 同步协议文件方式 0:同步 1:异步
     */
    private String async_flag = "0";
    /**
     * 三方协议文件地址
     */
    private String file_url;
    /**
     * 签署厂商 0-本地，11-妥妥递，21-CFCA
     */
    private String sign_vender;
}