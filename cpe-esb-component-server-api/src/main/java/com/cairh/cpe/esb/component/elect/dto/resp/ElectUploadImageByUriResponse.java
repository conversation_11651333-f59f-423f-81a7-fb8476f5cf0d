package com.cairh.cpe.esb.component.elect.dto.resp;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@Accessors(chain = true)
public class ElectUploadImageByUriResponse implements Serializable {
    private static final long serialVersionUID = 1988017293670047730L;
    /**文件记录ID*/
    @NotNull
    private String filerecord_id;
}
