package com.cairh.cpe.component.common.data.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.component.common.data.entity.AuthorizeInfo;
import com.cairh.cpe.component.common.form.AuthorizeinfoForm;
import com.cairh.cpe.component.common.form.request.CreateAuthorizeinfoRequest;
import com.cairh.cpe.component.common.form.request.DeleteAuthorizeinfoRequest;
import com.cairh.cpe.component.common.form.request.UpdateAuthorizeinfoRequest;

public interface IAuthorizeinfoService extends IService<AuthorizeInfo> {
    Page<AuthorizeInfo> queryByPage(AuthorizeinfoForm param);

    void createAuthorizeInfo(BaseUser baseUser, CreateAuthorizeinfoRequest entity);

    void modifyAuthorizeInfo(BaseUser baseUser, UpdateAuthorizeinfoRequest entity);

    void deleteAuthorizeInfo(BaseUser baseUser, DeleteAuthorizeinfoRequest request);

    AuthorizeInfo getByAppIdAndSecretKey(String appId, String secretKey);
}
