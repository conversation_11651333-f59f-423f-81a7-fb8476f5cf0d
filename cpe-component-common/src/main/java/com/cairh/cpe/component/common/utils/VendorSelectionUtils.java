package com.cairh.cpe.component.common.utils;

import cn.hutool.extra.spring.SpringUtil;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.core.autoconfiure.env.CompositePropertySources;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.springframework.util.StringUtils;

import static com.cairh.cpe.component.common.constant.ErrorConstant.SERVICE_SELECT_EXCEPTION;

/**
 * 厂商选择工具类
 */
@Slf4j
public class VendorSelectionUtils {

    private static CompositePropertySources compositePropertySources;

    /**
     * 获取服务厂商的实现类
     * <pre>
     *     1.如果有传入service_vender，并且能通过该Bean名称找到对象的实现类，则直接返回
     *     2.如果有传入property_key，并能在系统参数配置表中找到对应的配置项，而且存在对应的Bean，则直接返回
     *     3.通过上面两种方式均找不到实现类时，直接返回defaultServiceName参数对应的服务商实现类
     * </pre>
     *
     * @param service_vender     厂商实现类的Bean名称
     * @param property_key       厂商实现类的系统参数配置KEY
     * @param vendorClass        服务实现类Class
     * @param defaultServiceName 通过上述两种方案均找不到实现类时返回默认的服务商
     * @return 厂商实现类
     */
    public static <T> ServiceWrap<T> getServiceOrDefault(String service_vender, String property_key, Class<T> vendorClass, @NonNull String defaultServiceName) {
        return getService(service_vender, property_key, vendorClass, () -> {
            log.info("使用默认的服务厂商：{}", defaultServiceName);
            return ServiceWrap.wrap(getBean(defaultServiceName, vendorClass), defaultServiceName);
        });
    }


    /**
     * 获取服务厂商的实现类
     * <pre>
     *     1.如果有传入service_vender，并且能通过该Bean名称找到对象的实现类，则直接返回
     *     2.如果有传入property_key，并能在系统参数配置表中找到对应的配置项，而且存在对应的Bean，则直接返回
     *     3.通过上面两种方式均找不到实现类时，直接抛异常
     * </pre>
     *
     * @param service_vender 厂商实现类的Bean名称
     * @param property_key   厂商实现类的系统参数配置KEY
     * @param vendorClass    服务实现类Class
     * @param notFoundErrMsg 找不到时的异常提示信息
     * @return 厂商实现类
     */
    public static <T> ServiceWrap<T> getService(String service_vender, String property_key, Class<T> vendorClass, String notFoundErrMsg) {
        return getService(service_vender, property_key, vendorClass, () -> {
            throw new BizException(SERVICE_SELECT_EXCEPTION, notFoundErrMsg);
        });
    }


    /**
     * 获取服务厂商的实现类
     * <pre>
     *     1.如果有传入service_vender，并且能通过该Bean名称找到对象的实现类，则直接返回
     *     2.如果有传入property_key，并能在系统参数配置表中找到对应的配置项，而且存在对应的Bean，则直接返回
     *     3.通过上面两种方式均找不到实现类时，使用{@link NotFindCallback}进行实现类查找
     * </pre>
     *
     * @param service_vender  厂商实现类的Bean名称
     * @param property_key    厂商实现类的系统参数配置KEY
     * @param vendorClass     服务实现类Class
     * @param notFindCallback 找不到时的回调
     * @return 厂商实现类
     */
    public static <T> ServiceWrap<T> getService(String service_vender, String property_key, Class<T> vendorClass, NotFindCallback<T> notFindCallback) {
        if (StringUtils.hasText(service_vender)) {
            log.info("业务传入的服务厂商 : {}", service_vender);
            return ServiceWrap.wrap(getBean(service_vender, vendorClass), service_vender);
        }
        if (StringUtils.hasText(property_key)) {
            CompositePropertySources compositePropertySources = getCompositePropertySources();
            String propertyValue = compositePropertySources.getProperty(property_key);
            if (StringUtils.hasText(propertyValue)) {
                log.info("系统参数配置的服务厂商 :  PROPERTY_KEY={}, PROPERTY_VALUE={}", property_key, propertyValue);
                return ServiceWrap.wrap(getBean(propertyValue, vendorClass), propertyValue);
            }
        }
        return notFindCallback.getServiceWrap();
    }

    private static <T> T getBean(String beanName, Class<T> beanType) {
        try {
            return SpringUtil.getBean(beanName, beanType);
        } catch (Exception e) {
            throw new BizException(SERVICE_SELECT_EXCEPTION, "获取服务厂商实现类'" + beanName + "'失败", e);
        }
    }

    public static CompositePropertySources getCompositePropertySources() {
        if (compositePropertySources == null) {
            compositePropertySources = SpringUtil.getBean(CompositePropertySources.class);
        }
        return compositePropertySources;
    }


}

