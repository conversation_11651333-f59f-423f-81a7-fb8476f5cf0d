package com.cairh.cpe.component.common.model;

import com.cairh.cpe.component.common.utils.StringUtils;
import lombok.Data;

/**
 * 自动分配坐席对象
 *
 * <AUTHOR>
 * @since SP1-PACK1
 */
@Data
public class VideoOperator implements Comparable<VideoOperator> {

    public static final String ASSIGNED_USER_ID = "assigned_user_id";

    public static final String ASSIGNMENT_TIME = "assignmentTime";

    public static final String RESPONSE_TIME = "responseTime";

    public static final String RESPONSE_TYPE = "responseType";

    public static final String STATUS = "status";

    public static final String STATUS_CONVERT = "statusConvert";

    public static final String STATUS_TIME = "statusTime";

    public static final String STATUS_LAST_TIME = "statusLastTime";

    public static final String TIMEOUT_COUNT = "timeoutCount";

    public static final String LAST_START_REST_TIME = "lastStartRestTime";

    public static final String OPERATION_TIME = "operationTime";

    public static final String OPERATIONTYPE = "operationType";
    public static final String OPERATIONMENU = "operationMenu";

    public static final String OPERATIONTYPE_CONVERT = "operationTypeConvert";

    public static final String USER_NAME = "user_name";

    public static final String BRANCH_NO = "branch_no";

    public static final String BRANCH_NAME = "branch_name";

    public static final String EN_BRANCH_NOS = "enBranchNos";

    public static final String WAIT_QUEUE_INDEX = "waitQueueIndex";

    public static final String AUTO_RECEIVE_DELAY_SWITCH = "isAutoReceiveDelaySwitch";

    private String waitCurrStatusTime;
    /** 分配的用户id （用户编号+业务流水号） */
    private String assigned_user_id;

    /** 分配的时间 */
    private String assignmentTime;

    /** 所属营业部名称 */
    private String branch_name;

    /** 所属营业部 */
    private String branch_no;

    /** 最后一次接受视频任务时间 */
    private String lastAcceptVideoTime;

    /** 自动分配响应时间 */
    private String responseTime;

    /** 自动分配响应类型 */
    private String responseType;

    /** 员工编号 */
    private String staff_no;

    /** 状态 */
    private String status;

    /** 状态转义 */
    private String statusConvert;

    /** 状态变更时间 */
    private long statusTime;

    /** 状态持续时间 */
    private long statusLastTime;

    /** 操作员名称 */
    private String user_name;

    /** 超时忽略次数 */
    private int timeoutCount = 0;

    /** 开始休息的时间（休息完毕后才分配，即使已经在队列中） */
    private long lastStartRestTime = 0;

    /** 操作时间 */
    private String operationTime;

    private String operationMenu;

    /** 操作类型 */
    private String operationType;

    /** 操作类型转义 */
    private String operationTypeConvert;

    /** 允许的营业部集合 */
    private String enBranchNos;

    /** 等待队列中的位置 */
    private String waitQueueIndex;

    /** 自动领取自动分配任务 是否有延迟 */
    private String isAutoReceiveDelaySwitch;

    public void updateStatus(StatusDic statusDic) {
        this.status = statusDic.status;
        this.statusConvert = statusDic.statusConvert;
    }

    @Override
    public int compareTo(VideoOperator o) {
        return 0;
    }

    /**
     * 操作员状态字典枚举类
     *
     * <AUTHOR>
     * @since @since SP1-PACK1
     */
    public enum StatusDic {

        FREE("0", "空闲"), // 上线了，但未加入队列

        JOINED("1", "队列中"),

        ASSIGNED("2", "已分配"),

        ACCEPTED("3", "已接受"),

        VIDEOING("4", "视频中"),

        OFFLINE("5", "离线");

        public final String status;

        public final String statusConvert;

        StatusDic(String status, String statusConvert) {
            this.status = status;
            this.statusConvert = statusConvert;
        }

    }

    /**
     * 操作员自动分配操作枚举类
     *
     * <AUTHOR>
     * @since SP1-PACK3-PATCH1、SP1-PACK4
     */
    public enum Operation {

        QUEUE_ADD("add", "加入队列"),

        QUEUE_REMOVE("remove", "离开队列"),

        PRE_REMOVE_FOR_ACTIVE_REPLY("pre_remove_for_active_reply", "在主动接听用户前离开");

        public final String value;

        public final String translation;

        Operation(String value, String translation) {
            this.value = value;
            this.translation = translation;
        }

        /**
         * 依据value获取枚举
         *
         * @param value
         * @return Operation
         * <AUTHOR>
         * @since 2023/7/19 17:34
         */
        public static Operation getByValue(String value) {
            if (StringUtils.isBlank(value)) {
                return null;
            }
            for (Operation operation : Operation.values()) {
                if (StringUtils.equals(operation.value, value)) {
                    return operation;
                }
            }
            return null;
        }

    }

}
