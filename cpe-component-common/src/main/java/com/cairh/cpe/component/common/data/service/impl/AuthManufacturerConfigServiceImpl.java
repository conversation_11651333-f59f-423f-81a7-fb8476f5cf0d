package com.cairh.cpe.component.common.data.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.component.common.data.entity.AuthManufacturerConfig;
import com.cairh.cpe.component.common.data.mapper.AuthManufacturerConfigMapper;
import com.cairh.cpe.component.common.data.service.IAuthManufacturerConfigService;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 高清人脸比对配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class AuthManufacturerConfigServiceImpl extends ServiceImpl<AuthManufacturerConfigMapper, AuthManufacturerConfig> implements IAuthManufacturerConfigService {
}
