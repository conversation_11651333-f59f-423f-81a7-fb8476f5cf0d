package com.cairh.cpe.component.common.data.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cairh.cpe.component.common.data.entity.VideoRecord;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 视频流水表 Mapper
 *
 * <AUTHOR>
 */
public interface VideoRecordMapper extends BaseMapper<VideoRecord> {
    @Select("select SERIAL_ID from VIDEORECORD where BUSIN_FLAG = 13  and ROOM_ID in (select ROOM_ID from VIDEORECORD  where BUSIN_FLAG IN ( 8, 9 )) and ROOM_ID != ' '")
    List<String>  getVideoRecordByBusinFlag();
}
