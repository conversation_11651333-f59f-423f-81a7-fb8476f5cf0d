package com.cairh.cpe.component.common.service.impl;

import com.cairh.cpe.component.common.service.IRedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.RedisServerCommands;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Service
public class RedisServiceImpl implements IRedisService {

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @Override
    public void addToRedisSetById(String type, Double score, String id) {
        redisTemplate.opsForZSet().add(type, id, score);
    }

    @Override
    public long getMillisecond() {
        try {
            Long execute = redisTemplate.execute((RedisCallback<Long>) RedisServerCommands::time);
            if (Objects.isNull(execute)) {
                return System.currentTimeMillis();
            }
            return execute;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return System.currentTimeMillis();
        }
    }

    @Override
    public Double getScoreById(String type, String id) {
        return redisTemplate.opsForZSet().score(type, id);
    }

    @Override
    public Set<String> getSortedSet(String type) {
        return redisTemplate.opsForZSet().range(type, 0L, -1L);
    }


    @Override
    public boolean removeFromRedisSetById(String type, String id) {
        Long result = redisTemplate.opsForZSet().remove(type, id);
        if (result == 1) {
            return true;
        }
        return false;
    }

    @Override
    public Long zrank(String setName, String key) {
        return redisTemplate.opsForZSet().rank(setName, key);
    }

    @Override
    public Boolean publish(String channel, String message) {
        redisTemplate.convertAndSend(channel, message);
        return true;
    }

    @Override
    public Boolean zrem(String key, String member) {
        redisTemplate.opsForZSet().remove(key, member);
        return true;
    }
    @Override
    public Boolean srem(String key, String member) {
        redisTemplate.opsForSet().remove(key, member);
        return true;
    }
    @Override
    public int getSetCount(String type) {
        return Optional.ofNullable(redisTemplate.opsForZSet().zCard(type)).orElse(0L).intValue();
    }
}
