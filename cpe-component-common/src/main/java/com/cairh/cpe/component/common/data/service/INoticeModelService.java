package com.cairh.cpe.component.common.data.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.component.common.data.entity.NoticeModel;

/**
 * 消息模板表 服务类
 *
 * <AUTHOR>
 * @since 2022-05-07
 */
public interface INoticeModelService extends IService<NoticeModel> {

    /**
     * 保存消息模板
     * @param baseUser
     * @param entity
     */
    void baseSave(BaseUser baseUser, NoticeModel entity);
}
