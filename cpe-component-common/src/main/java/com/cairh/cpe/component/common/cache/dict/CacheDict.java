package com.cairh.cpe.component.common.cache.dict;

import com.cairh.cpe.component.common.cache.ICache;
import com.cairh.cpe.esb.base.rpc.dto.resp.support.Basedictionary;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年01月10日 17:44
 */
@Slf4j
@Component
public class CacheDict implements ICache {

	@Autowired
	ICacheDictionaryService cacheDictionaryService;

	public Basedictionary baseDataQryDict(String dictCode, String subCode) {
		Basedictionary basedictionary = null;
		List<Basedictionary> list = cacheDictionaryService.getBasedictionaryList(dictCode);
		for (Iterator<Basedictionary> iterator = list.iterator(); iterator.hasNext(); ) {
			Basedictionary dictionary = iterator.next();
			if(StringUtils.equals(dictionary.getSub_code(), subCode)){
				basedictionary = dictionary;
			}
		}
		return basedictionary ;
	}

	public List<Basedictionary> baseDataQryDictList(String dictCode) {
		List<Basedictionary> list = cacheDictionaryService.getBasedictionaryList(dictCode);
		if(CollectionUtils.isNotEmpty(list)){
			return list ;
		}
		else{
			return new ArrayList<Basedictionary>();
		}
	}

	@Override
	public void refresh() {
		cacheDictionaryService.refresh();
	}
}
