package com.cairh.cpe.component.common.data.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cairh.cpe.component.common.data.entity.AgreeModelBusiness;
import com.cairh.cpe.component.common.data.entity.ElecAgreeModel;
import com.cairh.cpe.component.common.form.AgreemodelbusinessForm;
import com.cairh.cpe.component.common.form.AgreemodelbusinessVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.Set;

/**
 * <p>
 * 协议业务参数 Mapper 接口
 * </p>
 */
public interface

AgreeModelBusinessMapper extends BaseMapper<AgreeModelBusiness> {

    /**
     * 关联、分页查询
     *
     * @param param
     * @return
     */
    Page<AgreemodelbusinessVo> diySelectPage(AgreemodelbusinessForm param);

    /**
     * 当协议模版被废弃时，将所有与之关联的协议规则中该协议模版的状态改为不启用
     *
     * @param elecagreemodelIdSet 被废弃的协议模版ID集合
     */
    void offAbandonElecagreemodel(@Param("elecagreemodelIdSet") Set<String> elecagreemodelIdSet);

    /**
     * 将协议规则中关联的一批协议模板同意更换为另一个版本，并且跟新状态
     * @param oldVersionIds 旧版本的协议模板ID集合
     * @param newModel  新版本的协议模板
     */
    void relevanceAgreeModel(@Param("oldVersionIds") Set<String> oldVersionIds,
                             @Param("newModel") ElecAgreeModel newModel);

    /**
     * 将协议规则的状态与对应的协议模板的状态相关联
     *
     * @param model 协议模板实例对象
     */
    @Update("UPDATE agreemodelbusiness SET status = #{model.agreement_status} WHERE elecagreemodel_id = #{model.serial_id}")
    void elecagreemodelStatusRelevance(@Param("model") ElecAgreeModel model);
}
