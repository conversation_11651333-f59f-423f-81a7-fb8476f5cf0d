package com.cairh.cpe.component.common.form;

import com.cairh.cpe.component.common.data.entity.VideoWordsModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 * @since 2022-07-11
 */
@EqualsAndHashCode(callSuper = false)
@Data
@ToString(callSuper = true)
public class VideowordsmodelForm extends BasePage<VideoWordsModel> {

    /**
     * id
     */
    private String serial_id;

    /**
     * 模板名称
     */
    private String model_name;

    /**
     * 模板类型
     */
    private String model_type;

    /**
     * 视频方式
     */
    private String video_type;

    /**
     * 状态
     */
    private String status;
    /**
     * 业务类型
     */
    private String busin_type;
}