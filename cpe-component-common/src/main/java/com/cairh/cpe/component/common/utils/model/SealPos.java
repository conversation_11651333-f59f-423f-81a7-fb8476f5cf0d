package com.cairh.cpe.component.common.utils.model;

/**
 * 签章位置信息
 */
public class SealPos {
    //签名类型 0:根据关键字签名 1：根据位置签名
    private String signtype;
    //签名关键字
    private String kw ;
    //关键字从上至下查找顺序号
    private String index ;
    //宽度
    private String width;
    //高度
    private String height;
    //x轴偏移量
    private String xOffset;
    //y轴偏移量
    private String yOffset;
    //签章代码
    private String sealcode;
    //签章密码
    private String sealpwd;

    public String getSigntype() {
        return signtype;
    }

    public void setSigntype(String signtype) {
        this.signtype = signtype;
    }

    public String getKw() {
        return kw;
    }

    public void setKw(String kw) {
        this.kw = kw;
    }

    public String getIndex() {
        return index;
    }

    public void setIndex(String index) {
        this.index = index;
    }

    public String getWidth() {
        return width;
    }

    public void setWidth(String width) {
        this.width = width;
    }

    public String getHeight() {
        return height;
    }

    public void setHeight(String height) {
        this.height = height;
    }

    public String getxOffset() {
        return xOffset;
    }

    public void setxOffset(String xOffset) {
        this.xOffset = xOffset;
    }

    public String getyOffset() {
        return yOffset;
    }

    public void setyOffset(String yOffset) {
        this.yOffset = yOffset;
    }

    public String getSealcode() {
        return sealcode;
    }

    public void setSealcode(String sealcode) {
        this.sealcode = sealcode;
    }

    public String getSealpwd() {
        return sealpwd;
    }

    public void setSealpwd(String sealpwd) {
        this.sealpwd = sealpwd;
    }
}
