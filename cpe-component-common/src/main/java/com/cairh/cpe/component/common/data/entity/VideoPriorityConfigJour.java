package com.cairh.cpe.component.common.data.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@TableName("VIDEOPRIORITYCONFIGJOUR")
@EqualsAndHashCode(callSuper=false)
public class VideoPriorityConfigJour extends Model<VideoPriorityConfigJour> {
    private static final long serialVersionUID = -5228851034997964256L;

    /**
     * 主键
     */
    @TableId("serial_id")
    private String serial_id;
    /**
     * 记录数据主键id
     */
    @TableField("video_priority_config_id")
    private String video_priority_config_id;

    /**
     * 优先级字段名称
     */
    @TableField("field_name")
    private String field_name;

    /**
     * 字段的优先级
     */
    @TableField("field_priority")
    private Integer field_priority;

    /**
     * 优先级字段值
     */
    @TableField("field_value")
    private String field_value;

    /**
     * 字段值的优先级
     */
    @TableField("value_priority")
    private Integer value_priority;
    /**
     * 创建时间
     */
    @TableField(value = "create_datetime")
    private Date create_datetime;
    /**
     * 修改时间
     */
    @TableField(value = "modify_datetime")
    private Date modify_datetime;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 创建人
     */
    @TableField(value = "create_by")
    private String create_by;

    /**
     * 是否启用
     */
    @TableField(value = "enable_flag")
    private String enable_flag = "1";

    /**
     * 操作标志
     */
    @TableField(value = "business_flag")
    private Integer business_flag;
}
