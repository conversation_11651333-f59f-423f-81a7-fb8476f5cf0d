package com.cairh.cpe.component.common.form;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cairh.cpe.component.common.model.VideoUserEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 视频用户表单
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class VideoUserForm extends Page<VideoUserEntity> {

    //名称
    private String user_name;

    //证件号码
    private String id_no;

    //手机号
    private String mobile_tel;

    //开户营业部
    private String branch_no;

    //接入来源
    private String app_id;

    //业务类型
    private String busin_type;

    //子系统
    private String subsys_no;

    //经纪人编号
    private String staff_no;

    /**
     * 渠道号
     */
    private String channel_code;
}
