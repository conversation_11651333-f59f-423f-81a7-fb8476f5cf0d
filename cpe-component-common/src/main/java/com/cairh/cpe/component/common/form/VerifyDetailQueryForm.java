package com.cairh.cpe.component.common.form;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 公安认证详情查询
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ToString(callSuper = true)
public class VerifyDetailQueryForm extends BasePage<VerifyDetailQueryForm> {
    {
        OrderItem orderItem = new OrderItem();
        orderItem.setAsc(false);
        orderItem.setColumn("af.create_datetime");

        OrderItem orderItem2 = new OrderItem();
        orderItem2.setAsc(true);
        orderItem2.setColumn("branch_no");

        ArrayList<OrderItem> orderItems = new ArrayList<>();
        orderItems.add(orderItem);
        orderItems.add(orderItem2);
        super.setOrders(orderItems);
    }

    /**
     * 用户所在营业部
     */
//    private List<String> branch_no_list;

    private String branch_no;
    /**
     * 操作员所在营业部
     */
//    private List<String> op_branch_no_list;

    private String op_branch_no;

    /**
     * 认证厂商
     */
    private String factory_name;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date begin_time;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date end_time;

    private String id_no;

    private String full_name;

    /**
     * 认证类型
     */
    private String verify_type;

    /**
     * 是否计费，选计费传0,1， 不计费随便传个值
     * 只要进表的都需要计费，接口调用不成功会抛出异常
     */
    private List<String> count_money;

    /**
     * 是否通过公安认证 1：通过，0：未通过
     */
    private String status;


}