package com.cairh.cpe.component.common.enums;

import lombok.Getter;

/**
 * 视频业务标识枚举类
 *
 * <AUTHOR>
 * @since 2023/8/29 10:16
 */
@Getter
public enum VideoBusinessFlagEnum {

    VIDEO_USER_JOIN_QUEUE("0", "视频用户加入排队队列"),
    VIDEO_OPERATOR_ANSWER_VIDEO("1", "视频坐席接听视频"),
    VIDEOING("4", "用户视频中"),
    VIDEOED("5", "用户视频完成"),
    VIDEO_USER_ENTER_ROOM_SUCCESS("6", "视频用户进入房间成功"),
    VIDEO_OPERATOR_ENTER_ROOM_SUCCESS("7", "视频坐席进入房间成功"),
    VIDEO_USER_EXIT_QUEUE("8", "视频用户退出排队排队"),
    VIDEO_USER_INTERRUPT_VIDEO("9", "视频中时视频用户中断视频"),
    VIDEO_OPERATOR_INTERRUPT_VIDEO("9", "视频中时视频坐席中断视频");

    private final String videoBusinessFlag;
    private final String videoBusinessFlagDesc;

    VideoBusinessFlagEnum(String videoBusinessFlag, String videoBusinessFlagDesc) {
        this.videoBusinessFlag = videoBusinessFlag;
        this.videoBusinessFlagDesc = videoBusinessFlagDesc;
    }

}
