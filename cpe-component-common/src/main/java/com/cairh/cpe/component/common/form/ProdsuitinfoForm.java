package com.cairh.cpe.component.common.form;

import com.cairh.cpe.component.common.data.entity.ProdSuitInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 产品适当性分页查询入参
 * <AUTHOR>
 * @since 2022-05-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ToString(callSuper = true)
public class ProdsuitinfoForm extends BasePage<ProdSuitInfo> {

    private String serial_id;

    /**
     * 产品代码
     */
    private String prod_code;

    /**
     * 产品TA编号
     */
    private String prodta_no;

    /**
     * 产品名称
     */
    private String prod_name;

    /**
     * 产品风险等级
     */
    private String risk_level;

    /**
     * 产品投资品种
     */
    private String invest_kind;

    /**
     * 产品投资期限
     */
    private String invest_term;

    /**
     * 产品最大亏损率
     */
    private BigDecimal max_deficit_rate;

    /**
     * 产品收益类型
     */
    private String income_type;

    /**
     * 产品预期收益
     */
    private BigDecimal prodpre_income;
}