package com.cairh.cpe.component.common.data.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.component.common.constant.Constant;
import com.cairh.cpe.component.common.data.entity.*;
import com.cairh.cpe.component.common.data.mapper.ExampaperMapper;
import com.cairh.cpe.component.common.data.service.*;
import com.cairh.cpe.component.common.utils.BaseBeanUtil;
import com.cairh.cpe.component.common.utils.JourUtil;
import com.cairh.cpe.component.common.utils.SeqUtil;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.context.constant.ErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 问卷 服务实现类
 * 1、同步时始终作废当前版本问卷，新增一套问卷状态同柜台
 * * 2、新增、修改问卷时新增或修改的状态为正常，则作废同类型的正常问卷，新增一套问卷
 * * 3、状态控制
 * * 3.1：不正常状态不允许修改
 * * 3.2：初始状态和正常状态允许修改
 * * 4：如果修改的时候有答题记录   需新增版本   前
 * *      端有传的情况下用前端的状态   没有传的情况下查看当前数据版本，
 * *      新增的状态与当前保持一致   并把当前版本改为非正常
 * *      如果当前版本是未启用状态，应该直接修改
 * </p>
 */
@Service
@Slf4j
public class ExampaperServiceImpl extends ServiceImpl<ExampaperMapper, ExamPaper> implements IExamPaperService {

    @Autowired
    private IExamTestResultService examtestresultService;

    @Autowired
    private IExamQuestionOptionsService examquestionoptionsService;

    @Autowired
    private IExamQuestionService examquestionService;

    @Autowired
    private IExamQuestionJourService examquestionjourService;

    @Autowired
    private IExamPaperJourService exampaperjourService;

    @Autowired
    private IExamResultCommentService examResultCommentService;

    @Autowired
    private IExamQuestionOptionsJourService examquestionoptionsjourService;

    @Autowired
    private IExamResultCommentJourService examresultcommentjourService;

    @Override
    public void baseSave(BaseUser baseUser, ExamPaper entity) {
        if (StringUtils.isNotBlank(entity.getSerial_id())) {
            ExamPaper exampaper = this.getById(entity.getSerial_id());
            if (Objects.nonNull(exampaper)) {
                throw new BizException(ErrorCode.ERR_PARAM_IN_ERROR, "该问卷编号已存在");
            }
        }
        List<ExamPaper> allPapersByType = lambdaQuery().eq(ExamPaper::getPaper_type, entity.getPaper_type())
                .select(ExamPaper::getSerial_id, ExamPaper::getVersion_no, ExamPaper::getStatus)
                .eq(ExamPaper::getOrgan_flag, entity.getOrgan_flag())
                .eq(ExamPaper::getPaper_name,entity.getPaper_name())
                .eq(StringUtils.isNotBlank(entity.getSub_paper_type()), ExamPaper::getSub_paper_type, entity.getSub_paper_type())
                .list();
        OptionalDouble max = allPapersByType.stream().mapToDouble(x -> Double.valueOf(StringUtils.defaultIfBlank(x.getVersion_no(), "0"))).max();
        entity.setVersion_no(String.valueOf(max.orElse(0) + 1));
        if (StringUtils.isBlank(entity.getStatus())) {
            entity.setStatus(Constant.COMMON_INIT_STATUS);
        }
        entity.setCreate_by(baseUser.getStaff_no());
        entity.setModify_by(baseUser.getStaff_no());
        this.save(entity);
        //保存流水
        JourUtil.writeJour(() -> saveJour(entity, false, Constant.BUSINESS_FLAG_ADD));
    }

    private List<ExamPaper> getValidPaper(ExamPaper entity) {
        List<ExamPaper> updateToDeleteList = lambdaQuery()
                .select(ExamPaper::getSerial_id)
                .eq(ExamPaper::getPaper_type, entity.getPaper_type())
                .eq(ExamPaper::getOrgan_flag, entity.getOrgan_flag())
                .eq(ExamPaper::getStatus, Constant.COMMON_VALID_STATUS)
                .eq(StringUtils.isNotBlank(entity.getSub_paper_type()), ExamPaper::getSub_paper_type, entity.getSub_paper_type())
                .list();
        return updateToDeleteList;
    }

    private List<ExamPaper> getAllPaperByType(ExamPaper entity) {
        List<ExamPaper> updateToDeleteList = lambdaQuery()
                .select(ExamPaper::getSerial_id)
                .eq(ExamPaper::getPaper_type, entity.getPaper_type())
                .eq(ExamPaper::getOrgan_flag, entity.getOrgan_flag())
                .eq(StringUtils.isNotBlank(entity.getSub_paper_type()), ExamPaper::getSub_paper_type, entity.getSub_paper_type())
                .list();
        return updateToDeleteList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void baseDelete(BaseUser baseUser, ExamPaper entity) {
        //校验是否可以删除
        Integer count = Math.toIntExact(examtestresultService.lambdaQuery().eq(ExamTestResult::getExampaper_id, entity.getSerial_id()).count());
        if (count > 0) {
            throw new BizException(ErrorCode.ERR_SYSWARNING, "该问卷已有作答记录不可以删除");
        }
        entity.setModify_by(baseUser.getStaff_no());
        entity.setStatus(Constant.COMMON_DELETE_STATUS);
        this.updateById(entity);
        List<ExamQuestion> examquestionList = examquestionService.lambdaQuery()
                .eq(ExamQuestion::getExampaper_id, entity.getSerial_id())
                .select(ExamQuestion::getSerial_id).list();
        if (CollectionUtils.isNotEmpty(examquestionList)) {
            examquestionList.forEach(x -> examquestionService.baseDelete(baseUser, x));
        }
        List<ExamQuestionOptions> examquestionoptionsList = examquestionoptionsService.deleteByPaper(entity.getSerial_id());
        //保存流水
        JourUtil.writeJour(() -> saveQuestionJourBatch(examquestionList, Constant.BUSINESS_FLAG_MOD));
        JourUtil.writeJour(() -> examquestionoptionsService.saveJour(examquestionoptionsList, true, Constant.BUSINESS_FLAG_MOD));
        JourUtil.writeJour(() -> saveJour(entity, true, Constant.BUSINESS_FLAG_ADD));
    }

    private boolean saveQuestionJourBatch(List<ExamQuestion> examquestionList, Integer business_flag) {
        if (CollectionUtils.isEmpty(examquestionList)) {
            return false;
        }
        List<String> collect = examquestionList.stream().map(x -> x.getSerial_id()).collect(Collectors.toList());
        List<ExamQuestion> list = examquestionService.listByIds(collect);
        List<ExamQuestionJour> examquestionjour = BaseBeanUtil.copyToList(list, ExamQuestionJour.class);
        examquestionjour.forEach(x -> {
            x.setExamquestion_id(x.getSerial_id());
            x.setSerial_id(null);
            x.setBusiness_flag(business_flag);
        });
        return examquestionjourService.saveBatch(examquestionjour);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void baseUpdate(BaseUser baseUser, ExamPaper entity) {
        entity.setModify_by(baseUser.getStaff_no());
        this.updateById(entity);
        //保存流水
        JourUtil.writeJour(() -> saveJour(entity, true, Constant.BUSINESS_FLAG_MOD));

    }

    @Override
    public void copyCurrVersion(BaseUser baseUser, ExamPaper entity) {
        //1. 获取问卷与问卷测评规则
        ExamPaper exampaper = getById(entity.getSerial_id());
        List<ExamResultComment> examResultCommentList = examResultCommentService.lambdaQuery().eq(ExamResultComment::getExampaper_id, entity.getSerial_id()).list();
        //2. 获取试题
        List<ExamQuestion> examquestionList = examquestionService.lambdaQuery().eq(ExamQuestion::getExampaper_id, entity.getSerial_id()).list();
        //3. 获取试题
        List<ExamQuestionOptions> examquestionoptionsList = examquestionoptionsService.lambdaQuery().eq(ExamQuestionOptions::getExampaper_id, entity.getSerial_id()).list();
        //4. 处理试题为新版本
        //4.1 获取并设置版本
        List<ExamPaper> allPapersByType = lambdaQuery().eq(ExamPaper::getPaper_type, exampaper.getPaper_type())
                .select(ExamPaper::getSerial_id, ExamPaper::getVersion_no, ExamPaper::getStatus)
                .eq(ExamPaper::getOrgan_flag, exampaper.getOrgan_flag())
                .eq(ExamPaper::getPaper_name,exampaper.getPaper_name())
                .eq(StringUtils.isNotBlank(exampaper.getSub_paper_type()), ExamPaper::getSub_paper_type, exampaper.getSub_paper_type())
                .list();
        OptionalDouble max = allPapersByType.stream()
                .map(x -> new BigDecimal(StringUtils.defaultIfBlank(x.getVersion_no(), "0")))
                .mapToDouble(BigDecimal::doubleValue)
                .max();
        // 将 double 转换为字符串
        String numberStr = String.valueOf(max.getAsDouble());
        log.info("试卷版本号:{}", numberStr);
        // 查找小数点的位置
        int decimalIndex = numberStr.indexOf('.');
        // 计算小数位数
        int decimalPlaces = numberStr.length() - decimalIndex - 1;
        log.info("字符串长度：{}，小数点位置：{}，试卷版本号小数位数:{}", numberStr.length(),decimalIndex,decimalPlaces);
        BigDecimal maxBigDecimal = BigDecimal.valueOf(max.orElse(0)).add(BigDecimal.ONE);
        // 四舍五入保留小数
        BigDecimal roundedValue = maxBigDecimal.setScale(decimalPlaces, RoundingMode.HALF_UP);
        log.info("四舍五入后的试卷版本号:{}", roundedValue);
        exampaper.setVersion_no(String.valueOf(roundedValue));
        //4.2 产生新id
        String newpaper_id = SeqUtil.getSeqCode();
        exampaper.setSerial_id(newpaper_id);
        //4.3 设置状态为初始化
        exampaper.setStatus(Constant.COMMON_INIT_STATUS);
        //4.4 设置创建人
        exampaper.setCreate_by(baseUser.getStaff_no());
        exampaper.setModify_by(baseUser.getStaff_no());

        //5. 处理试题
        //5.1 新旧试题id映射
        HashMap<String, String> old_new_question_id_map = new HashMap<>();
        examquestionList.forEach(x -> {
            String new_question_id = SeqUtil.getSeqCode();
            old_new_question_id_map.put(x.getSerial_id(), new_question_id);
            x.setSerial_id(new_question_id);
            x.setCreate_by(baseUser.getStaff_no());
            x.setModify_by(baseUser.getStaff_no());
            x.setExampaper_id(newpaper_id);
        });
        //6. 处理选项
        HashMap<String, String> old_new_options_id_map = new HashMap<>();
        examquestionoptionsList.forEach(x -> {
            String new_options_id = SeqUtil.getSeqCode();
            old_new_options_id_map.put(x.getSerial_id(), new_options_id);
            x.setSerial_id(new_options_id);
            x.setCreate_by(baseUser.getStaff_no());
            x.setModify_by(baseUser.getStaff_no());
            x.setExampaper_id(newpaper_id);
            x.setExamquestion_id(old_new_question_id_map.get(x.getExamquestion_id()));
        });
        // 德邦证券测试时，发现以下逻辑有问题，暂时屏蔽该代码
        //7. 处理试题的标准答案、默认答案
        examquestionList.forEach(x -> {
            if (StringUtils.isNotBlank(x.getQuestion_answer())) {
                x.setQuestion_answer(replace_old_options(old_new_options_id_map, x.getQuestion_answer()));
            }
            if (StringUtils.isNotBlank(x.getDefault_option_ids())) {
                x.setDefault_option_ids(replace_old_options(old_new_options_id_map, x.getDefault_option_ids()));
            }
        });
        //8. 处理评测规则
        examResultCommentList.forEach(x -> {
            x.setSerial_id(SeqUtil.getSeqCode());
            x.setExampaper_id(newpaper_id);
            x.setCreate_by(baseUser.getStaff_no());
            x.setModify_by(baseUser.getStaff_no());
        });
        //9. 创建流水对象
        ExamPaperJour exampaperjour = BaseBeanUtil.copyProperties(exampaper, ExamPaperJour.class);
        exampaperjour.setExampaper_id(exampaper.getSerial_id());
        exampaperjour.setSerial_id(null);
        exampaperjour.setBusiness_flag(Constant.BUSINESS_FLAG_ADD);
        List<ExamQuestionJour> examquestionjourList = new ArrayList<>();
        examquestionList.forEach(examquestion -> {
            ExamQuestionJour examquestionjour = BaseBeanUtil.copyProperties(examquestion, ExamQuestionJour.class);
            examquestionjour.setExamquestion_id(examquestion.getSerial_id());
            examquestionjour.setSerial_id(null);
            examquestionjour.setBusiness_flag(Constant.BUSINESS_FLAG_ADD);
            examquestionjourList.add(examquestionjour);
        });
        List<ExamQuestionOptionsJour> examquestionoptionsjourList = new ArrayList<>();
        examquestionoptionsList.forEach(examquestionoptions -> {
            ExamQuestionOptionsJour examquestionoptionsjour = BaseBeanUtil.copyProperties(examquestionoptions, ExamQuestionOptionsJour.class);
            examquestionoptionsjour.setSerial_id(null);
            examquestionoptionsjour.setExamquestionoptions_id(examquestionoptions.getSerial_id());
            examquestionoptionsjour.setBusiness_flag(Constant.BUSINESS_FLAG_ADD);
            examquestionoptionsjourList.add(examquestionoptionsjour);
        });
        List<ExamResultCommentJour> examresultcommentjourList = new ArrayList<>();
        examResultCommentList.forEach(examResultComment -> {
            ExamResultCommentJour examresultcommentjour = BaseBeanUtil.copyProperties(examResultCommentList, ExamResultCommentJour.class);
            examresultcommentjour.setSerial_id(null);
            examresultcommentjour.setExamresultcomment_id(examResultComment.getSerial_id());
            examresultcommentjour.setBusiness_flag(Constant.BUSINESS_FLAG_ADD);
            examresultcommentjourList.add(examresultcommentjour);
        });
        //10. 全部入库
        ExampaperServiceImpl service = (ExampaperServiceImpl) AopContext.currentProxy();
        service.doCopyCurrVersion(exampaper, examquestionList, examquestionoptionsList, examResultCommentList
                , exampaperjour, examquestionjourList, examquestionoptionsjourList, examresultcommentjourList);
    }


    @Transactional(rollbackFor = Exception.class)
    public void doCopyCurrVersion(ExamPaper exampaper, List<ExamQuestion> examquestionList, List<ExamQuestionOptions> examquestionoptionsList,
                                  List<ExamResultComment> examResultCommentList, ExamPaperJour exampaperjour, List<ExamQuestionJour> examquestionjourList
            , List<ExamQuestionOptionsJour> examquestionoptionsjourList, List<ExamResultCommentJour> examresultcommentjourList) {
        save(exampaper);
        examquestionService.saveBatch(examquestionList);
        examquestionoptionsService.saveBatch(examquestionoptionsList);
        examResultCommentService.saveBatch(examResultCommentList);
        exampaperjourService.save(exampaperjour);
        examquestionjourService.saveBatch(examquestionjourList);
        examquestionoptionsjourService.saveBatch(examquestionoptionsjourList);
        examresultcommentjourService.saveBatch(examresultcommentjourList);
    }

    private String replace_old_options(HashMap<String, String> old_new_options_id_map, String question_answer) {
        String[] split = question_answer.split(",");
        for (int i = 0; i < split.length; i++) {
            split[i] = old_new_options_id_map.get(split[i]);
        }
        String join = StringUtils.join(split, ",");
        return join;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void enable(BaseUser baseUser, ExamPaper entity) {
        entity = getById(entity.getSerial_id());
        LambdaUpdateWrapper<ExamPaper> updateWrapper = new UpdateWrapper<ExamPaper>().lambda()
                .eq(ExamPaper::getPaper_type, entity.getPaper_type())
                .eq(ExamPaper::getOrgan_flag, entity.getOrgan_flag())
                .eq(ExamPaper::getPaper_name,entity.getPaper_name())
                .eq(StringUtils.isNotBlank(entity.getRegular_expre()),ExamPaper::getRegular_expre,entity.getRegular_expre())
                .eq(StringUtils.isNotBlank(entity.getSub_paper_type()), ExamPaper::getSub_paper_type, entity.getSub_paper_type())
                .eq(ExamPaper::getStatus, Constant.COMMON_VALID_STATUS)
                .set(ExamPaper::getStatus, Constant.COMMON_DELETE_STATUS);
        update(updateWrapper);
        entity.setStatus(Constant.COMMON_VALID_STATUS);
        entity.setModify_by(baseUser.getStaff_no());
        updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void stop(BaseUser baseUser, ExamPaper entity) {
        entity = getById(entity.getSerial_id());
        entity.setStatus(Constant.COMMON_DELETE_STATUS);
        entity.setModify_by(baseUser.getStaff_no());
        updateById(entity);
    }

    public boolean saveJour(ExamPaper entity, boolean isUpdate, Integer business_flag) {
        if (isUpdate) {
            entity = getById(entity.getSerial_id());
        }
        ExamPaperJour exampaperjour = BaseBeanUtil.copyProperties(entity, ExamPaperJour.class);
        exampaperjour.setSerial_id(null);
        exampaperjour.setExampaper_id(entity.getSerial_id());
        exampaperjour.setBusiness_flag(business_flag);
        return exampaperjour.insert();
    }



    public boolean saveExamquestionjour(ExamQuestion examquestion) {
        ExamQuestionJour examquestionjour = BaseBeanUtil.copyProperties(examquestion, ExamQuestionJour.class);
        return examquestionjourService.save(examquestionjour);
    }

    private boolean saveJour(List<ExamPaper> list, boolean isUpdate, Integer business_flag) {
        if (isUpdate) {
            list = listByIds(list.stream().map(x -> x.getSerial_id()).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(list)) {
            List<ExamPaperJour> exampaperjourList = BaseBeanUtil.copyToList(list, ExamPaperJour.class);
            exampaperjourList.forEach(exampaperjour -> {
                exampaperjour.setExampaper_id(exampaperjour.getSerial_id());
                exampaperjour.setSerial_id(null);
                exampaperjour.setBusiness_flag(business_flag);
            });
            exampaperjourService.saveBatch(exampaperjourList);
        }
        return true;
    }
}
