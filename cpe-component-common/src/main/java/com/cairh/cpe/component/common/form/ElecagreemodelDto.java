package com.cairh.cpe.component.common.form;

import com.cairh.cpe.component.common.data.entity.ElecAgreeModel;
import com.cairh.cpe.component.common.data.entity.ElecAgreeModelSub;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-06-06
 */
@EqualsAndHashCode(callSuper = false)
@Data
@ToString(callSuper = true)
public class ElecagreemodelDto extends ElecAgreeModel {

    /**
     * 协议子项列表
     */
    private List<ElecAgreeModelSub> elecagreemodelsubList;

    /**
     * 新增或发布新版本时，是否存在已经存在状态正常且待审核的模板
     * 1：存在，0：不存在
     */
    private String is_exist;
}