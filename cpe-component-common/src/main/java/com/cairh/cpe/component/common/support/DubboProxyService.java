package com.cairh.cpe.component.common.support;

import com.cairh.cpe.component.common.utils.StringUtils;
import com.cairh.cpe.esb.base.rpc.IVBaseDictDubboService;
import com.cairh.cpe.esb.base.rpc.dto.req.VBaseDictQryRequest;
import com.cairh.cpe.esb.base.rpc.dto.resp.VBaseDictQryResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * dubbo服务代理层
 *
 * <AUTHOR>
 * @since 2023/11/8 22:02
 */
@Service
public class DubboProxyService {

    @DubboReference(check = false, lazy = true)
    private IVBaseDictDubboService baseDictDubboService;

    /**
     * 字典查询类
     *
     * @param request
     * @return List<com.cairh.cpe.esb.base.rpc.dto.resp.VBaseDictQryResponse>
     * <AUTHOR>
     * @since 2023/11/8 22:02
     */
    public List<VBaseDictQryResponse> baseDataQryDict(VBaseDictQryRequest request) {
        if (StringUtils.isBlank(request.getSubsys_no())) {
            request.setSubsys_no("10");
        }
        return baseDictDubboService.baseDataQryDict(request);
    }

}
