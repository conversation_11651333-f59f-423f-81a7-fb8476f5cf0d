package com.cairh.cpe.component.common.data.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.component.common.constant.ErrorConstant;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.component.common.data.entity.WitnessRejectionInfo;
import com.cairh.cpe.component.common.data.mapper.WitnessRejectionInfoMapper;
import com.cairh.cpe.component.common.data.service.IWitnessRejectionInfoService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 见证拒绝
 *
 * <AUTHOR>
 * @since 2023/6/7 14:27
 */
@Service
public class WitnessRejectionInfoServiceImpl extends ServiceImpl<WitnessRejectionInfoMapper, WitnessRejectionInfo> implements IWitnessRejectionInfoService {

    @Override
    public boolean save(WitnessRejectionInfo entity) {
        checkExist(entity);
        return super.save(entity);
    }

    @Override
    public boolean updateById(WitnessRejectionInfo entity) {
        checkExist(entity);
        return super.updateById(entity);
    }

    /**
     * 校验数据是否已存在
     *
     * @param entity
     * <AUTHOR>
     * @since 2023/7/13 10:10
     */
    public void checkExist(WitnessRejectionInfo entity) {
        if (Objects.isNull(entity)) {
            return;
        }
        Integer subsysNo = entity.getSubsys_no();
        Integer businType = entity.getBusin_type();
        String causeName = entity.getCause_name();
        LambdaQueryWrapper<WitnessRejectionInfo> queryWrapper = new QueryWrapper<WitnessRejectionInfo>()
                .lambda()
                .eq(WitnessRejectionInfo::getSubsys_no, subsysNo)
                .eq(WitnessRejectionInfo::getBusin_type, businType)
                .eq(WitnessRejectionInfo::getCause_name, causeName);
        List<WitnessRejectionInfo> list = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        String serialId = entity.getSerial_id();
        if (Objects.isNull(serialId)) {
            throw new BizException(ErrorConstant.SAME_DATA_ALREADY_EXISTS, "相同记录已存在请修改");
        }
        if (BooleanUtils.isNotTrue(StringUtils.equals(list.get(0).getSerial_id(), serialId))) {
            throw new BizException(ErrorConstant.SAME_DATA_ALREADY_EXISTS, "相同记录已存在请修改");
        }
    }

}
