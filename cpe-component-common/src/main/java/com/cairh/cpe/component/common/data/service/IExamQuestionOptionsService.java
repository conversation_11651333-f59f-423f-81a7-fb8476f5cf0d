package com.cairh.cpe.component.common.data.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.component.common.data.entity.ExamQuestionOptions;

import java.util.List;

/**
 * <p>
 * 试题选项 服务类
 * </p>
 *
 */
public interface IExamQuestionOptionsService extends IService<ExamQuestionOptions> {

    /**
     * 新增、修改、删除(仅支持同一试题下的选项)
     * @param entityList
     * @return boolean
     * <AUTHOR>
     * @time 2022/5/27
     */
    boolean saveOrUpdateOrDelBatch(BaseUser baseUser, List<ExamQuestionOptions> entityList);

    /**
     * 保存选项 支持批量
     * @param baseUser
     * @param entityList
     */
    void baseSave(BaseUser baseUser, List<ExamQuestionOptions> entityList);

    boolean saveJour(List<ExamQuestionOptions> entityList, boolean isUpdate, Integer business_flag);

    boolean saveJour(ExamQuestionOptions examquestionoptions, Integer business_flag);

    /**
     * 删除问卷下所有选项
     * @param paper_id
     * @return
     */
    List<ExamQuestionOptions> deleteByPaper(String paper_id);

    /**
     * 删除试题下所有选项
     * @param question_id
     * @return
     */
    List<ExamQuestionOptions> deleteByQuestion(String question_id);


}