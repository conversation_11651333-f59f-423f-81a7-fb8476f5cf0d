package com.cairh.cpe.component.common.cache;

import com.cairh.cpe.component.common.cache.branch.CacheBranch;
import com.cairh.cpe.component.common.cache.dict.CacheDict;
import com.cairh.cpe.component.common.cache.operator.CacheOperatorInfo;
import com.cairh.cpe.esb.base.rpc.dto.resp.VBaseAllBranchQryResponse;
import com.cairh.cpe.esb.base.rpc.dto.resp.VBaseQryUserInfosResponse;
import com.cairh.cpe.esb.base.rpc.dto.resp.support.Basedictionary;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.ContextualSerializer;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.Objects;

/**
 * 功能说明: 数据转换<br>
 * 系统版本: v1.0<br>
 * 如果需要通过新属性名输出，则需要跟新属性写get方法，通过get方法获取字典值进行翻译
 *  	@TableField(exist = false)
 *  	@DataConvert(code_type = DataConvert.DICT, code_dict = "subsys_no")
 *  	private String subsys_no_tranfer;
 *
 *     public String getSubsys_no_tranfer() {
 *         if(subsys_no_tranfer == null){
 *             return String.valueOf(subsys_no);
 *         }
 *         return subsys_no_tranfer;
 *     }
 * 如果直接替换原属性内容，则直接在属性上加标注
 * 营业部，操作员翻译，不需要带code_dict
 */
@Slf4j
public class DataConvertSerializer extends StdSerializer<Object> implements ContextualSerializer {

	private DataConvert dataConvert;

	private String code_type;

	private String code_dict;

	DataConvertSerializer() {
		super(Object.class);
	}

	public DataConvertSerializer(DataConvert dataConvert) {
		super(Object.class);
		this.dataConvert = dataConvert;
	}

	@Override
	public JsonSerializer<?> createContextual(SerializerProvider prov, BeanProperty beanProperty) throws JsonMappingException {

		if (Objects.isNull(beanProperty)) {
			return prov.findValueSerializer(beanProperty.getType(), beanProperty);
		}
		DataConvert data_convert = beanProperty.getAnnotation(DataConvert.class);
		if (Objects.nonNull(data_convert)) {
			code_type = data_convert.code_type();
			code_dict = data_convert.code_dict();
			return this;
		}
		return prov.findNullValueSerializer(null);
	}

	@Override
	public void serialize(Object value, JsonGenerator gen, SerializerProvider provider) throws IOException {

		if (Objects.isNull(value)) {
			gen.writeObject(value);
			return;
		}
		if (StringUtils.isBlank(String.valueOf(value))) {//空字符串
			gen.writeObject(value);
			return;
		}
		if (Objects.nonNull(dataConvert)) {
			code_type = dataConvert.code_type();
			code_dict = dataConvert.code_dict();
		}
		//数据转换类型判断
		String newValue = String.valueOf(value);
		if (DataConvert.DICT.equals(code_type)) {//字典
			CacheDict cacheDictionaryService = ApplicationContextHolder.get(CacheDict.class);
			Basedictionary basedictionary = cacheDictionaryService.baseDataQryDict(code_dict, String.valueOf(value));
			if(!ObjectUtils.isEmpty(basedictionary)){
				newValue = basedictionary.getSub_name();
			}
		}else if(DataConvert.BRANCH.equals(code_type)){//营业部
			CacheBranch cacheBranchService = ApplicationContextHolder.get(CacheBranch.class);
			VBaseAllBranchQryResponse branchQryResponse = cacheBranchService.baseDataQryBranch(String.valueOf(value));
			if(!ObjectUtils.isEmpty(branchQryResponse)){
				newValue = branchQryResponse.getBranch_name();
			}
		}else if(DataConvert.OPERATORINFO.equals(code_type)){//操作员
			CacheOperatorInfo cacheOperratorInfoService = ApplicationContextHolder.get(CacheOperatorInfo.class);
			VBaseQryUserInfosResponse userInfosResponse = cacheOperratorInfoService.baseDataQryOperatorInfo(String.valueOf(value));
			if(!ObjectUtils.isEmpty(userInfosResponse)){
				newValue = userInfosResponse.getUser_name();
			}
		}

		gen.writeObject(newValue.toString());
	}

}
