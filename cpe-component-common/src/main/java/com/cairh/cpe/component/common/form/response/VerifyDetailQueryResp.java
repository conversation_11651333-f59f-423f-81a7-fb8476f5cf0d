package com.cairh.cpe.component.common.form.response;


import com.cairh.cpe.common.backend.annotation.Desensitize;
import com.cairh.cpe.component.common.cache.DataConvert;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

import static com.cairh.cpe.common.backend.masking.mode.DataMaskingMode.ID_NO;
import static com.cairh.cpe.common.backend.masking.mode.DataMaskingMode.MOBILE_TEL;

/**
 * 公安认证详情查询
 * <AUTHOR>
 * @since 2022-09-02
 */
@Data
public class VerifyDetailQueryResp implements Serializable {

    private static final long serialVersionUID = -5810250866170056971L;

    /**
     * ID
     */
    private String serial_id;

    /**
     * 客户证件类型
     */
    private String id_kind;

    /**
     * 客户证件号码
     */
    @Desensitize(mode = ID_NO)
    private String id_no;

    /**
     * 账户全称
     */
    private String full_name;

    /**
     * 手机号码
     */
    @Desensitize(mode = MOBILE_TEL)
    private String mobile_tel;

    /**
     * 认证类型
     */
    private String verify_type;

    @DataConvert(code_type = DataConvert.DICT, code_dict = "verify_type")
    private String verify_type_tranfer;

    public String getVerify_type_tranfer() {
        if(verify_type_tranfer == null){
            return verify_type;
        }
        return verify_type_tranfer;
    }

    /**
     * 状态 是否实名认证  0：否  1：是
     */
    private String status;

    /**
     * 公安接口信息
     */
    private String result_info;

    /**
     * 文件记录ID
     */
    private String filerecord_id;

    /**
     * 实现厂商
     */
    private String factory_name;

    @DataConvert(code_type = DataConvert.DICT, code_dict = "factory_name")
    private String factory_name_tranfer;

    public String getFactory_name_tranfer() {
        if(factory_name_tranfer == null){
            return factory_name;
        }
        return factory_name_tranfer;
    }

    /**
     * 创建时间
     */
    private String create_datetime;

    /**
     * 归历史标志
     */
    private String tohis_flag;

    /**
     * 归历史时间
     */
    private Date tohis_datetime;

    /**
     * 运营商
     */
    private String operator;

    /**
     * 中登业务类型 01：手机号码当前状态查询  02：手机号码与客户名称对应关系核查03：手机号码与身份证明文件对应关系核查
     */
    private String csdc_busi_kind;

    /**
     * 清算日期
     */
    private Integer date_clear;

    /**
     * 操作员营业部编号
     */
    private String op_branch_no;

    /**
     * 操作员营业部名称
     */
    private String op_branch_name;

    /**
     * 用户营业部编号
     */
    private String branch_no;

    /**
     * 用户营业部名称
     */
    private String branch_name;

    /**
     * 操作员编号
     */
    private String operator_no;

    /**
     * 操作员姓名
     */
    private String operator_name;

    /**
     * 认证厂商中文
     */
    private String auth_config;
}