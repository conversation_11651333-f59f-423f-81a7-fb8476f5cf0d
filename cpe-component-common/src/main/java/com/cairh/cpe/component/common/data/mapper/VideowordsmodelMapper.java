package com.cairh.cpe.component.common.data.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cairh.cpe.component.common.data.entity.VideoWordsModel;
import com.cairh.cpe.component.common.form.VideowordsmodelDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 视频话术模板表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-11
 */
public interface VideowordsmodelMapper extends BaseMapper<VideoWordsModel> {

    List<VideowordsmodelDto> queryModelAndConfig(@Param("video_type") String video_type, @Param("model_id_list") List<String> model_id_list);

}
