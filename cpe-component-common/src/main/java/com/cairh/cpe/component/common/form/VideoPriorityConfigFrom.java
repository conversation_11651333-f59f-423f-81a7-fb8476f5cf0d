package com.cairh.cpe.component.common.form;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.cairh.cpe.component.common.data.entity.VideoPriorityConfig;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class VideoPriorityConfigFrom extends BasePage<VideoPriorityConfig> {
    {
        OrderItem orderItem = new OrderItem();
        orderItem.setAsc(false);
        orderItem.setColumn("create_datetime");
        ArrayList<OrderItem> orderItems = new ArrayList<>();
        orderItems.add(orderItem);
        super.setOrders(orderItems);
    }
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date[] curr_date_range;

    private String serial_id;

    /**
     * 优先级字段名称
     */
    private String field_name;

    /**
     * 字段的优先级
     */
    private Integer field_priority;

    /**
     * 优先级字段值
     */
    private String field_value;

    /**
     * 字段值的优先级
     */
    private Integer value_priority;
    /**
     * 创建时间
     */
    private Date create_datetime;
    /**
     * 修改时间
     */
    private Date modify_datetime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private String create_by;

    /**
     * 是否启用
     */
    private String enable_flag ;
}
