package com.cairh.cpe.component.common.form;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.cairh.cpe.component.common.constant.Constant;
import com.cairh.cpe.component.common.data.entity.ExamQuestion;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper=false)
@ToString(callSuper = true)
public class ExamquestionForm extends BasePage<ExamQuestion> {

    {
        OrderItem orderItem = new OrderItem();
        orderItem.setAsc(true);
        orderItem.setColumn("order_no");
        ArrayList<OrderItem> orderItems = new ArrayList<>();
        orderItems.add(orderItem);
        super.setOrders(orderItems);
    }

    /**
     * 试题ID
     */
    private String serial_id;

    /**
     * 问卷ID
     */
    private String exampaper_id;

    /**
     * 问题编号
     */
    private String question_no;

    /**
     * 题目名称
     */
    private String question_name;

    /**
     * 排序信息
     */
    private Long order_no;

    /**
     * 分数
     */
    private BigDecimal score;

    /**
     * 试题类别
     */
    private String question_type;

    /**
     * 试题类型
     */
    private String question_kind;

    /**
     * 关联类型
     */
    private String relation_type;

    /**
     * 试题标准答案
     */
    private String question_answer;

    /**
     * 备注
     */
    private String remark;

    /**
     * 默认选择项
     */
    private String default_option_ids;

    private String create_by;

    /**
     * 创建日期时间
     */
    private Date create_datetime;

    private String modify_by;

    /**
     * 修改时间
     */
    private Date modify_datetime;

    /**
     * 提示信息
     */
    private String answer_hint;

    /**
     * 指标编码
     */
    private String indicator_id;

    /**
     * 是否显示
     */
    private String show_flag;

    /**
     * 归历史标志
     */
    private String tohis_flag;

    /**
     * 归历史时间
     */
    private Date tohis_datetime;

    /**
     * 试题分组
     */
    private String question_group;

    private String status = Constant.COMMON_VALID_STATUS;
}
