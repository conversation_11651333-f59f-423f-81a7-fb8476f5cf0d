package com.cairh.cpe.component.common.data.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cairh.cpe.component.common.data.entity.InterfaceDataJour;
import com.cairh.cpe.component.common.data.entity.ServiceQualityMonitor;
import com.cairh.cpe.component.common.form.AgreemodelbusinessForm;
import com.cairh.cpe.component.common.form.AgreemodelbusinessVo;
import com.cairh.cpe.component.common.form.InterfaceDataJourDisplayForm;
import com.cairh.cpe.component.common.form.InterfaceDataJourDisplayVo;
import com.cairh.cpe.component.common.form.InterfaceDataJourForm;
import com.cairh.cpe.component.common.form.InterfaceDataJourVo;
import com.cairh.cpe.context.BaseUser;

import java.util.List;

public interface IInterfaceDataJourService extends IService<InterfaceDataJour> {

	/**
	 * 记录流水
	 *
	 * @param jour_kind   流水类型(1：公安认证，2：短信)
	 * @param fail_num
	 * @param success_num
	 */
	public void saveJour(String jour_kind, int success_num, int fail_num);

	public Page<InterfaceDataJourVo> queryByPage(InterfaceDataJourForm param);

	/**
	 * 数据显示
	 * @param param
	 * @return
	 */
	List<InterfaceDataJourDisplayVo> datadisplay(InterfaceDataJourDisplayForm param);
}