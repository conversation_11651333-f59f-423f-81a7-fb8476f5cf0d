package com.cairh.cpe.component.common.form;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.cairh.cpe.component.common.data.entity.RiskUserInfo;
import com.cairh.cpe.component.common.data.entity.ServiceQualityMonitor;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.ArrayList;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper=false)
@ToString(callSuper = true)
public class ServiceQualityMonitorForm extends BasePage<ServiceQualityMonitor> {

    {
        OrderItem orderItem1 = new OrderItem();
        OrderItem orderItem2 = new OrderItem();
        orderItem1.setAsc(false);
        orderItem2.setAsc(false);
        orderItem1.setColumn("create_datetime");
        orderItem2.setColumn("serial_id");
        ArrayList<OrderItem> orderItems = new ArrayList<>();
        orderItems.add(orderItem1);
        orderItems.add(orderItem2);
        super.setOrders(orderItems);
    }


    /**
     * 客户姓名
     */
    private String user_name;

    /**
     * 手机号
     */
    private String mobile_tel;

    /**
     * 证件号码
     */
    private String id_no;

    /**
     * 客户所在营业部
     */
    private String branch_no;

    /**
     * 接入来源
     */
    private String app_id;

    /**
     * 子系统编号
     */
    private Integer subsys_no;

    /**
     * 查询的时间范围
     */
    private String[] query_date_range;
}
