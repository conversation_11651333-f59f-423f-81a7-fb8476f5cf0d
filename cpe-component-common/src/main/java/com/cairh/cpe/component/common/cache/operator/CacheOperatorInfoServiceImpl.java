package com.cairh.cpe.component.common.cache.operator;

import com.cairh.cpe.component.common.cache.branch.ICacheBranchService;
import com.cairh.cpe.esb.base.rpc.IVBaseAllBranchDubboService;
import com.cairh.cpe.esb.base.rpc.IVBaseUserInfoDubboService;
import com.cairh.cpe.esb.base.rpc.dto.req.VBaseAllBranchQryRequest;
import com.cairh.cpe.esb.base.rpc.dto.req.VBaseQryUserInfosRequest;
import com.cairh.cpe.esb.base.rpc.dto.resp.VBaseAllBranchQryResponse;
import com.cairh.cpe.esb.base.rpc.dto.resp.VBaseQryUserInfosResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年01月11日 13:47
 */
@Slf4j
@Component
public class CacheOperatorInfoServiceImpl implements ICacheOperatorInfoService {

	@DubboReference(check = false, lazy = true)
	private IVBaseUserInfoDubboService ivBaseUserInfoDubboService;

	@Override
	@Cacheable(value = "compOperatorinfo", key = "#root.targetClass.simpleName+'.'+#root.method.name")
	public List<VBaseQryUserInfosResponse> getOperatorinfos() {
		VBaseQryUserInfosRequest baseQryUserInfosRequest = new VBaseQryUserInfosRequest();
		baseQryUserInfosRequest.setBranch_no("");
		baseQryUserInfosRequest.setUser_name("");
		baseQryUserInfosRequest.setUser_name_precise(false);
		List<VBaseQryUserInfosResponse> baseQryUserInfosResponses = this.ivBaseUserInfoDubboService.baseUserQryUserInfoList(baseQryUserInfosRequest);
		return baseQryUserInfosResponses;
	}

	@Override
	@Scheduled(fixedDelay = 10 * 60 * 1000)
	@CacheEvict(value = "compOperatorinfo", allEntries = true)
	public void refresh() {
		log.debug("Cache[compOperatorinfo]执行清除.");
	}
}
