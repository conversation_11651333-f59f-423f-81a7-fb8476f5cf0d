package com.cairh.cpe.component.common.data.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.cairh.cpe.component.common.cache.DataConvert;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 见证拒绝信息
 *
 * <AUTHOR>
 * @since 2023/6/7 13:55
 */
@Setter
@Getter
@Accessors(chain = true)
@TableName("witnessrejectioninfo")
public class WitnessRejectionInfo extends Model<WitnessRejectionInfo> {
    /**
     * ID
     */
    @TableId("serial_id")
    private String serial_id;

    @TableField("subsys_no")
    private Integer subsys_no;
    @TableField(exist = false)
    @DataConvert(code_type = DataConvert.DICT, code_dict = "subsys_no")
    private String subsys_no_tranfer;

    public String getSubsys_no_tranfer() {
        if(subsys_no_tranfer == null){
            return String.valueOf(subsys_no);
        }
        return subsys_no_tranfer;
    }

    @TableField("busin_type")
    private Integer busin_type;
    @TableField(exist = false)
    @DataConvert(code_type = DataConvert.DICT, code_dict = "busin_type")
    private String busin_type_tranfer;

    public String getBusin_type_tranfer() {
        if(busin_type_tranfer == null){
            return String.valueOf(busin_type);
        }
        return busin_type_tranfer;
    }

    /**
     * 拒绝名称
     */
    @TableField("cause_name")
    private String cause_name;

    /**
     * 拒绝内容
     */
    @TableField("cause_content")
    private String cause_content;

    /**
     * 排序字段
     */
    @TableField(value = "order_no")
    private String order_no;

    /**
     * 规则表达式
     */
    @TableField(value = "regular_expre")
    private String regular_expre;

    /**
     * 状态
     */
    @TableField(value = "status", fill = FieldFill.INSERT)
    private String status;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String create_by;

    /**
     * 创建日期时间
     */
    @TableField(value = "create_datetime", fill = FieldFill.INSERT)
    private Date create_datetime;

    /**
     * 修改人
     */
    @TableField("modify_by")
    private String modify_by;

    /**
     * 修改时间
     */
    @TableField(value = "modify_datetime", fill = FieldFill.INSERT_UPDATE)
    private Date modify_datetime;

    @Override
    public Serializable pkVal() {
        return this.serial_id;
    }

}
