package com.cairh.cpe.component.common.form;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.cairh.cpe.component.common.constant.Constant;
import com.cairh.cpe.component.common.data.entity.FundProduct;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.ArrayList;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper=false)
@ToString(callSuper = true)
public class FundProductForm extends BasePage<FundProduct>{

    {
        OrderItem orderItem1 = new OrderItem();
        OrderItem orderItem2 = new OrderItem();
        orderItem1.setAsc(false);
        orderItem1.setColumn("modify_datetime");
        orderItem2.setAsc(true);
        orderItem2.setColumn("prod_code");
        ArrayList<OrderItem> orderItems = new ArrayList<>();
        orderItems.add(orderItem1);
        orderItems.add(orderItem2);
        super.setOrders(orderItems);
    }
    /**
     * 序列号
     */
    private String serial_id;

    /**
     * 基金产品代码
     */
    private String prod_code;

    /**
     * 基金产品简称
     */
    private String product_short_name;

    /**
     * 基金产品全称
     */
    private String product_full_name;

    /**
     * 基金产品类别
     */
    private String fund_product_type;

    /**
     * 基金公司代码
     */
    private String company_code;

    /**
     * 创建人
     */
    private String create_by;

    /**
     * 创建日期时间
     */
    private Date create_datetime;

    /**
     * 修改人
     */
    private String modify_by;

    /**
     * 修改时间
     */
    private Date modify_datetime;
}
