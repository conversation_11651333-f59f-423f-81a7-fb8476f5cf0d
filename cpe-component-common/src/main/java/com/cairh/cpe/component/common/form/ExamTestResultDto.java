package com.cairh.cpe.component.common.form;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.cairh.cpe.component.common.cache.DataConvert;
import com.cairh.cpe.component.common.data.entity.ExamPaper;
import com.cairh.cpe.component.common.data.entity.ExamTestResult;
import com.cairh.cpe.component.common.data.entity.ExamTestResultOther;
import com.cairh.cpe.component.common.data.entity.RiskUserInfo;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 客户测评记录菜单
 *
 * <AUTHOR>
 */
@Data
public class ExamTestResultDto {
    /**
     * 问卷类型
     */
    private String paper_type;

    //翻译
    @TableField(exist = false)
    @DataConvert(code_type = DataConvert.DICT, code_dict = "paper_type")
    private String paper_type_tranfer;

    public String getPaper_type_tranfer() {
        if(paper_type_tranfer == null){
            return paper_type;
        }
        return paper_type_tranfer;
    }

    /**
     * 机构标志
     */
    private String organ_flag;
    /**
     * 问卷名称
     */
    private String paper_name;
    /**
     * 所属营业部 编号
     */
    private BigDecimal branch_no;
    /**
     * 所属营业部
     */
    private BigDecimal branch_name;

    /**
     * 测评编号
     */
    private String serial_id;

    /**
     * 问卷编号
     */
    private String exampaper_id;

    /**
     * 分数
     */
    private BigDecimal score;

    /**
     * 状态
     */
    private String status;

    /**
     * 测评有效期开始日
     */
    private String risk_begin_date;

    /**
     * 测评有效期结束日
     */
    private String risk_end_date;

    /**
     * 风测 试卷操作
     */
    private String paper_answer;

    /**
     * 问卷提交时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date submit_datetime;

    /**
     * 问卷确认时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date confirm_datetime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private String render_data;

    /**
     * 归历史标识
     */
    private String tohis_flag;

    /**
     * 归历史时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date tohis_datetime;

    /**
     * 清算日期
     */
    private String date_clear;

    /**
     * 风测客户信息
     */

    /**
     * 客户号
     */
    private String client_id;
    /**
     * 客户姓名
     */
    private String client_name;
    /**
     * 证件类别
     */
    private String id_kind;
    /**
     * 证件号码
     */
    private String id_no;

    /**
     * 问卷内容
     */
    private ExamPaper examPaper;
}
