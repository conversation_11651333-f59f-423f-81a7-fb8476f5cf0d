package com.cairh.cpe.component.common.data.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.component.common.data.entity.AgreeModelBusiness;
import com.cairh.cpe.component.common.form.AgreemodelbusinessDto;
import com.cairh.cpe.component.common.form.AgreemodelbusinessForm;
import com.cairh.cpe.component.common.form.AgreemodelbusinessVo;

/**
 * <p>
 * 协议业务参数 服务类
 * </p>
 *
 */
public interface IAgreemodelbusinessService extends IService<AgreeModelBusiness> {

    /**
     * 新增协议规则
     * @param entity
     * @return
     */
    boolean baseSave(BaseUser baseUser, AgreemodelbusinessDto entity);

    /**
     * 修改协议规则
     * @param entity
     * @return
     */
    boolean baseUpdate(BaseUser baseUser,AgreemodelbusinessDto entity);

    /**
     * 详情查询
     * @param subsys_no
     * @param busin_type
     * @return
     */
    AgreemodelbusinessDto queryOne(Integer subsys_no, Integer busin_type, String organ_flag);

    /**
     * 分页查询
     * @param param
     * @return
     */
    Page<AgreemodelbusinessVo> queryByPage(AgreemodelbusinessForm param);
}