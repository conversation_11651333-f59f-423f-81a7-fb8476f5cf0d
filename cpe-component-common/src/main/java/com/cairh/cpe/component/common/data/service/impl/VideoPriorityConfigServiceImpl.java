package com.cairh.cpe.component.common.data.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.component.common.data.entity.VideoPriorityConfig;
import com.cairh.cpe.component.common.data.mapper.VideoPriorityConfigMapper;
import com.cairh.cpe.component.common.data.service.IVideoPriorityConfigService;
import com.cairh.cpe.component.common.form.support.PriorityFilterResult;
import com.cairh.cpe.component.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class VideoPriorityConfigServiceImpl extends ServiceImpl<VideoPriorityConfigMapper, VideoPriorityConfig> implements IVideoPriorityConfigService {

    @Autowired
    private VideoPriorityConfigMapper videoPriorityConfigMapper;
    @Override
    public PriorityFilterResult checkAmbiguous(VideoPriorityConfig entity) {
        PriorityFilterResult result = new PriorityFilterResult();
        if (ObjectUtil.isEmpty(entity.getField_value())) {
            result.setAmbiguous(true);
            result.setInfo("无效配置，优先级字段值不能为空");
            return result;
        }

        // 整合包含当前配置的所有优先级配置
        LambdaQueryWrapper<VideoPriorityConfig> queryWrapper = new LambdaQueryWrapper<VideoPriorityConfig>()
                .eq(VideoPriorityConfig::getEnable_flag, "1")
                .eq(ObjectUtil.isNotEmpty(entity.getField_priority()), VideoPriorityConfig::getField_priority, entity.getField_priority())
                .eq(ObjectUtil.isNotEmpty(entity.getField_name()), VideoPriorityConfig::getField_name, entity.getField_name())
                .ne(ObjectUtil.isNotEmpty(entity.getSerial_id()), VideoPriorityConfig::getSerial_id, entity.getSerial_id())
                .eq(ObjectUtil.isNotEmpty(entity.getField_value()), VideoPriorityConfig::getField_value, entity.getField_value());


        List<VideoPriorityConfig> configs = videoPriorityConfigMapper.selectList(queryWrapper);
        //   无效配置，优先级字段值已存在  无效配置，相同记录已存在
        if (configs.size() > 0) {
            result.setAmbiguous(true);
            result.setInfo("无效配置，该配置已存在");
            return result;
        }
        return result;
    }

}
