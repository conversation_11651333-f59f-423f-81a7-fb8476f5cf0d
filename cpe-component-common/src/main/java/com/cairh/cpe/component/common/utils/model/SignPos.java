package com.cairh.cpe.component.common.utils.model;

/**
 * 签名位置信息
 * 功能说明: <br>
 * 系统版本: v1.0<br>
 * 开发人员: <AUTHOR>
 * 开发时间: Dec 16, 2018<br>
 */
public class SignPos {
	//签名类型 0:根据关键字签名 1：根据位置签名
	private String signtype;
    //签名关键字
    private String kw ;
    //关键字从上至下查找顺序号
    private String index ;
    //宽度
    private String width;
    //高度
    private String height;
    //x轴偏移量
    private String xOffset;
    //y轴偏移量
    private String yOffset;
    //机构表示 0 个人 1 机构
    private String organ;


    public String getKw() {
        return kw;
    }

    public void setKw(String kw) {
        this.kw = kw;
    }

    public String getIndex() {
        return index;
    }

    public void setIndex(String index) {
        this.index = index;
    }

    public String getWidth() {
        return width;
    }

    public void setWidth(String width) {
        this.width = width;
    }

    public String getHeight() {
        return height;
    }

    public void setHeight(String height) {
        this.height = height;
    }

    public String getxOffset() {
        return xOffset;
    }

    public void setxOffset(String xOffset) {
        this.xOffset = xOffset;
    }

    public String getyOffset() {
        return yOffset;
    }

    public void setyOffset(String yOffset) {
        this.yOffset = yOffset;
    }

    public String getOrgan() {
        return organ;
    }

    public void setOrgan(String organ) {
        this.organ = organ;
    }

	public String getSigntype() {
		return signtype;
	}

	public void setSigntype(String signtype) {
		this.signtype = signtype;
	}

}
