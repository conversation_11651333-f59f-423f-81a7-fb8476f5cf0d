package com.cairh.cpe.component.common.form;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.cairh.cpe.component.common.data.entity.ElecAgreeModel;
import com.cairh.cpe.component.common.data.entity.RiskUserInfo;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.ArrayList;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper=false)
@ToString(callSuper = true)
public class RiskUserInfoForm extends BasePage<RiskUserInfo> {

    {
        OrderItem orderItem1 = new OrderItem();
        OrderItem orderItem2 = new OrderItem();
        orderItem1.setAsc(false);
        orderItem2.setAsc(false);
        orderItem1.setColumn("modify_datetime");
        orderItem2.setColumn("serial_id");
        ArrayList<OrderItem> orderItems = new ArrayList<>();
        orderItems.add(orderItem1);
        orderItems.add(orderItem2);
        super.setOrders(orderItems);
    }

    /**
     * 客户号
     */
    private String client_id;

    /**
     * 客户姓名
     */
    private String user_name;

    /**
     * 机构标志
     */
    private String organ_flag;

    /**
     * 证件类别
     */
    private String id_kind;

    /**
     * 证件号码
     */
    private String id_no;


    /**日期范围*/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date [] curr_date_range;
}
