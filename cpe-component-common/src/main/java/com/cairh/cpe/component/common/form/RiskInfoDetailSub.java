package com.cairh.cpe.component.common.form;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 风险评测变更详情子项
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-06
 */
@Data
public class RiskInfoDetailSub{

    /**
     * 变更后
     */
    private String newvalue;

    /**
     * 变更前
     */
    private String oldvalue;

    /**
     * 变更项
     */
    private String column;

    /**
     * 类型：字典项
     */
    private String type;

}
