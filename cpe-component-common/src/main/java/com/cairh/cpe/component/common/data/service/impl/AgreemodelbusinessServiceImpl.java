package com.cairh.cpe.component.common.data.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.component.common.utils.BaseBeanUtil;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.context.constant.ErrorCode;
import com.cairh.cpe.component.common.constant.Constant;
import com.cairh.cpe.component.common.data.entity.AgreeModelBusiness;
import com.cairh.cpe.component.common.data.entity.AgreeModelBusinessJour;
import com.cairh.cpe.component.common.data.entity.ElecAgreeModel;
import com.cairh.cpe.component.common.data.mapper.AgreeModelBusinessMapper;
import com.cairh.cpe.component.common.data.mapper.ElecAgreeModelMapper;
import com.cairh.cpe.component.common.data.service.IAgreemodelbusinessService;
import com.cairh.cpe.component.common.data.service.IAgreemodelbusinessjourService;
import com.cairh.cpe.component.common.form.AgreemodelbusinessDto;
import com.cairh.cpe.component.common.form.AgreemodelbusinessForm;
import com.cairh.cpe.component.common.form.AgreemodelbusinessVo;
import com.cairh.cpe.component.common.form.support.AgreementRel;
import com.cairh.cpe.component.common.utils.JourUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 协议业务参数 服务实现类
 * </p>
 */
@Service
public class AgreemodelbusinessServiceImpl extends ServiceImpl<AgreeModelBusinessMapper, AgreeModelBusiness> implements IAgreemodelbusinessService {

    @Autowired
    private ElecAgreeModelMapper elecagreemodelMapper;

    @Autowired
    private IAgreemodelbusinessjourService agreemodelbusinessjourService;

    @Override
    public boolean baseSave(BaseUser baseUser, AgreemodelbusinessDto entity) {
        List<AgreementRel> agreementRelList = entity.getAgreementRelList();
        if (checkRepeat(agreementRelList)) {
            throw new BizException(ErrorCode.ERR_PARAM_IN_ERROR, "添加失败，存在重复的协议");
        }
        Integer count = Math.toIntExact(this.lambdaQuery()
                .eq(AgreeModelBusiness::getSubsys_no, entity.getSubsys_no())
                .eq(AgreeModelBusiness::getBusin_type, entity.getBusin_type())
                .eq(AgreeModelBusiness::getOrgan_flag, entity.getOrgan_flag())
                .count());
        if (count > 0) {
            throw new BizException(ErrorCode.ERR_PARAM_IN_ERROR, "该业务规则已存在,您可以直接去添加协议");
        }
        List<AgreeModelBusiness> agreemodelbusinessList = dtoToList(baseUser, entity);
        this.saveBatch(agreemodelbusinessList);
        JourUtil.writeJour(() -> saveJour(agreemodelbusinessList, false, Constant.BUSINESS_FLAG_ADD));
        return true;
    }

    private boolean checkRepeat(List<AgreementRel> agreementRelList) {
        Map<String, AgreementRel> collect = new HashMap<>();
        for (AgreementRel agreementRel : agreementRelList) {
            if (collect.containsKey(agreementRel.getElecagreemodel_id())) {
                return true;
            }
            collect.put(agreementRel.getElecagreemodel_id(), agreementRel);
        }
        return false;
    }

    @Transactional
    @Override
    public boolean baseUpdate(BaseUser baseUser, AgreemodelbusinessDto entity) {
        List<AgreementRel> agreementRelList = entity.getAgreementRelList();
        if (CollectionUtils.isEmpty(agreementRelList)) {
            throw new BizException(ErrorCode.ERR_PARAM_IN_ERROR, "至少需要选择一个协议");
        }
        if (checkRepeat(entity.getAgreementRelList())) {
            throw new BizException(ErrorCode.ERR_PARAM_IN_ERROR, "操作失败，存在重复的协议");
        }
        ArrayList<String> idList = new ArrayList<>();
        List<AgreeModelBusiness> agreemodelbusinessList = dtoToList(baseUser, entity, idList);
        //        删除
        this.remove(new QueryWrapper<AgreeModelBusiness>().lambda()
                .eq(AgreeModelBusiness::getBusin_type, entity.getBusin_type())
                .eq(AgreeModelBusiness::getSubsys_no, entity.getSubsys_no())
                .eq(AgreeModelBusiness::getOrgan_flag, entity.getOrgan_flag())
                .notIn(CollectionUtils.isNotEmpty(idList), AgreeModelBusiness::getSerial_id, idList));
        //新增或修改
        this.saveOrUpdateBatch(agreemodelbusinessList);
        JourUtil.writeJour(() -> saveJour(agreemodelbusinessList, true, Constant.BUSINESS_FLAG_MOD));
        return false;
    }

    private boolean saveJour(AgreeModelBusiness agreemodelbusiness, boolean isUpdate, Integer business_flag) {
        if (isUpdate) {
            agreemodelbusiness = getById(agreemodelbusiness.getSerial_id());
        }
        AgreeModelBusinessJour agreemodelbusinessjour = BaseBeanUtil.copyProperties(agreemodelbusiness, AgreeModelBusinessJour.class);
        agreemodelbusinessjour.setAgreemodelbusiness_id(agreemodelbusiness.getSerial_id());
        agreemodelbusinessjour.setSerial_id(null);
        agreemodelbusinessjour.setBusiness_flag(business_flag);
        return agreemodelbusinessjour.insert();
    }

    private boolean saveJour(List<AgreeModelBusiness> agreemodelbusinessList, boolean isUpdate, Integer business_flag) {
        List<AgreeModelBusinessJour> agreemodelbusinessjourList = null;
        if (isUpdate) {
            List<String> idlist = agreemodelbusinessList.stream().map(x -> x.getSerial_id()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(idlist)) {
                List<AgreeModelBusiness> list = this.lambdaQuery().in(AgreeModelBusiness::getSerial_id, idlist).list();
                agreemodelbusinessjourList = BaseBeanUtil.copyToList(list, AgreeModelBusinessJour.class);
            }
        } else {
            agreemodelbusinessjourList =
                    BaseBeanUtil.copyToList(agreemodelbusinessList, AgreeModelBusinessJour.class);
        }
        if (CollectionUtils.isNotEmpty(agreemodelbusinessjourList)) {
            agreemodelbusinessjourList.forEach(x -> {
                x.setAgreemodelbusiness_id(x.getSerial_id());
                x.setSerial_id(null);
                x.setBusiness_flag(business_flag);
            });
            agreemodelbusinessjourService.saveBatch(agreemodelbusinessjourList);
        }
        return true;
    }

    @Override
    public AgreemodelbusinessDto queryOne(Integer subsys_no, Integer busin_type, String organ_flag) {
        List<AgreeModelBusiness> agreemodelbusinessList = this.lambdaQuery()
                .eq(AgreeModelBusiness::getSubsys_no, subsys_no)
                .eq(AgreeModelBusiness::getBusin_type, busin_type)
                .eq(AgreeModelBusiness::getOrgan_flag, organ_flag)
                .list();
        return listToDto(agreemodelbusinessList);
    }

    @Override
    public Page<AgreemodelbusinessVo> queryByPage(AgreemodelbusinessForm param) {
        return baseMapper.diySelectPage(param);
    }

    private AgreemodelbusinessDto listToDto(List<AgreeModelBusiness> agreemodelbusinessList) {
        List<AgreementRel> agreementRelList = BaseBeanUtil.copyToList(agreemodelbusinessList, AgreementRel.class);
        List<String> elecagreemodelIdList = agreementRelList.stream().map(AgreementRel::getElecagreemodel_id).collect(Collectors.toList());
        List<ElecAgreeModel> elecagreemodelList = elecagreemodelMapper.selectList(
                new QueryWrapper<ElecAgreeModel>().in("serial_id", elecagreemodelIdList)
        );
        Map<String, ElecAgreeModel> elecagreemodelMap = elecagreemodelList.stream().collect(Collectors.toMap(x -> x.getSerial_id(), x -> x, (x1, x2) -> x1));
        agreementRelList.forEach(agreementRel -> {
            ElecAgreeModel elecagreemodel = elecagreemodelMap.get(agreementRel.getElecagreemodel_id());
            if (Objects.nonNull(elecagreemodel)) {
                agreementRel.setAgreement_type(elecagreemodel.getAgreement_type());
                agreementRel.setAgreement_name(elecagreemodel.getAgreement_name());
                agreementRel.setAgreement_version(elecagreemodel.getAgreement_version());
                agreementRel.setAgreement_no(elecagreemodel.getAgreement_no());
                agreementRel.setEx_name(elecagreemodel.getEx_name());
            }
        });
        AgreemodelbusinessDto agreemodelbusinessDto = BaseBeanUtil.copyProperties(agreemodelbusinessList.get(0), AgreemodelbusinessDto.class);
        agreemodelbusinessDto.setAgreementRelList(agreementRelList);
        return agreemodelbusinessDto;
    }

    private List<AgreeModelBusiness> dtoToList(BaseUser baseUser, AgreemodelbusinessDto entity) {
        if (CollectionUtils.isEmpty(entity.getAgreementRelList())) {
            throw new BizException(ErrorCode.ERR_PARAM_IN_ERROR, "至少需要选择一个协议");
        }
        List<String> idList = entity.getAgreementRelList().stream().map(x -> x.getElecagreemodel_id()).collect(Collectors.toList());
        Map<String, List<AgreeModelBusiness>> map = this.lambdaQuery()
                .in(AgreeModelBusiness::getElecagreemodel_id, idList)
                .eq(AgreeModelBusiness::getSubsys_no, entity.getSubsys_no())
                .eq(AgreeModelBusiness::getBusin_type, entity.getBusin_type())
                .eq(AgreeModelBusiness::getOrgan_flag, entity.getOrgan_flag())
                .list().stream().collect(Collectors.groupingBy(x -> x.getElecagreemodel_id()));
        ArrayList<AgreeModelBusiness> agreemodelbusinessList = new ArrayList<>();
        entity.getAgreementRelList().forEach(agreementRel -> {
            AgreeModelBusiness agreemodelbusiness = BaseBeanUtil.copyProperties(entity, AgreeModelBusiness.class);
            fillAgreemodelbusiness(baseUser, agreementRel, agreemodelbusiness);
            List<AgreeModelBusiness> checkList = map.get(agreemodelbusiness.getElecagreemodel_id());
            if (CollectionUtils.isNotEmpty(checkList)) {
                for (AgreeModelBusiness check : checkList) {
                    if (StringUtils.equals(check.getRegular_expre(), agreemodelbusiness.getRegular_expre())) {
                        throw new BizException(ErrorCode.ERR_PARAM_IN_ERROR, "协议" + agreemodelbusiness.getElecagreemodel_id() + "规则重复");
                    }
                }
            } else {
                checkList = new ArrayList<>();
                map.put(agreemodelbusiness.getElecagreemodel_id(), checkList);
            }
            checkList.add(agreemodelbusiness);
            agreemodelbusinessList.add(agreemodelbusiness);
        });
        return agreemodelbusinessList;
    }

    private List<AgreeModelBusiness> dtoToList(BaseUser baseUser, AgreemodelbusinessDto entity, List<String> idList) {
        if (CollectionUtils.isEmpty(entity.getAgreementRelList())) {
            throw new BizException(ErrorCode.ERR_PARAM_IN_ERROR, "至少需要选择一个协议");
        }
        ArrayList<AgreeModelBusiness> agreemodelbusinessList = new ArrayList<>();
        entity.getAgreementRelList().forEach(agreementRel -> {
            AgreeModelBusiness agreemodelbusiness = BaseBeanUtil.copyProperties(entity, AgreeModelBusiness.class);
            fillAgreemodelbusiness(baseUser, agreementRel, agreemodelbusiness);
            agreemodelbusinessList.add(agreemodelbusiness);
            if (StringUtils.isNotBlank(agreementRel.getSerial_id())) {
                idList.add(agreementRel.getSerial_id());
            }
        });
        return agreemodelbusinessList;
    }

    private void fillAgreemodelbusiness(BaseUser baseUser, AgreementRel agreementRel, AgreeModelBusiness agreemodelbusiness) {
        agreemodelbusiness.setAgreement_type(agreementRel.getAgreement_type());
        agreemodelbusiness.setAgreement_read_type(agreementRel.getAgreement_read_type());
        agreemodelbusiness.setElecagreemodel_id(agreementRel.getElecagreemodel_id());
        agreemodelbusiness.setForce_read_time(agreementRel.getForce_read_time());
        if (StringUtils.isNotBlank(agreementRel.getSerial_id())) {
            agreemodelbusiness.setSerial_id(agreementRel.getSerial_id());
        }
        agreemodelbusiness.setRegular_expre(agreementRel.getRegular_expre());
        agreemodelbusiness.setStatus(agreementRel.getStatus());
        if (StringUtils.isBlank(agreemodelbusiness.getSerial_id())) {
            agreemodelbusiness.setCreate_by(baseUser.getStaff_no());
            agreemodelbusiness.setStatus(Optional.ofNullable(agreemodelbusiness.getStatus()).orElse(Constant.COMMON_VALID_STATUS));
        }
        agreemodelbusiness.setModify_by(baseUser.getStaff_no());
        agreemodelbusiness.setOrder_no(agreementRel.getOrder_no());
    }

    public boolean updateById(AgreeModelBusiness entity) {
        super.updateById(entity);
        JourUtil.writeJour(() -> saveJour(entity, true, Constant.BUSINESS_FLAG_MOD));
        return true;
    }

}
