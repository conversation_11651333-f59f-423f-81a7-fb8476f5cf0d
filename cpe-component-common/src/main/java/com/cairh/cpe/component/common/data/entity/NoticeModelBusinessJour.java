package com.cairh.cpe.component.common.data.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 消息模板业务表
 *
 * <AUTHOR>
 * @since 2022-05-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("Noticemodelbusinessjour")
public class NoticeModelBusinessJour extends Model<NoticeModelBusinessJour> {

    private static final long serialVersionUID = 3370887893657899776L;

    /**
     * ID
     */
    @TableId(value = "serial_id",type = IdType.ASSIGN_UUID)
    private String serial_id;

    /**
     * 规则表id
     */
    @TableField("noticemodelbusiness_id")
    private String noticemodelbusiness_id;

    /**
     * 模板编号
     */
    @TableField("noticemodel_id")
    private String noticemodel_id;

    /**
     * 子系统编号
     */
    @TableField("subsys_no")
    private Integer subsys_no;

    /**
     * 业务编号
     */
    @TableField("busin_type")
    private Integer busin_type;
    /**
     * 发送方式
     */
    @TableField("send_type")
    private String send_type;
    /**
     * 机构标识
     */
    @TableField("organ_flag")
    private String organ_flag;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String create_by;

    /**
     * 创建日期时间
     */
    @TableField(value = "create_datetime")
    private Date create_datetime;

    /**
     * 修改人
     */
    @TableField("modify_by")
    private String modify_by;

    /**
     * 修改时间
     */
    @TableField(value = "modify_datetime")
    private Date modify_datetime;

    /**
     * 状态
     */
    @TableField(value = "status")
    private String status;

    /**
     * 规则表达式
     */
    @TableField("regular_expre")
    private String regular_expre;

    @TableField(value = "business_flag")
    private Integer business_flag;


    @Override
    public Serializable pkVal() {
        return this.serial_id;
    }

}
