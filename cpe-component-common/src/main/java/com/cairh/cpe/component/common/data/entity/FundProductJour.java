package com.cairh.cpe.component.common.data.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@TableName("FUNDPRODUCTJOUR")
public class FundProductJour extends Model<FundProductJour> {

    private static final long serialVersionUID = 8052319049575930628L;

    /**
     * 序列号
     */
    @TableId(value = "serial_id",type = IdType.ASSIGN_UUID)
    private String serial_id;

    /**
     * 基金产品代码
     */
    @TableField("prod_code")
    private String prod_code;

    /**
     * 基金产品简称
     */
    @TableField("product_short_name")
    private String product_short_name;

    /**
     * 基金产品全称
     */
    @TableField("product_full_name")
    private String product_full_name;

    /**
     * 基金产品类别
     */
    @TableField("fund_product_type")
    private String fund_product_type;

    /**
     * 基金公司代码
     */
    @TableField("company_code")
    private String company_code;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String create_by;

    /**
     * 创建日期时间
     */
    @TableField(value = "create_datetime", fill = FieldFill.INSERT)
    private Date create_datetime;

    /**
     * 修改人
     */
    @TableField("modify_by")
    private String modify_by;

    /**
     * 修改时间
     */
    @TableField(value = "modify_datetime", fill = FieldFill.INSERT_UPDATE)
    private Date modify_datetime;

    /**
     * 操作标志
     */
    @TableField(value = "business_flag")
    private Integer business_flag;

}

