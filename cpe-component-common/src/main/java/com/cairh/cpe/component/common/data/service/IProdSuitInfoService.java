package com.cairh.cpe.component.common.data.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cairh.cpe.component.common.data.entity.ProdSuitInfo;
import com.cairh.cpe.component.common.form.request.ProdsuitinfoAndRule;

/**
 * <p>
 * 产品适当性信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-23
 */
public interface IProdSuitInfoService extends IService<ProdSuitInfo> {

    /**
     * 保存产品适当性信息 以及规则
     * @param entity
     */
    void baseSave(ProdsuitinfoAndRule entity);

    /**
     * 编辑适当性以及规则
     * @param entity
     */
    void baseUpdate(ProdsuitinfoAndRule entity);

    /**
     * 删除适当性和规则
     * @param serial_id
     */
    void baseDelete(String serial_id);
}
