package com.cairh.cpe.component.common.form.support;

import com.cairh.cpe.component.common.data.entity.ExamQuestion;
import com.cairh.cpe.component.common.data.entity.ExamQuestionOptions;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-07-04
 */
@Data
public class QuestionAndOptions {

    /**
     * 题目
     */
    private List<ExamQuestion> examquestionList = new ArrayList<>();

    /**
     * 选项
     */
    private List<ExamQuestionOptions> examquestionoptionsList = new ArrayList<>();

    /**
     * 总分
     */
    private BigDecimal totalSorce = new BigDecimal("0");
}