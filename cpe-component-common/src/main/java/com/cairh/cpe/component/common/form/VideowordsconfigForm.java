package com.cairh.cpe.component.common.form;

import com.cairh.cpe.component.common.data.entity.VideoWordsConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2022-06-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ToString(callSuper = true)
public class VideowordsconfigForm extends BasePage<VideoWordsConfig> {

    /**
     * id
     */
    private String serial_id;

    /**
     * 业务类型
     */
    private String busin_type;

    /**
     * 预览欢迎话术
     */
    private String welcome_content;

    /**
     * 预览视频话术
     */
    private String video_content;

    /**
     * 预览消息话术
     */
    private String msg_content;

    /**
     * 机构标志
     */
    private String organ_flag;
}