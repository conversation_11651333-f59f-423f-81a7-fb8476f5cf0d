package com.cairh.cpe.component.common.data.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cairh.cpe.component.common.data.entity.ExamQuestion;
import com.cairh.cpe.component.common.form.ExamquestionDto;
import com.cairh.cpe.context.BaseUser;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 问卷试题 服务类
 * </p>
 */
public interface IExamQuestionService extends IService<ExamQuestion> {

    /**
     * 新增问卷试题
     *
     * @param entity
     */
    void baseSave(BaseUser baseUser, ExamquestionDto entity);

    /**
     * 更新问卷试题
     *
     * @param baseUser
     * @param entity
     */
    void baseUpdate(BaseUser baseUser, ExamquestionDto entity);

    /**
     * 删除问卷试题
     *
     * @param entity
     */
    void baseDelete(BaseUser baseUser, ExamQuestion entity);

    /**
     * 查询试题分组
     */
    Map<String, Object> baseQueryGourp(BaseUser baseUser, ExamQuestion entity);

    /**
     * 查询分组下的试题
     */
    Map<String,List<ExamQuestion>> baseQueryQuestions(BaseUser baseUser, ExamQuestion entity);

}