package com.cairh.cpe.component.common.constant;

/**
 * 电子协议平台错误号常量类
 * 号段(160000-169999)
 * <AUTHOR> 2018-05-21
 */
public class AgreementStdErrorNo {

	// -----------------------电子协议模板表 （160000-160099）-----------------------
	/**
	 * 电子协议模板表为空
	 */
	public static final String QUERY_ELECAGREEMODEL_NO_DATA = "160000";
	
	/**
	 * agreement_no,agreement_type,busin_type,agreements 参数不能同时为空!
	 */
	public static final String NOTALL_BLANK = "160001";
	/**
	 * 不能同时传agreements和agreement_no
	 */
	public static final String NOT_PASS_PARAM = "160002";
	
	/**
	 * user_id , client_id , fund_account , agreementsign_id , mobile_tel 参数不能同时为空!
	 */
	public static final String NOTALL_BLANK_1 = "160003";
	/**
	 * 模板内容文件格式不对
	 */
	public static final String MODEL_FILE_TYPE_WRONG = "160004";
	/**
	 * 模板内容为空
	 */
	public static final String MODEL_CONTENT_BLANK = "160005";
	/**
	 * 不支持的协议类型
	 */
	public static final String NOT_SUPPORT_AGREEMENT_TYPE = "160006";
	/**
	 * 不存在已发布的电子协议模板
	 */
	public static final String ERROR_ELECAGREEMODEL_RELEASE_DATA_NULL = "160007";
	/**
	 * 签字位置信息为空
	 */
	public static final String ERROR_ELECAGREEMODEL_SIGN_POS_NULL = "160008";
	/**
	 * 解析json串失败
	 */
	public static final String ERROR_PARSE_JSON = "160009";
	/**
	 * 第三方协议类型为空
	 */
	public static final String ERROR_THIRD_AGREEMENT_TYPE = "160010";
	/**
	 * 获取顶点第三方协议ID失败
	 */
	public static final String ERROR_THIRD_AGREEMENT_ID = "160011";
	/**
	 * 解析日期时间失败
	 */
	public static final String ERROR_PARSE_DATETIME= "160012";

	// -----------------------电子协议模板签署表（160100-160199） -----------------------
	/**
	 * 协议签署表记录查询为空
	 */
	public static final String QUERY_ELECAGREEMENTSIGN_NO_DATA = "160100";
	/**
	 * 客户已经签署协议，不能再次签署
	 */
	public static final String SIGN_ELECAGREEMENTSIGN_NOT_AGAIN = "160101";
	/**
	 * 传入的产品代码错误
	 */
	public static final String PASS_PRO_CODE_WRONG= "160102";
	/**
	 * 批量签署协议失败
	 */
	public static final String BATCH_SIGN_FAIL= "160103";
	/**
	 * 插入流水失败
	 */
	public static final String INSET_JOUR_FAIL= "160104";
	/**
	 * 获取协议签署多线程返回结果失败
	 */
	public static final String ERROR_THREAD_RESULT= "160105";

	// -----------------------电子协议模板库（160200-160299） -----------------------
	/**
	 * 文档编号不能为空，不允许删除
	 */
	public static final String QUERY_AGREETEMPLATE_NO_DOCUMENT_NO = "160200";
	/**
	 * 不存在模板库
	 */
	public static final String QUERY_AGREETEMPLATE_NO_DATA = "160201";
	/**
	 * 文档正在被使用
	 */
	public static final String QUERY_AGREETEMPLATE_IS_USING = "160202";
	/**
	 * 删除模板库失败
	 */
	public static final String DEL_AGREETEMPLATE_FAILLE = "160203";
	/**
	 * 参数不合法
	 */
	public static final String AGREETEMPLATE_PARAM_WRONGFUL = "160204";
	/**
	 * 文档编号不能为空
	 */
	public static final String AGREETEMPLATE_NO_DOCUMENT_NO = "160205";
	/**
	 * 流水编号不能为空
	 */
	public static final String AGREETEMPLATE_NO_SERIAL_NO = "160206";
	/**
	 * 查看文档内容失败
	 */
	public static final String VIEW_AGREETEMPLATE_FAIL = "160207";
	/**
	 * 重新发布协议失败：有人正在修改此文档
	 */
	public static final String PUBLISH_AGREETEMPLATE_FAIL = "160208";
	/**
	 * 文档没有协议使用
	 */
	public static final String AGREETEMPLATE_NO_USING = "160209";

	/**
	 * 协议签署文件加密失败
	 */
	public static final String ERROR_SIGNATURE_DATA = "160210";
	/**
	 * 协议签署失败
	 */
	public static final String ERROR_SIGN_DATA = "160211";
	/**
	 * 新意接口调用失败
	 */
	public static final String SHINE_API_ERROR="160212";
	/**
	 * 新意app_id配置错误
	 */
	public static final String SHINE_CONFIG_ERROR="160213";

	// -----------------------电子协议其它异常（160300-160399）-----------------------
	/**
	 * 文件转换并上传ftp失败
	 */
	public static final String UPLOAD_FTP_FAIL = "160300";
	/**
	 * 请选择文档或者填写正文内容
	 */
	public static final String NEED_AGREEMENT_CONTENT = "160301";
	/**
	 * 选择的文档不存在
	 */
	public static final String NOT_EXIST_DOCUMENT = "160302";
	/**
	 * 复制文件失败
	 */
	public static final String COPY_FILE_FAIL = "160303";
	/**
	 * 转换文件MD5失败
	 */
	public static final String TURN_MD5_FAIL = "160304";
	/**
	 * url不能为空
	 */
	public static final String NO_URL = "160305";
	/**
	 * 该用户无证书安装记录，无法做验签功能
	 */
	public static final String NO_CERT_JOUR = "160306";
	/**
	 * 用户未安装证书
	 */
	public static final String NO_CERT = "160307";
	/**
	 * 生成pdf文件异常
	 */
	public static final String TURN_PDF_FAIL = "160308";
	/**
	 * 服务器文件不存在
	 */
	public static final String FILE_NOT_EXIST = "160309";
	/**
	 * json格式不正确
	 */
	public static final String REPLACE_STR_FORMAT_WRONG = "160310";
	/**
	 * 解析json失败analysis
	 */
	public static final String ANALYSIS_JSON_FAIL = "160311";
	/**
	 * copy bean失败
	 */
	public static final String COPY_BEAN_FAIL = "160312";
	/**
	 * 该任务已经被处理
	 */
	public static final String TASK_HAVE_SOLVING = "160313";
	/**
	 * 证书颁发审核失败
	 */
	public static final String CERT_ISSUES_AUDIT_FAIL = "160314";
	/**
	 * 用户信息不存在
	 */
	public static final String USERS_INFO_NOT_EXIST = "160315";
	/**
	 * 后台还未颁发证书，暂无法申请，请稍后再试
	 */
	public static final String CERT_NO_PASS_ERROR = "160316";
	/**
	 * CA证书保存失败
	 */
	public static final String ERROR_SAVE_CA_CERT = "160317";
	/**
	 * 无CA证书
	 */
	public static final String ERROR_NO_CA_CERT = "160318";
	/**
	 * 系统繁忙,请稍后再试
	 */
	public static final String REQUEST_FAIL = "160319";
	/**
	 * 还未申请中登证书
	 */
	public static final String CERT_NO_CA_CERT_ERROR = "160320";
	/**
	 * userCaVerify数据不存在
	 */
	public static final String NO_USERCAVERIFY = "160321";
	/**
	 * 您有正在处理的批量导入任务，请稍后
	 */
	public static final String WAITING_BATCHTASK = "160322";
	/**
	 * 保存文件到本地失败
	 */
	public static final String SAVE_FILE_FAIL = "160323";
	/**
	 * 上传档案系统失败
	 */
	public static final String UPLOAD_ARCH_FAIL = "160324";
	/**
	 * 生成加密包路径失败
	 */
	public static final String CRYPTO_PATH_FAIL = "160325";
	/**
	 * 调用北京CA签署失败
	 */
	public static final String USE_BJCA_SIGN_FAIL = "160326";
	/**
	 * 解码base64失败
	 */
	public static final String DECODE_BASE64_FAIL = "160327";
	/**
	 * prod_code和agreement_type不能同时为空
	 */
	public static final String PROCODE_AGREEMENTTYPE_NOTALLBLANK = "160328";
	/**
	 * user_id , client_id , fund_account , agreementsign_id , mobile_tel 参数不能同时为空!
	 */
	public static final String CLIENT_SIGNID_NOTALLBLANK = "160329";
	/**
	 * 签署信息异常
	 */
	public static final String SIGN_MESSIAGE_ERROR = "160330";
	/**
	 * 下载pdf异常
	 */
	public static final String DOWN_PDF_ERROR = "160331";
	/**
	 * 必传参数不合法，请检查必传参数传递情况
	 */
	public static final String PARAMS_ERROR = "160332";
	/**
	 * 验证异常
	 */
	public static final String PROVING_ERROR = "160333";
	/**
	 * agreement_type不合法
	 */
	public static final String AGREEMENTTYPE_ERROR = "160334";
	/**
	 * busin_type不合法
	 */
	public static final String BUSINTYPE_ERROR = "160335";
	/**
	 * agreement_no不合法
	 */
	public static final String AGREEMENTNO_ERROR = "160336";
	/**
	 * agreement_version不合法
	 */
	public static final String AGREEMENTVERSION_ERROR = "160337";
	/**
	 * 不支持的编码方式
	 */
	public static final String NOT_SUPPORT_CODE_ERROR = "160338";
	/**
	 * 创建签名后 PDF文件异常
	 */
	public static final String CREATE_SIGN_PDF_ERROR = "160339";
	
	/**
	 * 同步柜台协议签署流水失败
	 */
	public static final String ERROR_CALL_T2_AGREEMENT_JOUR = "160340";
	/**
	 * url错误
	 */
	public static final String ERROR_URL_WRONG = "160341";
	/**
	 * pdf协议下载出错
	 */
	public static final String ERROR_FILEPDF_LOAD_WEONG = "160342";
	/**
	 * 入参user_id与agreementsign_id对应的记录的user_id不匹配
	 */
	public static final String USERID_ERROR = "160343";
	/**
	 * 获取图片序列失败
	 */
	public static final String ERROR_GET_SEQ = "160344";
	
	/**
	 * pdf转图片保存数据库失败
	 */
	public static final String ERROR_SAVE_PDF_TO_PIC = "160345";
	/**
	 * pdf转图片失败
	 */
	public static final String ERROR_CONVERT_PDF_TO_PIC = "160346";
	/**
	 * 查询重签标识失败
	 */
	public static final String ERROR_QUERY_RESGIN_FLAG = "160347";

	/**
	 * 协议模板签名区域格式转换失败
	 */
	public static final String ERROR_AGREEMENT_SIGNPPOSJSON_TRANS = "160348";
	
	/**
	 * word格式设置失败
	 */
	public static final String ERROR_SET_WORD_FORMAT = "160349";
	/**
	 * MD5码不匹配
	 */
	public static final String ERROR_MD5_NO_MATCH = "160350";
	
	/**
	 * 文本格式设置失败
	 */
	public static final String ERROR_SET_TXT_FORMAT = "160351";
	
	/**
	 * 服务器文件记录数超过最大限制
	 */
	public static final String FILE_OVER_MAX_VALUE = "160352";
	
	/**
	 * 无法获取邮箱地址，资产账号传入为空
	 */
	public static final String ERROR_FUND_ACCOUNT_BLANK = "160353";
	
	/**
	 * PdF文件增加签字图片时没有查询到关键字
	 */
	public static final String ERROR_PDF_ADDSIGNIMAGE_NOEXITS_QRYKEY = "160354";
	
	/**
	 * PdF文件增加签字图片失败
	 */
	public static final String ERROR_PDF_ADDSIGNIMAGE_FAILD = "160355";
	
	/**
	 * PdF文件增加签字图片异常
	 */
	public static final String ERROR_PDF_ADDSIGNIMAGE_EXP = "160356";
	
	/**
	 * 证书已过期
	 */
	public static final String ERROR_EXPIRE_DATE = "160357";
	
	/**
	 * 协议签署记录不存在
	 */
	public static final String ERROR_SIGN_NOT_EXIST = "160358";
	
	/**
	 * 合同ID不存在
	 */
	public static final String ERROR_CONTRACTID_NOT_EXIST = "160359";
	
	/**
	 * 调用第三方厂商天威获取天威合同图片失败
	 */
	public static final String ERROR_GET_ITRUS_PIC = "160360";
	
	/**
	 * 调用第三方厂商天威创建天威合同失败
	 */
	public static final String ERROR_CREATE_CONTRACT = "160361";
	
	/**
	 * 调用第三方厂商天威创建天威用户失败
	 */
	public static final String ERROR_CREATE_USER = "160362";
	
	/**
	 * 调用第三方厂商天威签署协议失败
	 */
	public static final String ERROR_SIGN_CONTRACT = "160363";
	
	/**
	 * 调用第三方厂商天威查询签署协议失败
	 */
	public static final String ERROR_QUERY_SIGN_CONTRACT = "160364";
	
	/**
	 * 档案文件不存在
	 */
	public static final String ERROR_ARCHFILEINFO_NOT_EXIST = "160365";

	/**
	 * 档案袋不存在
	 */
	public static final String ARCHBAG_NOT_EXIST = "160366";

	/**
	 * 档案袋已归档
	 */
	public static final String ARCHBAG_HAVE_FINISH = "160367";

	/**
	 * 档案文件配置不存在
	 */
	public static final String ARCHFILECONFIG_NOT_EXIST = "160368";

	/**
	 * 调用档案服务异常
	 */
	public static final String ERROR_ARCH_SERIVCE = "160369";

    /**
     * 此类型的印章已存在
     */
    public static final String ERROR_IS_EXIST_SEALINFO = "160370";

	/**
	 * 同步个人用户失败
	 */
	public static final String ERROR_ADD_PERSONA_LUSER = "160371";

	/**
	 * 获取协议模版失败
	 */
	public static final String ERROR_GET_PROTOCOL = "160372";

	/**
	 * 创建合同失败
	 */
	public static final String ERROR_CREATE_ZY_CONTRACT = "160373";

	/**
	 * 同步签署合同失败
	 */
	public static final String ERROR_DIRECT_SIGN = "160374";

	/**
	 * 业务类型与产品代码不能同时为空
	 */
	public static final String ERROR_PARAM_BLANK = "160375";

	/**
	 * 解析数字失败
	 */
	public static final String ERROR_PARSE_INT = "160376";

	public static final String ERROR_CONTRACT_PDF = "160377";
	
	// -----------------------电子协议CA证书第三方厂商异常（160400-160499）-----------------------
	/**
	 * 第三方厂商中登直连CA证书服务异常
	 */
	public static final String ERROR_CSDC_CA_SERIVCE = "160400";
	/**
	 * 第三方厂商中登直连CA证书服务返回结果错误
	 */
	public static final String ERROR_CSDC_CA_RETURN_RESULT = "160401";
	/**
	 * 第三方厂商天威代理中登CA证书服务异常
	 */
	public static final String ERROR_ITURS_CA_SERIVCE = "160402";
	/**
	 * 第三方厂商同花顺代理中登申请CA证书服务异常
	 */
	public static final String ERROR_JQKA_CA_APPLY_SERIVCE = "160403";
	/**
	 * 第三方厂商同花顺代理中登更新CA证书服务异常
	 */
	public static final String ERROR_JQKA_CA_UPDATE_SERIVCE = "160404";
	/**
	 * 第三方厂商同花顺代理中登下载CA证书服务异常
	 */
	public static final String ERROR_JQKA_CA_DOWNLOAD_SERIVCE = "160405";
	/**
	 * 第三方厂商同花顺代理中登申请CA证书服务返回结果异常
	 */
	public static final String ERROR_JQKA_CA_APPLY_RESLUT = "160406";
	/**
	 * 第三方厂商同花顺代理中登更新CA证书服务返回结果异常
	 */
	public static final String ERROR_JQKA_CA_UPDATE_RESLUT = "160407";
	/**
	 * 第三方厂商同花顺代理中登下载CA证书服务返回结果异常
	 */
	public static final String ERROR_JQKA_CA_DOWNLOAD_RESLUT = "160408";
	
	/**
	 * 第三方厂商恒生统一网关代理中登申请CA证书服务异常
	 */
	public static final String ERROR_ASSET_CA_APPLY_SERIVCE = "160409";
	/**
	 * 第三方厂商恒生统一网关代理中登更新CA证书服务异常
	 */
	public static final String ERROR_ASSET_CA_UPDATE_SERIVCE = "160410";
	/**
	 * 第三方厂商恒生统一网关代理中登下载CA证书服务异常
	 */
	public static final String ERROR_ASSET_CA_DOWNLOAD_SERIVCE = "160411";
	/**
	 * 第三方厂商恒生统一网关代理中登申请CA证书服务返回结果异常
	 */
	public static final String ERROR_ASSET_CA_APPLY_RESLUT = "160412";
	/**
	 * 第三方厂商同恒生统一网关代理中登更新CA证书服务返回结果异常
	 */
	public static final String ERROR_ASSET_CA_UPDATE_RESLUT = "160413";
	/**
	 * 第三方厂商恒生统一网关代理中登下载CA证书服务返回结果异常
	 */
	public static final String ERROR_ASSET_CA_DOWNLOAD_RESLUT = "160414";
	/**
	 * 第三方厂商恒生统一网关代理中登查询CA证书服务异常
	 */
	public static final String ERROR_ASSET_CA_QUERY_SERIVCE = "160415";
	/**
	 * 第三方厂商恒生统一网关代理中登查询CA证书服务返回结果异常
	 */
	public static final String ERROR_ASSET_CA_QUERY_RESLUT = "160416";
	
	
	/**
	 * 第三方厂商天威自建证书服务异常
	 */
	public static final String ERROR_ITURS_CA_SELF_SERIVCE = "160417";
	
	/**
	 * 第三方厂商同花顺自建证书申请服务异常
	 */
	public static final String ERROR_JQKA_CA_SELF_APPLY_SERIVCE = "160418";
	/**
	 * 第三方厂商同花顺自建证书更新服务异常
	 */
	public static final String ERROR_JQKA_CA_SELF_UPDATE_SERIVCE = "160419";
	/**
	 * 第三方厂商同花顺自建证书下载服务异常
	 */
	public static final String ERROR_JQKA_CA_SELF_DOWNLOAD_SERIVCE = "160420";
	/**
	 * 第三方厂商同花顺自建证书申请服务返回结果异常
	 */
	public static final String ERROR_JQKA_CA_SELF_APPLY_RESLUT = "160421";
	/**
	 * 第三方厂商同花顺自建证书更新服务返回结果异常
	 */
	public static final String ERROR_JQKA_CA_SELF_UPDATE_RESLUT = "160422";
	/**
	 * 第三方厂商同花顺自建证书下载服务返回结果异常
	 */
	public static final String ERROR_JQKA_CA_SELF_DOWNLOAD_RESLUT = "160423";
	/**
	 * 第三方厂商河南自建证书服务异常
	 */
	public static final String ERROR_HENAN_CA_SELF_SERIVCE = "160424";
	
	/**
	 * 第三方厂商山东自建证书申请服务异常
	 */
	public static final String ERROR_SHANDONG_CA_SELF_APPLY_SERIVCE = "160425";
	/**
	 * 第三方厂商山东自建证书更新服务异常
	 */
	public static final String ERROR_SHANDONG_CA_SELF_UPDATE_SERIVCE = "160426";

	/**
	 * 第三方厂商山东自建证书申请服务返回结果异常
	 */
	public static final String ERROR_SHANDONG_CA_SELF_APPLY_RESLUT = "160427";
	/**
	 * 第三方厂商山东自建证书更新服务返回结果异常
	 */
	public static final String ERROR_SHANDONG_CA_SELF_UPDATE_RESLUT = "160428";

	/**
	 * 第三方厂商山东自建证书替换服务异常
	 */
	public static final String ERROR_SHANDONG_CA_SELF_REPLACE_SERIVCE = "160429";
	/**
	 * 第三方厂商山东自建证书作废服务异常
	 */
	public static final String ERROR_SHANDONG_CA_SELF_CANCEL_SERIVCE = "160430";
	/**
	 * 第三方厂商山东自建证书解冻服务异常
	 */
	public static final String ERROR_SHANDONG_CA_SELF_RECOVER_SERIVCE = "160431";
	/**
	 * 第三方厂商山东自建证书冻结服务异常
	 */
	public static final String ERROR_SHANDONG_CA_SELF_FREEZE_SERIVCE = "160432";
	/**
	 * 第三方厂商山东自建证书通信服务链路异常
	 */
	public static final String ERROR_SHANDONG_CA_SELF_NETWORK_CONNECT_FAIL = "160433";
	
	/**
	 * 第三方厂商北京自建证书服务异常
	 */
	public static final String ERROR_BEIJING_CA_SELF_SERIVCE = "160434";

	/**
	 * 第三方厂商北京自建证书V2服务异常
	 */
	public static final String ERROR_BEIJING_V2_CA_SELF_SERIVCE = "160435";
	/**
	 * 第三方厂商北京自建证书V2下载证书服务异常
	 */
	public static final String ERROR_BEIJING_V2_DOWNLOAD_CA_SELF_SERIVCE = "160436";
	/**
	 * 第三方厂商信安世纪自建证书服务异常
	 */
	public static final String ERROR_INFOSEC_DOWNLOAD_CA_SELF_SERIVCE = "160437";
	
	/**
	 * 第三方厂商天威云证书服务异常
	 */
	public static final String ERROR_ITRUST_SERIVCE = "160438";

	/**
	 * 第三方厂商信安世纪加密服务异常
	 */
	public static final String ERROR_INFOSEC_DETACHED_SIGNATURE = "160439";

	/**
	 * 第三方厂商netsignagent.properties配置文件不存在
	 */
	public static final String ERROR_PROPERTIES_NOT_EXIST = "160440";
	
	
	// -----------------------对接新意接口错误号（160500-160599） -----------------------
	
	/**
	 * 挑战码申请失败
	 */
	public static final String ERROR_SHINE_REQ_CHALLENGECODE = "160500";
	
	/**
	 * 挑战码或无挑战码签名失败
	 */
	public static final String ERROR_SHINE_ORDER_NETDETAIL = "160501";
	
	/**
	 * 互联网协议签署校验失败
	 */
	public static final String ERROR_SHINE_CHECK_PROTOCOL_TASK_STATUS = "160502";
	
	/**
	 * 协议模板信息查询失败
	 */
	public static final String ERROR_SHINE_QUERY_PTOLIST = "160503";
	
	/**
	 * 空白协议模版下载失败
	 */
	public static final String ERROR_SHINE_GET_PDFSTREAM = "160504";
	
	/**
	 * 调用第三方厂商新意电子协议接口服务异常
	 */
	public static final String ERROR_SHINE_SERIVCE = "160505";
	
	/**
	 * 协议模板套打未完成
	 */
	public static final String ERROR_SHINE_REPLACE_UNDO = "160506";
	
	/**
	 * 调用第三方厂商CFCA接口生成签名pdf文件失败
	 */
	public static final String CFCA_PDF_FAIL = "160507";
	
	/**
	 * 第三方厂商天威云协议签署失败
	 */
	public static final String ITRUS_SIGN_FAIL = "160508";
	/**
	 * 第三方厂商国金协议签署失败
	 */
	public static final String ERROR_GJ_SIGN = "160509";
	/**
	 * 坐标签章文件下载失败
	 */
	public static final String ERROR_SHINE_GET_COORDINATESEAL = "160510";
	/**
	 * 妥妥递产品号为空
	 */
	public static final String PRODUCT_SERIAL_IS_NULL = "160511";
	/**
	 * 妥妥递模板下载失败
	 */
	public static final String PRODUCT_MODEL_DOWNLOAD_FAILURE = "160512";
	
}
