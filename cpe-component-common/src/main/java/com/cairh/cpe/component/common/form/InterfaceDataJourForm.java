package com.cairh.cpe.component.common.form;


import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.cairh.cpe.component.common.data.entity.AgreeModelBusiness;
import com.cairh.cpe.component.common.data.entity.InterfaceDataJour;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.ArrayList;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper=false)
@ToString(callSuper = true)
public class InterfaceDataJourForm extends BasePage<InterfaceDataJour> {
    {
        /*OrderItem orderItem = new OrderItem();
        orderItem.setAsc(false);
        orderItem.setColumn("jour_initdate");
        ArrayList<OrderItem> orderItems = new ArrayList<>();
        orderItems.add(orderItem);

        super.setOrders(orderItems);*/


        OrderItem orderItem1 = new OrderItem();
        OrderItem orderItem2 = new OrderItem();
        orderItem1.setAsc(true);
        orderItem2.setAsc(false);
        orderItem1.setColumn("jour_kind");
        orderItem2.setColumn("jour_initdate");
        ArrayList<OrderItem> orderItems = new ArrayList<>();
        orderItems.add(orderItem1);
        orderItems.add(orderItem2);
        super.setOrders(orderItems);
    }

    /**
     * 开始日期
     */
    private Integer begin_date;

    /**
     * 结束日期
     */
    private Integer end_date;

}
