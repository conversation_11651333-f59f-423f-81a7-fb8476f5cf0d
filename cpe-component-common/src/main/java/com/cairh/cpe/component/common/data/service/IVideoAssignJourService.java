package com.cairh.cpe.component.common.data.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cairh.cpe.component.common.data.entity.VideoAssignJour;
import com.cairh.cpe.component.common.form.VideoAssignJourForm;
import com.cairh.cpe.component.common.model.VideoAssignJourEntity;
import com.cairh.cpe.component.common.model.VideoUserEntity;

/**
 * 功能说明: 视频派单流水
 * 公司名称: 杭州财人汇网络股份有限公司
 * 开发人员: <EMAIL>
 * 开发时间: 2023-12-28 14:23
 */
public interface IVideoAssignJourService extends IService<VideoAssignJour> {

    Page<VideoAssignJourEntity> queryByPage(VideoAssignJourForm form);
}
