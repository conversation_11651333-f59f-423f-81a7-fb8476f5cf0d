package com.cairh.cpe.component.common.form;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cairh.cpe.component.common.data.entity.VideoAssignJour;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.Date;

/**
 * 功能说明: 视频派单流水
 * 公司名称: 杭州财人汇网络股份有限公司
 * 开发人员: <EMAIL>
 * 开发时间: 2023-12-28 14:38
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class VideoAssignJourForm extends Page<VideoAssignJour> {

    {
        OrderItem orderItem1 = new OrderItem();
        OrderItem orderItem2 = new OrderItem();
        orderItem1.setAsc(false);
        orderItem2.setAsc(false);
        orderItem1.setColumn("curr_datetime");
        orderItem2.setColumn("serial_id");
        ArrayList<OrderItem> orderItems = new ArrayList<>();
        orderItems.add(orderItem1);
        orderItems.add(orderItem2);
        super.setOrders(orderItems);
    }
    /**
     * 主键
     */
    private String serial_id;

    /**
     * 员工号
     */
    private String staff_no;

    /**
     * 员工姓名
     */
    private String staff_name;

    /**
     * 手机号（客户）
     */
    private String mobile_tel;

    /**
     * 证件号码（客户）
     */
    private String id_no;

    /**
     * 证件类型（客户）
     */
    private String id_kind;
    /**
     * 客户姓名（客户）
     */
    private String full_name;
    /**
     * 操作类型
     */
    private String video_assign_type;
    /**
     * 归历史标记
     */
    private String tohis_flag;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date[] curr_date_range;

}
