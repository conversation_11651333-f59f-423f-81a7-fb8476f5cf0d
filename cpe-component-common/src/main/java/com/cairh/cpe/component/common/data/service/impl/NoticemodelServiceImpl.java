package com.cairh.cpe.component.common.data.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.component.common.utils.BaseBeanUtil;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.context.constant.ErrorCode;
import com.cairh.cpe.component.common.constant.Constant;
import com.cairh.cpe.component.common.data.entity.NoticeModel;
import com.cairh.cpe.component.common.data.entity.NoticeModelJour;
import com.cairh.cpe.component.common.data.mapper.NoticeModelMapper;
import com.cairh.cpe.component.common.data.service.INoticeModelService;
import com.cairh.cpe.component.common.data.service.INoticeModelJourService;
import com.cairh.cpe.component.common.utils.JourUtil;
import lombok.AllArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 消息模板表 服务实现类
 *
 * <AUTHOR>
 * @since 2022-05-07
 */
@Service
@AllArgsConstructor
public class NoticemodelServiceImpl extends ServiceImpl<NoticeModelMapper, NoticeModel> implements INoticeModelService {

    private final INoticeModelJourService noticemodeljourService;

    @Override
    public void baseSave(BaseUser baseUser, NoticeModel entity) {
        if (StringUtils.isNotBlank(entity.getModel_no())) {
            if (CollectionUtils.isNotEmpty(this.lambdaQuery().eq(NoticeModel::getModel_no, entity.getModel_no()).list())) {
                throw new BizException(ErrorCode.ERR_PARAM_IN_ERROR, "该模板编号已存在");
            }
        }
        entity.setCreate_by(baseUser.getStaff_no());
        entity.setModify_by(baseUser.getStaff_no());
        this.save(entity);
    }

    @Override
    public boolean save(NoticeModel entity) {
        super.save(entity);
        JourUtil.writeJour(() -> saveJour(entity,false, Constant.BUSINESS_FLAG_ADD));
        return true;
    }

    private boolean saveJour(NoticeModel entity, boolean isUpdate, Integer business_flag) {
        if (isUpdate){
            entity=getById(entity.getSerial_id());
        }
        NoticeModelJour noticemodeljour = getNoticemodeljour(entity);
        noticemodeljour.setBusiness_flag(business_flag);
        return noticemodeljourService.save(noticemodeljour);
    }

    @Override
    public boolean updateById(NoticeModel entity) {
        super.updateById(entity);
        JourUtil.writeJour(() -> saveJour(entity,true, Constant.BUSINESS_FLAG_MOD));
        return true;
    }

    private NoticeModelJour getNoticemodeljour(NoticeModel entity) {
        NoticeModelJour noticemodeljour = BaseBeanUtil.copyProperties(entity, NoticeModelJour.class);
        noticemodeljour.setSerial_id(null);
        noticemodeljour.setNoticemodel_id(entity.getSerial_id());
        return noticemodeljour;
    }

}
