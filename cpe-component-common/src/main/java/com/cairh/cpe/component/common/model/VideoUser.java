package com.cairh.cpe.component.common.model;

import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.lang.Nullable;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 视频用户
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class VideoUser implements Comparable<VideoUser> {

    public static final String AUTO_ASSIGN_TIME = "autoAssignTime";
    public static final String ASSIGNED_EMP = "assignedEmp";
    public static final String AUTO_ASSIGN_ACCEPT_TIME = "autoAssignAcceptTime";
    public static final String LAST_ASSIGNED_EMP = "last_assigned_emp";
    public static final String STATUS = "status";

    /**
     * 系统编号 Y
     */
    private String subsys_no;

    /**
     * 业务类型 Y
     */
    private String busin_type;

    /**
     * 业务名称 Y
     */
    private String busin_name;

    /**
     * 机构标识 Y
     */
    private String organ_flag;

    /**
     * 客户端类型 Y
     */
    private String app_id;

    /**
     * 客户营业部 Y
     */
    private String branch_no;

    /**
     * 姓名 Y
     */
    private String user_name;

    /**
     * 证件类型 Y
     */
    private String id_kind;

    /**
     * 证件号码 Y
     */
    private String id_no;

    /**
     * 客户号 N
     */
    private String client_id;

    /**
     * 资金账号 N
     */
    private String fund_account;

    /**
     * 手机号 N
     */
    private String mobile_tel;

    /**
     * 渠道号
     */
    private String channel_code;
    /**
     * 渠道名称
     */
    private String channel_name;

    /**
     * 视频类型 Y
     */
    private String video_type;

    /**
     * 视频优先级 N
     */
    private Integer video_level;
    /**
     * 是否设置过最高优先级
     */
    private Integer priority_flag = 0;

    /**
     * 视频厂商 N
     */
    private String service_vender;

    /**
     * 业务流水号(需保证唯一) Y
     */
    private String unique_id;

    //queue score
    private Double score;

    //入队时间
    private long enqueue_time;

    //状态(0:排队中;1:已匹配;2:视频中)
    private String status;

    //最近状态更新时间
    private Long last_status_update_time;

    //所在队列名
    private String queue_name;

    //排队位置(当前队列)
    private Long queue_position;

    //排队总数(排队总数)
    private Long queue_count;

    //操作员编号
    private String operator_no;

    //操作员姓名
    private String operator_name;

    //操作员从业资格证书
    private String profession_cert;

    //员工编号
    private String staff_no;

    //跳转页面
    private String page_addr;

    //--通用配置--

    //房间号
    private String room_id;

    //房间密码
    private String room_pwd;

    //--anychat配置--

    //if anychat customize
    private boolean anychat_customizable = false;

    //视频服务器内网地址
    private String server_addr_in;

    //视频服务器外网地址
    private String server_addr_out;

    //视频服务器端口
    private String server_port;

    //anychat集群标志
    private String anychat_group_flag;

    //anychat集群appguid
    private String anychat_appguid;

    //登录名
    private String login_name;

    //坐席ID
    private String remote_user_id;

    //anychat h5 port
    private String anychat_h5_port;

    //anychat h5 addr
    private String anychat_h5_address;

    //--zego配置--

    //zego user token
    private String zego_user_token;

    //zego operator token
    private String zego_operator_token;

    //zego服务地址
    private String zego_video_address;

    //zego是否使用https
    private boolean zego_use_https;

    //zego appid
    private Integer zego_appid;

    //zego sign_key
    private String zego_signkey;

    //zego env
    private String zego_env;

    //video json param
    private String json_param;

    /**
     * 组件化请求流水id
     */
    private String request_id;

    private long autoAssignAcceptTime;
    private String assignedEmp;
    private long autoAssignTime;
    /**
     * 最近一次分配的坐席
     */
    private String last_assigned_emp;

    /**
     * 通知类型 放在这个地方不合适，后面需要提出来
     */
    private String notice_type;
    /**
     * 存放自定义字段
     */
    private Map<Object, Object> map = new HashMap<>();

    @Override
    public int compareTo(@Nullable VideoUser videoUser) {
        return Objects.nonNull(videoUser) ? Double.compare(score, videoUser.getScore()) : 1;
    }

    /**
     * 补录
     */
    private String additional;
    /**
     * 经纪人编号
     */
    private String broker_code;

    /**
     * 智能派单的任务id
     */
    private String task_id;

    /**
     * 客户性别信息
     */
    private String user_gender;

    /**
     * 操作员所属营业部编号
     */
    private String operator_branch_no;

    /**
     * 操作员所属营业部名称
     */
    private String operator_branch_name;

    /**
     * 网厅业务办理-开户类别
     */
    private String client_category;

    /**
     * 网厅业务办理-经办人姓名
     */
    private String agent_name;

    /**
     * 网厅业务办理-经办人证件类型
     */
    private String agent_id_kind;

    /**
     * 网厅业务办理-经办人证件号码
     */
    private String agent_id_no;
}
