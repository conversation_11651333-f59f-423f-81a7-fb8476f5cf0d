package com.cairh.cpe.component.common.data.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cairh.cpe.component.common.data.entity.FundProduct;
import com.cairh.cpe.context.BaseUser;

public interface IFundProductService extends IService<FundProduct> {

    /**
     * 新增基金产品
     */
    void saveFund(BaseUser baseUser, FundProduct fundProduct);

    /**
     * 修改基金产品
     */
    void updateFund(BaseUser baseUser, FundProduct fundProduct);

    /**
     * 删除基金产品
     */
    void deleteFund(BaseUser baseUser, FundProduct fundProduct);
}
