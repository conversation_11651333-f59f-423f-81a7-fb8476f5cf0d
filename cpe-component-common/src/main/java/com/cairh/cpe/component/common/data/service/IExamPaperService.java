package com.cairh.cpe.component.common.data.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.component.common.data.entity.ExamPaper;
import com.cairh.cpe.component.common.data.entity.ExamQuestion;

/**
 * <p>
 * 问卷 服务类
 * </p>
 *
 */
public interface IExamPaperService extends IService<ExamPaper> {

    /**
     * 保存问卷
     * @param baseUser
     * @param entity
     */
    void baseSave(BaseUser baseUser, ExamPaper entity);

    /**
     * 修改问卷
     * @param baseUser
     * @param entity
     */
    void baseUpdate(BaseUser baseUser, ExamPaper entity);

    /**
     * 删除问卷
     * @param baseUser
     * @param entity
     */
    void baseDelete(BaseUser baseUser, ExamPaper entity);

    /**
     * 复制新版
     * @param baseUser
     * @param entity
     */
    void copyCurrVersion(BaseUser baseUser, ExamPaper entity);

    /**
     * 启用问卷
     * @param baseUser
     * @param entity
     */
    void enable(BaseUser baseUser, ExamPaper entity);

    /**
     * 停用问卷
     * @param baseUser
     * @param entity
     */
    void stop(BaseUser baseUser, ExamPaper entity);

    /**
     * 保存流水
     * @param entity
     * @return
     */
    boolean saveJour(ExamPaper entity, boolean isUpdate, Integer business_flag);

    boolean saveExamquestionjour(ExamQuestion examquestion);
}