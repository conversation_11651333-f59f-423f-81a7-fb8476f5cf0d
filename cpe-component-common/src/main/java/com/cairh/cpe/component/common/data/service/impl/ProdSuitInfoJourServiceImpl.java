package com.cairh.cpe.component.common.data.service.impl;

import com.cairh.cpe.component.common.data.entity.ProdSuitInfoJour;
import com.cairh.cpe.component.common.data.mapper.ProdSuitInfoJourMapper;
import com.cairh.cpe.component.common.data.service.IProdSuitInfoJourService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 产品适当性信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-23
 */
@Service
public class ProdSuitInfoJourServiceImpl extends ServiceImpl<ProdSuitInfoJourMapper, ProdSuitInfoJour> implements IProdSuitInfoJourService {

}
