package com.cairh.cpe.component.common.cache.mq;

import com.cairh.cpe.component.common.cache.CpeCacheManager;
import com.cairh.cpe.component.common.cache.dict.CacheDict;
import com.cairh.cpe.component.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * cpe平台更新cacheType：字典 ：basedictionary 营业部：allbranch 城市：allcitycode 操作员：operatorinfo
 * 系统配置：syspropertyconfig之后进行mq发通知后是否能正常接收，并且刷新
 *
 */
@Slf4j
@Component
public class CpeCacheUpdateMqReceive implements ApplicationListener<ApplicationStartedEvent> {

    @Autowired
    private CacheUpdateMQOperator cacheUpdateBroadcastMQOperator;

    @Autowired
    private CacheDict cacheDict;

    @Autowired
    private CpeCacheManager cpeCacheManager;

    /**
     * push msg if it comes
     */
    @Override
    public void onApplicationEvent(ApplicationStartedEvent event) {
        msgPush();
    }

    public void msgPush() {
        cacheUpdateBroadcastMQOperator.receive(this::doMsgPush);
    }

    /**
     * real msg push
     *
     */
    private void doMsgPush(CacheUpdateMQOperator.SendMsg msg) {
        if (Objects.isNull(msg)) {
            log.error("CacheUpdateBroadcastMQOperator更新数据，接收到mq消息为空");
            return;
        }
        log.info("CacheUpdateBroadcastMQOperator更新数据，接收到mq消息：" + msg);
        String cacheType = msg.getCacheType();
        if(StringUtils.isNotBlank(cacheType)){
            CpeCacheManager.CpeCacheMessage message = new CpeCacheManager.CpeCacheMessage();
            message.setCacheType(cacheType);
            cpeCacheManager.deleteCache(message);
        }
        else{
            log.info("CacheUpdateBroadcastMQOperator更新数据，接收到cacheType为空");
        }
    }
}
