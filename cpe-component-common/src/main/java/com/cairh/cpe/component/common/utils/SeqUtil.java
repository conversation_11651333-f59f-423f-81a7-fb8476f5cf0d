package com.cairh.cpe.component.common.utils;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;

/**
 * <AUTHOR>
 * @since 2022-07-22
 */
public class SeqUtil {

    public static String getSeqCode() {
        IdentifierGenerator identifierGenerator = SpringUtil.getBean("idGenerator", IdentifierGenerator.class);
        return identifierGenerator.nextUUID(null);
    }
}