package com.cairh.cpe.component.common.data.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cairh.cpe.component.common.data.entity.InterfaceDataJour;
import com.cairh.cpe.component.common.data.entity.RiskUserInfo;
import com.cairh.cpe.component.common.form.AgreemodelbusinessForm;
import com.cairh.cpe.component.common.form.AgreemodelbusinessVo;
import com.cairh.cpe.component.common.form.InterfaceDataJourDisplayForm;
import com.cairh.cpe.component.common.form.InterfaceDataJourDisplayVo;
import com.cairh.cpe.component.common.form.InterfaceDataJourForm;
import com.cairh.cpe.component.common.form.InterfaceDataJourVo;

import java.util.List;

public interface InterfaceDataJourMapper extends BaseMapper<InterfaceDataJour> {

	/**
	 * 关联、分页查询
	 * @param param
	 * @return
	 */
	Page<InterfaceDataJourVo> diySelectPage(InterfaceDataJourForm param);

	/**
	 * 数据显示
	 * @param param
	 * @return
	 */
	List<InterfaceDataJourDisplayVo> datadisplay(InterfaceDataJourDisplayForm param);

}
