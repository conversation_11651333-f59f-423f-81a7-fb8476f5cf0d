package com.cairh.cpe.component.common.form;

import com.cairh.cpe.component.common.form.support.AgreementRel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 用于协议规则管理 新增、更新、详情
 * <AUTHOR>
 * @since 2022-06-07
 */
@Data
public class AgreemodelbusinessDto {

    /**
     * 子系统编号
     */
    private Integer subsys_no;

    /**
     * 轮播模式
     */
    private String auto_read_type;

    /**
     * 业务编号
     */
    private Integer busin_type;

    /**
     * 机构标识
     */
    private String organ_flag;

    /**
     * 创建人
     */
    private String create_by;

    /**
     * 创建日期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date create_datetime;

    /**
     * 修改人
     */
    private String modify_by;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date modify_datetime;

    /**
     * 状态
     */
    private String status;

    /**
     * 归历史标志
     */
    private String tohis_flag;

    /**
     * 归历史时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date tohis_datetime;

    /**
     * 协议列表
     */
    private List<AgreementRel> agreementRelList;
}