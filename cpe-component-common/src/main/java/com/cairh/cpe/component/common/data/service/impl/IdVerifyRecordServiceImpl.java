package com.cairh.cpe.component.common.data.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.component.common.data.entity.IdVerifyRecord;
import com.cairh.cpe.component.common.data.mapper.IdVerifyRecordMapper;
import com.cairh.cpe.component.common.data.service.IIdVerifyRecordService;
import com.cairh.cpe.component.common.form.VerifyDetailQueryForm;
import com.cairh.cpe.component.common.form.VerifyQueryForm;
import com.cairh.cpe.component.common.form.response.VerifyDetailQueryResp;
import com.cairh.cpe.component.common.form.response.VerifyQueryResp;
import com.cairh.cpe.component.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Iterator;
import java.util.List;

/**
 * <p>
 * 身份认证记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-13
 */
@Service
public class IdVerifyRecordServiceImpl extends ServiceImpl<IdVerifyRecordMapper, IdVerifyRecord> implements IIdVerifyRecordService {

    @Autowired
    private IdVerifyRecordMapper idVerifyRecordMapper;

    @Override
    public void diyInsertOrUpdate(IdVerifyRecord idverifyrecord) {
        idverifyrecord.setCreate_datetime(new Date());
//        List<IdVerifyRecord> list = this.lambdaQuery()
//                .eq(IdVerifyRecord::getCsdc_busi_kind,idverifyrecord.getCsdc_busi_kind())
//                .eq(IdVerifyRecord::getId_kind, idverifyrecord.getId_kind())
//                .eq(IdVerifyRecord::getId_no, idverifyrecord.getId_no())
//                .eq(IdVerifyRecord::getVerify_type, idverifyrecord.getVerify_type())
//                .eq(IdVerifyRecord::getFull_name, idverifyrecord.getFull_name()).list();
//        if (CollectionUtils.isNotEmpty(list)) {
//            idverifyrecord.setSerial_id(list.stream().findFirst().map(IdVerifyRecord::getSerial_id).orElse(null));
//            this.update(idverifyrecord, new QueryWrapper<IdVerifyRecord>().lambda()
//                    .eq(IdVerifyRecord::getCsdc_busi_kind,idverifyrecord.getCsdc_busi_kind())
//                    .eq(IdVerifyRecord::getId_kind, idverifyrecord.getId_kind())
//                    .eq(IdVerifyRecord::getId_no, idverifyrecord.getId_no())
//                    .eq(IdVerifyRecord::getVerify_type, idverifyrecord.getVerify_type())
//                    .eq(IdVerifyRecord::getFull_name, idverifyrecord.getFull_name()));
//        } else {
            idverifyrecord.setSerial_id(null);
            this.save(idverifyrecord);
//        }
    }

    @Override
    public Page<VerifyQueryResp> verifyQuery(VerifyQueryForm param) {
        Page<VerifyQueryResp> verifyQueryRespPage = idVerifyRecordMapper.verifyQuery(param);
        List<VerifyQueryResp> respList = verifyQueryRespPage.getRecords();
        for(int i = 0; i < respList.size(); i++){
            VerifyQueryResp verifyQueryResp = respList.get(i);
            verifyQueryResp.setId(i + "");
            // 两个计数字段兜底为0
            if(StringUtils.isBlank(verifyQueryResp.getTotal_num())){
                verifyQueryResp.setTotal_num("0");
            }
            if(StringUtils.isBlank(verifyQueryResp.getTotal_success())){
                verifyQueryResp.setTotal_success("0");
            }
        }
        return verifyQueryRespPage;
    }

    @Override
    public Page<VerifyDetailQueryResp> verifyDetailQuery(VerifyDetailQueryForm param) {
        Page<VerifyDetailQueryResp> idverifyrecordPage = idVerifyRecordMapper.verifyDetailQuery(param);
        List<VerifyDetailQueryResp> idVerifyRecords = idverifyrecordPage.getRecords();
        for (Iterator<VerifyDetailQueryResp> iterator = idVerifyRecords.iterator(); iterator.hasNext(); ) {
            VerifyDetailQueryResp record = iterator.next();
            String status = record.getStatus();
            //状态：0不通过、1通过、2异常
            if(StringUtils.equals(status, "0") || StringUtils.equals(status,"2")){
                record.setStatus("失败");
            }
            else if(StringUtils.equals(status, "1")){
                record.setStatus("通过");
            }
        }
        return idverifyrecordPage;
    }
}
