package com.cairh.cpe.component.common.data.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NonNull;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

/**
 * 视频队列优先级配置
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("VIDEOPRIORITYCONFIG")
public class VideoPriorityConfig extends Model<VideoPriorityConfig> {

    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId("serial_id")
    private String serial_id;

    /**
     * 优先级字段名称
     */
    @TableField("field_name")
    private String field_name;

    /**
     * 字段的优先级
     */
    @TableField("field_priority")
    private Integer field_priority;

    /**
     * 优先级字段值
     */
    @TableField("field_value")
    private String field_value;

    /**
     * 字段值的优先级
     */
    @TableField("value_priority")
    private Integer value_priority;
    /**
     * 创建时间
     */
    @TableField(value = "create_datetime")
    private Date create_datetime;
    /**
     * 修改时间
     */
    @TableField(value = "modify_datetime")
    private Date modify_datetime;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 创建人
     */
    @TableField(value = "create_by")
    private String create_by;

    /**
     * 是否启用
     */
    @TableField(value = "enable_flag")
    private String enable_flag = "1";
    public int compareTo(VideoPriorityConfig o) {
        if (o == null) {
            return 1;
        }
        String field_priority = o.getField_priority().toString();
        String value_priority = o.getValue_priority().toString();
        if (StringUtils.isAnyBlank(field_priority, value_priority) && StringUtils.isAnyBlank(this.field_priority.toString(), this.value_priority.toString())) {
            return 0;
        }

        Integer fp = Integer.parseInt(field_priority);
        Integer vp = Integer.parseInt(value_priority);
        Integer thisFp = this.field_priority;
        Integer thisVp = this.value_priority;
        if (thisFp == fp) {
            return thisVp.compareTo(vp);
        }
        return thisFp.compareTo(fp);
    }

}
