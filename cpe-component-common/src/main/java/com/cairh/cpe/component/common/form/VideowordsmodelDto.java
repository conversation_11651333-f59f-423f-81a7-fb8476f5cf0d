package com.cairh.cpe.component.common.form;

import com.cairh.cpe.component.common.data.entity.VideoWordsConfig;
import com.cairh.cpe.component.common.data.entity.VideoWordsModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-07-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ToString(callSuper = true)
public class VideowordsmodelDto extends VideoWordsModel {

    /**
     * 话术列表
     */
    private List<VideoWordsConfig> videowordsconfigList;
}