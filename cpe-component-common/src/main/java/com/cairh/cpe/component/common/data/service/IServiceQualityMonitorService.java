package com.cairh.cpe.component.common.data.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cairh.cpe.component.common.data.entity.RiskUserInfo;
import com.cairh.cpe.component.common.data.entity.ServiceQualityMonitor;
import com.cairh.cpe.component.common.form.RiskInfoDetailDto;
import com.cairh.cpe.component.common.form.ServiceQualityMonitorForm;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.context.Result;
import org.springframework.web.bind.annotation.RequestBody;

public interface IServiceQualityMonitorService extends IService<ServiceQualityMonitor> {

	public void insert(BaseUser baseUser, ServiceQualityMonitor entity);

	public Page<ServiceQualityMonitor> queryByPage( BaseUser baseUser, ServiceQualityMonitorForm param);

}
