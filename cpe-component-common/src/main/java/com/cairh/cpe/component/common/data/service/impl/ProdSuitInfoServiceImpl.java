package com.cairh.cpe.component.common.data.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.component.common.utils.BaseBeanUtil;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.context.constant.ErrorCode;
import com.cairh.cpe.component.common.constant.Constant;
import com.cairh.cpe.component.common.data.entity.ProdSuitInfo;
import com.cairh.cpe.component.common.data.entity.ProdSuitInfoJour;
import com.cairh.cpe.component.common.data.entity.SuitMatchRule;
import com.cairh.cpe.component.common.data.mapper.ProdSuitInfoMapper;
import com.cairh.cpe.component.common.data.service.IProdSuitInfoService;
import com.cairh.cpe.component.common.data.service.ISuitMatchRuleService;
import com.cairh.cpe.component.common.form.request.ProdsuitinfoAndRule;
import com.cairh.cpe.component.common.utils.JourUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 产品适当性信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-23
 */
@Slf4j
@Service
@AllArgsConstructor
public class ProdSuitInfoServiceImpl extends ServiceImpl<ProdSuitInfoMapper, ProdSuitInfo> implements IProdSuitInfoService {

    private final ISuitMatchRuleService suitmatchruleService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void baseSave(ProdsuitinfoAndRule entity) {
        if (CollectionUtils.isNotEmpty(this.lambdaQuery().eq(ProdSuitInfo::getProd_code, entity.getProd_code())
                .eq(ProdSuitInfo::getProdta_no, entity.getProdta_no()).list())) {
            throw new BizException(ErrorCode.ERR_PARAM_IN_ERROR, "同一个TA编号的同一个产品业务只允许添加一次");
        }
        ProdSuitInfo prodsuitinfo = BaseBeanUtil.copyProperties(entity, ProdSuitInfo.class);
        this.save(prodsuitinfo);
        List<SuitMatchRule> entityList = entity.getEntityList();
        if (CollectionUtils.isNotEmpty(entityList)) {
            suitmatchruleService.baseSaveBatch(entityList);
        }
        log.info("保存一条产品适当性信息成功，serial_id为:{}", entity.getSerial_id());
        JourUtil.writeJour(() -> saveJour(prodsuitinfo, false, Constant.BUSINESS_FLAG_ADD));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void baseUpdate(ProdsuitinfoAndRule entity) {
        List<SuitMatchRule> entityList = entity.getEntityList();
        Map<Boolean, List<SuitMatchRule>> insert_or_update_map=new HashMap<>();
        if (CollectionUtils.isNotEmpty(entityList)) {
            List<String> updateIds = suitmatchruleService.saveOrUpdateOrDelBatch(entityList);
             insert_or_update_map = entityList.stream().collect(Collectors.groupingBy(x -> updateIds.contains(x.getSerial_id())));
        }
        ProdSuitInfo prodsuitinfo = BaseBeanUtil.copyProperties(entity, ProdSuitInfo.class);
        updateById(prodsuitinfo);
        List<SuitMatchRule> updateList = insert_or_update_map.get(Boolean.TRUE);
        List<SuitMatchRule> insertList = insert_or_update_map.get(Boolean.FALSE);
        JourUtil.writeJour(() -> suitmatchruleService.saveJour(updateList, true, Constant.BUSINESS_FLAG_MOD));
        JourUtil.writeJour(() -> suitmatchruleService.saveJour(insertList, false, Constant.BUSINESS_FLAG_ADD));

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void baseDelete(String serial_id) {
        ProdSuitInfo prodsuitinfo = getById(serial_id);
        suitmatchruleService.remove(new QueryWrapper<SuitMatchRule>().lambda()
                .eq(SuitMatchRule::getProd_code, prodsuitinfo.getProd_code())
                .eq(SuitMatchRule::getProdta_no, prodsuitinfo.getProdta_no()));
        removeById(serial_id);
    }

    private boolean saveJour(ProdSuitInfo entity, boolean isUpdate, Integer business_flag) {
        if (isUpdate) {
            entity = getById(entity.getSerial_id());
        }
        entity=entity.selectById();
        ProdSuitInfoJour prodsuitinfojour = BaseBeanUtil.copyProperties(entity, ProdSuitInfoJour.class);
        prodsuitinfojour.setSerial_id(null);
        prodsuitinfojour.setProdsuitinfo_id(entity.getSerial_id());
        prodsuitinfojour.setBusiness_flag(business_flag);
        return prodsuitinfojour.insert();
    }

    @Override
    public boolean updateById(ProdSuitInfo entity) {
        List<ProdSuitInfo> list = lambdaQuery()
                .eq(ProdSuitInfo::getProdta_no, entity.getProdta_no())
                .eq(ProdSuitInfo::getProd_code, entity.getProd_code())
                .list();
        if (CollectionUtils.isNotEmpty(list)) {
            for (ProdSuitInfo prodsuitinfo : list) {
                if (!StringUtils.equals(prodsuitinfo.getSerial_id(), entity.getSerial_id())) {
                    throw new BizException(ErrorCode.ERR_PARAM_IN_ERROR, "同样的产品代码与ta代码不允许重复添加");
                }
            }
        }
        super.updateById(entity);
        log.info("更新一条产品适当性信息成功，serial_id为:{}", entity.getSerial_id());
        JourUtil.writeJour(() -> saveJour(entity, true, Constant.BUSINESS_FLAG_MOD));
        return true;
    }

}
