package com.cairh.cpe.component.common.form;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * 问卷同步请求参数
 * <AUTHOR>
 * @since 2022-07-04
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class ExamSyncReq {

    /**
     * 试题类型 1：使用本地的问题编号计算，2：使用柜台的问题编号计算
     */
    private String exam_type;

    /**
     * 试卷类型 1:客户风险测评试卷 n:港股通知识问答
     */
    @NotNull
    private String paper_type;

    private String sub_paper_type;

    /**
     * 机构标志  0 个人  1 机构    不传值默认为个人
     */
    private String organ_flag;

    /**
     * 题库ID
     */
    private String local_paper_id;

    /**
     * 产品ta编号
     */
    private String prodta_no;

    /**
     * 资产属性
     */
    private String asset_prop;

    /**
     * 业务属性
     */
    private String busin_type;

    private String create_by;
}