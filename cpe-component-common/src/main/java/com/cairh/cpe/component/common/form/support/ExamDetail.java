package com.cairh.cpe.component.common.form.support;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
public class ExamDetail {
    /**
     * 问卷编号id
     */
    private String serial_id;

    /**
     * 试卷名称
     */
    private String paper_name;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态
     */
    private String status;

    /**
     * 试卷类别
     */
    private String paper_type;

    /**
     * 机构标志
     */
    @TableField("organ_flag")
    private String organ_flag;

    /**
     * 基础分值
     */
    private BigDecimal base_score;

    /**
     * 试卷最低分
     */
    private BigDecimal min_score;

    /**
     * 试卷最高分
     */
    private BigDecimal max_score;

    /**
     * 每日测评次数
     */
    private Integer daily_permit_times;

    /**
     * 有效时间（月）
     */
    private Integer expire_in;

    /**
     * 试卷子类型
     */
    private String sub_paper_type;

    /**
     * 版本号
     */
    private String version_no;
    /**
     * 创建人
     */
    private String create_by;

    /**
     * 创建日期时间
     */
    private Date create_datetime;
    /**
     * 修改人
     */
    private String modify_by;

    /**
     * 修改时间
     */
    private Date modify_datetime;

    /**
     * 是否来自柜台
     */
    private String is_from_counter;

    /**
     * 柜台问卷编号
     */
    private String counter_paper_no;

    /**
     * 归历史标志
     */
    private String tohis_flag;

    /**
     * 归历史时间
     */
    private Date tohis_datetime;

    /**
     * 产品TA编号
     */
    private String prodta_no;

    /**
     * 规则表达式
     */
    private String regular_expre;

    /**
     * 试题列表
     */
    List<QuestionDetail> examquestions;
}
