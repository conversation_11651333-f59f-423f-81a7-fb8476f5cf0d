package com.cairh.cpe.component.common.form;

import com.cairh.cpe.component.common.constant.Constant;
import com.cairh.cpe.component.common.data.entity.ExamQuestionOptions;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper=false)
@ToString(callSuper = true)
public class ExamquestionoptionsForm extends BasePage<ExamQuestionOptions> {
    /**
     * 试题选项ID
     */
    private String serial_id;

    /**
     * 柜台试题编号
     */
    private Integer option_no;

    /**
     * 选项内容
     */
    private String option_content;

    /**
     * 问卷编号
     */
    private String exampaper_id;

    /**
     * 分数
     */
    private BigDecimal score;

    /**
     * 问卷试题ID
     */
    private String examquestion_id;

    /**
     * 选项关联指标值
     */
    private String relation_value;

    /**
     * 最低风险标识
     */
    private String min_risk_level_flag;

    /**
     * 菜单排序信息
     */
    private Long order_no;

    /**
     * 创建日期时间
     */
    private Date create_datetime;

    private String create_by;

    /**
     * 修改时间
     */
    private Date modify_datetime;

    private String modify_by;

    /**
     * 选中提示
     */
    private String selected_tip;

    /**
     * 归历史标志
     */
    private String tohis_flag;

    /**
     * 归历史时间
     */
    private Date tohis_datetime;

    /**
     * 是否需要具体原因
     */
    private String option_need_reason;

    private String status = Constant.COMMON_VALID_STATUS;
}
