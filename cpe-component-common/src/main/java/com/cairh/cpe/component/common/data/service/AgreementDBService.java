package com.cairh.cpe.component.common.data.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.cairh.cpe.component.common.constant.ErrorConstant;
import com.cairh.cpe.component.common.data.entity.*;
import com.cairh.cpe.component.common.data.mapper.AgreeModelBusinessMapper;
import com.cairh.cpe.component.common.data.mapper.ElecAgreeModelMapper;
import com.cairh.cpe.component.common.data.mapper.ElecAgreeSignMapper;
import com.cairh.cpe.component.common.utils.BaseBeanUtil;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.context.constant.ErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.ResultContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AgreementDBService {
    @Autowired
    private ElecAgreeModelMapper elecAgreeModelMapper;
    @Resource
    private AgreeModelBusinessMapper agreeModelBusinessMapper;
    @Autowired
    private ElecAgreeSignMapper elecAgreeSignMapper;

    public ElecAgreeModel getElecAgreeModelById(String Id) {
        return elecAgreeModelMapper.selectById(Id);
    }

    public ElecAgreeModel getElecAgreeModelByAgreeNo(String agreement_no, String agreement_version) {
        Map<String, Object> columnMap = new HashMap<>();
        columnMap.put("agreement_no", agreement_no);
        columnMap.put("agreement_version", agreement_version);
        if (elecAgreeModelMapper.selectByMap(columnMap).size() > 0) {
            return elecAgreeModelMapper.selectByMap(columnMap).get(0);
        } else {
            BizException bizexception = new BizException(ErrorConstant.DATA_NOT_EXIST, "协议模板不存在!");
            throw bizexception;
        }
    }

    public List<ElecAgreeModel> getElecAgreeModelByAgreeStatus(String agreement_status) {
        Map<String, Object> columnMap = new HashMap<>();
        columnMap.put("agreement_status", agreement_status);
        return elecAgreeModelMapper.selectByMap(columnMap);
    }

    public boolean updateById(ElecAgreeModel elecAgreeModel){
        elecAgreeModelMapper.updateById(elecAgreeModel);
        saveJour(elecAgreeModel);
        return true;
    }

    private boolean saveJour(ElecAgreeModel elecAgreeModel) {
        ElecAgreeModelJour elecagreemodeljour = BaseBeanUtil.copyProperties(elecAgreeModel, ElecAgreeModelJour.class);
        elecagreemodeljour.setSerial_id(null);
        elecagreemodeljour.insert();
        return true;
    }

    public boolean insert(ElecAgreeModel elecAgreeModel){
        elecAgreeModelMapper.insert(elecAgreeModel);
        saveJour(elecAgreeModel);
        return true;
    }

    /**
     * 更新协议规则关联的协议模板
     *
     * @param oldSerialId
     * @param serialId
     * <AUTHOR>
     * @since 2023/6/15 19:01
     */
    public void updateModelSerialId(String oldSerialId, String serialId) {
        LambdaUpdateWrapper<AgreeModelBusiness> updateWrapper = new UpdateWrapper<AgreeModelBusiness>().lambda()
                .eq(AgreeModelBusiness::getElecagreemodel_id, oldSerialId)
                .set(AgreeModelBusiness::getElecagreemodel_id, serialId);
        agreeModelBusinessMapper.update(null, updateWrapper);
    }

    /**
     * 依据协议类型查找启用的协议模板
     *
     * @param agreementTypeList
     * @return List<com.cairh.cpe.esb.component.agreement.core.database.entity.ElecAgreeModel>
     * <AUTHOR>
     * @since 2023/6/28 10:27
     */
    public List<ElecAgreeModel> getAgreeModelByAgreementTypeList(List<String> agreementTypeList) {
        if (CollectionUtils.isEmpty(agreementTypeList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ElecAgreeModel> queryWrapper = new QueryWrapper<ElecAgreeModel>().lambda()
                .in(ElecAgreeModel::getAgreement_type, agreementTypeList)
                .eq(ElecAgreeModel::getAgreement_status, "8");
        return elecAgreeModelMapper.selectList(queryWrapper);
    }

    /**
     * 依据协议id查找启用的协议模板
     *
     * @param agreementIdList
     * @return List<com.cairh.cpe.esb.component.agreement.core.database.entity.ElecAgreeModel>
     * <AUTHOR>
     * @since 2023/6/28 10:27
     */
    public List<ElecAgreeModel> getAgreeModelByAgreementIdList(List<String> agreementIdList) {
        if (CollectionUtils.isEmpty(agreementIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ElecAgreeModel> queryWrapper = new QueryWrapper<ElecAgreeModel>().lambda()
                .in(ElecAgreeModel::getSerial_id, agreementIdList)
                .eq(ElecAgreeModel::getAgreement_status, "8");
        return elecAgreeModelMapper.selectList(queryWrapper);
    }

    public List<AgreeModelBusiness> qryNeedSignList(String subsys_no, String organ_flag, String agreement_type, String busin_type, String regular_data) {
        LambdaQueryWrapper<AgreeModelBusiness> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AgreeModelBusiness::getSubsys_no, subsys_no);
        queryWrapper.eq(AgreeModelBusiness::getOrgan_flag, organ_flag);
        queryWrapper.eq(StringUtils.isNotBlank(agreement_type), AgreeModelBusiness::getAgreement_type, agreement_type);
        queryWrapper.eq(AgreeModelBusiness::getBusin_type, busin_type);
        queryWrapper.eq(AgreeModelBusiness::getStatus, "8");
        queryWrapper.orderByAsc(AgreeModelBusiness::getOrder_no);
        return agreeModelBusinessMapper.selectList(queryWrapper);
    }

    public List<ElecAgreeModelSub> qryElecagreemodelsubList(List<String> elecagreemodel_ids) {
        ElecAgreeModelSub elecagreemodelsub = new ElecAgreeModelSub();
        List<ElecAgreeModelSub> elecagreemodelsubList = elecagreemodelsub.selectList(new QueryWrapper<ElecAgreeModelSub>().in("elecagreemodel_id", elecagreemodel_ids));
        return elecagreemodelsubList;
    }

    public List<ElecAgreeSign> qrySignedAgree(String elecagreesign_ids) {
        List<ElecAgreeSign> elecAgreeSigns = new ArrayList<>();
        if (StringUtils.isNotBlank(elecagreesign_ids)) {
            elecAgreeSigns = elecAgreeSignMapper.selectBatchIds(Arrays.asList(elecagreesign_ids.split(",")));
        }
        return elecAgreeSigns;
    }

    public ElecAgreeSign getElecagreeSignBySignId(String elecagreesign_id) {
        return elecAgreeSignMapper.selectById(elecagreesign_id);
    }

    public int updateById(ElecAgreeSign elecAgreeSign) {
        return elecAgreeSignMapper.updateById(elecAgreeSign);

    }

    public List<ElecAgreeModel> getNeedSyncElecAgreeModelList() {
        LambdaQueryWrapper<ElecAgreeModel> queryWrapper = new QueryWrapper<ElecAgreeModel>().lambda()
                .ne(ElecAgreeModel::getThird_agreement_id, " ")
                .eq(ElecAgreeModel::getAgreement_status, "8");
        return elecAgreeModelMapper.selectList(queryWrapper);
    }

    public List<ElecAgreeModel> qrySignedAgreeByAgreementSignIds(String elecagreesign_ids) {
        List<ElecAgreeModel> elecAgreeModels = new ArrayList<>();
        if (StringUtils.isNotBlank(elecagreesign_ids)) {
            List<ElecAgreeSign> elecAgreeSignList = elecAgreeSignMapper.selectBatchIds(Arrays.asList(elecagreesign_ids.split(",")));
            if (CollectionUtils.isNotEmpty(elecAgreeSignList)) {
                return elecAgreeModelMapper.selectBatchIds(elecAgreeSignList.stream().map(ElecAgreeSign::getElecagreemodel_id).collect(Collectors.toList()));
            }
        }
        return elecAgreeModels;
    }

}
