package com.cairh.cpe.component.common.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class MessageUtil {

	public  static String  getMessage(String message, Map<String,String> params){
		if(StringUtils.isEmpty(message)){
			message="";
			return message;
		}

		if(params==null)  params = new  HashMap<String,String>();

		List<String>   pl = new  ArrayList<String>();
		String regex = "\\$\\{([^}]+)}";
		Pattern p = Pattern.compile(regex);
		Matcher m = p.matcher(message);
		while (m.find()) {
			pl.add(m.group(1));
		}

		String paramsContent="";
		if(pl!=null  && pl.size()>0){
			for(String  temp:pl){

				paramsContent = params.get(temp);
				if(StringUtils.isNotBlank(paramsContent) && !StringUtils.equals("null", paramsContent)){
					message = message.replace("${"+temp+"}", paramsContent);
				}
			}

		}
		return message;
	}

}