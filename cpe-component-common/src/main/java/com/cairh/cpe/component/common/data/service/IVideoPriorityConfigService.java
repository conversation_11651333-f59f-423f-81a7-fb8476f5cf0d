package com.cairh.cpe.component.common.data.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cairh.cpe.component.common.data.entity.VideoPriorityConfig;
import com.cairh.cpe.component.common.form.support.PriorityFilterResult;
import org.springframework.stereotype.Service;


public interface IVideoPriorityConfigService extends IService<VideoPriorityConfig> {

    public PriorityFilterResult checkAmbiguous(VideoPriorityConfig config);
}
