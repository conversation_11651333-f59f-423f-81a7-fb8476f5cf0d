package com.cairh.cpe.component.common.data.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.component.common.data.entity.CsdcDataExchange;
import com.cairh.cpe.component.common.data.mapper.CsdcDataExchangeMapper;
import com.cairh.cpe.component.common.data.service.ICsdcDataExchangeService;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-29
 */
@Service
public class CsdcDataExchangeServiceImpl extends ServiceImpl<CsdcDataExchangeMapper, CsdcDataExchange> implements ICsdcDataExchangeService {

}
