package com.cairh.cpe.component.common.model;

import cn.hutool.core.lang.UUID;
import com.alibaba.fastjson.annotation.JSONField;
import com.cairh.cpe.mq.operator.AbstractBroadcastMQOperator;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 短信kafka通道
 * <AUTHOR>
 */
@Component
public class SmsKafkaChannelProducer extends AbstractBroadcastMQOperator<SmsKafkaChannelProducer.SmsChannelMsg> {


    @Getter
    @Value("${cpe.topic.smsTopicName:G_P_USER_DHYGT2MSG}")
    private String destinationName;

    @Getter
    @Setter
    public static class SmsChannelMsg {

        @JSONField(name = "SRC")
        private String SRC = "19";
        @JSONField(name = "MSG_TYPE")
        private String MSG_TYPE = "ICONV_EVT";
        @JSONField(name = "MSG_SUBTYPE")
        private String MSG_SUBTYPE = "KV";
        @JSONField(name = "TARGET")
        private List<String> TARGET = Lists.newArrayList("18");
        @JSONField(name = "SEND_DATE")
        private Integer SEND_DATE;
        @JSONField(name = "SEND_TIME")
        private Integer SEND_TIME;
        @JSONField(name = "MSG_ID")
        private String MSG_ID = UUID.randomUUID(true).toString();

        /**
         * 具体的消息中心格式规范（消息仅支持json格式）
         */
        @JSONField(name = "CONTENT")
        private String CONTENT;

    }

    @Getter
    @Setter
    public static class SmsContent {

        /**
         *  消息源ID
         */
        @JSONField(name = "providerId")
        private String providerId = "SPJZ";

        /**
         *  发送通道
         */
        @JSONField(name = "channels")
        private String channels = "message";

        /**
         *  发送对象  手机号码
         */
        @JSONField(name = "sendAccounts")
        private String sendAccounts;

        /**
         *  发送对象  手机号码
         */
        @JSONField(name = "sendAcctType")
        private String sendAcctType = "tel";

        /**
         * 消息本体，为嵌套json格式，内容字段如下区分
         */
        @JSONField(name = "msgContent")
        private String msgContent;


    }

    @Getter
    @Setter
    public static class MsgContent {

        /**
         * 子主题id
         */
        @JSONField(name = "mtype")
        private String mtype ;

        /**
         * 传短信内容
         */
        @JSONField(name = "msgBody")
        private String msgBody;

        /**
         *  员工号
         */
        @JSONField(name = "empId")
        private String empId;

    }
}
