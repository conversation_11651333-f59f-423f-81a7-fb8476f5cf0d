package com.cairh.cpe.component.common.data.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cairh.cpe.component.common.data.entity.ElecAgreeModel;
import com.cairh.cpe.component.common.form.AgreemodelbusinessForm;
import com.cairh.cpe.component.common.form.AgreemodelbusinessVo;
import com.cairh.cpe.component.common.form.ElecagreemodelForm;
import com.cairh.cpe.component.common.form.VideowordsmodelDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 电子协议模板表 Mapper 接口
 * </p>
 *
 */
public interface ElecAgreeModelMapper extends BaseMapper<ElecAgreeModel> {

	Page<ElecAgreeModel> diySelectPage(Page<ElecAgreeModel> pageInfo);

	Page<ElecAgreeModel> diySelectPageParam(ElecagreemodelForm param);
}