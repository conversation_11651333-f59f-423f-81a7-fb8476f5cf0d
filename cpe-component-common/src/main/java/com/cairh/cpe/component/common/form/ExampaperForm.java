package com.cairh.cpe.component.common.form;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.cairh.cpe.component.common.data.entity.ExamPaper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper=false)
@ToString(callSuper = true)
public class ExampaperForm extends BasePage<ExamPaper> {

    {
        OrderItem orderItem1 = new OrderItem();
        OrderItem orderItem2 = new OrderItem();
        orderItem1.setAsc(false);
        orderItem1.setColumn("create_datetime");
        orderItem2.setAsc(true);
        orderItem2.setColumn("serial_id");
        ArrayList<OrderItem> orderItems = new ArrayList<>();
        orderItems.add(orderItem1);
        orderItems.add(orderItem2);
        super.setOrders(orderItems);
    }

    /**
     * 问卷编号id
     */
    private String serial_id;

    /**
     * 试卷名称
     */
    private String paper_name;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态
     */
    private String status ;

    /**
     * 试卷类别
     */
    private String paper_type;

    /**
     * 机构标志
     */
    private String organ_flag;

    /**
     * 基础分值
     */
    private BigDecimal base_score;

    /**
     * 试卷最低分
     */
    private BigDecimal min_score;

    /**
     * 试卷最高分
     */
    private BigDecimal max_score;

    /**
     * 每日测评次数
     */
    private Integer daily_permit_times;

    /**
     * 有效时间（月）
     */
    private Integer expire_in;

    /**
     * 试卷子类型
     */
    private String sub_paper_type;

    /**
     * 版本号
     */
    private String version_no;

    private String create_by;

    /**
     * 创建日期时间
     */
    private Date create_datetime;

    private String modify_by;

    /**
     * 修改时间
     */
    private Date modify_datetime;

    /**
     * 是否来自柜台
     */
    private String is_from_counter;

    /**
     * 柜台问卷编号
     */
    private String counter_paper_no;

    /**
     * 归历史标志
     */
    private String tohis_flag;

    /**
     * 归历史时间
     */
    private Date tohis_datetime;

    /**
     * 产品TA编号
     */
    private String prodta_no;

    /**
     * 规则表达式
     */
    private String regular_expre;
}
