package com.cairh.cpe.component.common.utils;


import cfca.com.itextpdf.text.BaseColor;
import cfca.com.itextpdf.text.pdf.BaseFont;
import cfca.com.itextpdf.text.pdf.PdfContentByte;
import cfca.com.itextpdf.text.pdf.PdfReader;
import cfca.com.itextpdf.text.pdf.PdfStamper;
import com.aspose.pdf.*;
import com.aspose.pdf.facades.PdfFileSignature;
import com.cairh.cpe.component.common.constant.AgreementStdErrorNo;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.context.constant.ErrorCode;
import com.cairh.cpe.component.common.utils.model.PdfSignPos;
import com.cairh.cpe.component.common.utils.model.SignPos;
import com.cairh.cpe.util.json.FastJsonUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.net.URL;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;


public class AsposePdfUtil {

    private static Logger logger = LoggerFactory.getLogger(AsposePdfUtil.class);

    private static boolean IsLoadLicense = false;

    public static void main(String[] args) throws IOException {

        if (!getLicense("D:\\project\\xpe-sisap-arch\\WebCodes\\src\\main\\resources\\license.xml")) {
            return;
        }
        SeachTextAddImgBase64OfPDf("C:\\Users\\<USER>\\Desktop\\127694.pdf",
                "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",
                "[{\"kw\":\"客户：\", \"index\":\"1\", \"width\":\"128\", \"height\":\"48\", \"xOffset\":\"40\", \"yOffset\":\"-20\"},{\"kw\":\"哈尔滨珠江路\", \"index\":\"1\", \"width\":\"128\", \"height\":\"48\", \"xOffset\":\"40\", \"yOffset\":\"-20\"},{\"kw\":\"日期\", \"index\":\"1\", \"width\":\"128\", \"height\":\"48\", \"xOffset\":\"40\", \"yOffset\":\"-20\"}]");
    }
    
    public static String SeachTextAddImgBase64OfPDf(String pdfPath,String image_data,String sign_pos) {

        if (!getLicense()) {
            throw new BizException(ErrorCode.ERR_UNKNOWN,"PdF文件查找关键字并增加签字图片发生失败:", AgreementStdErrorNo.ERROR_PDF_ADDSIGNIMAGE_FAILD);
        }
        try
        {
            // 生成临时目标文件路径
            String uuid = UUID.randomUUID().toString();
            String fileType = "pdf";
            String tempFilePath = FileUtil.getTempPath();
            String outPath = tempFilePath + uuid + "." + fileType;
            if(StringUtils.isBlank(sign_pos)){
                throw new BizException(ErrorCode.ERR_UNKNOWN,"PdF文件添加个人签名时签名关键字为空", AgreementStdErrorNo.ERROR_PDF_ADDSIGNIMAGE_FAILD);
            }
            //读取sign_pos列表
            List<SignPos> listSignpos = FastJsonUtil.parseList(sign_pos, SignPos.class);

            if(listSignpos.size() <= 0){
                throw new BizException(ErrorCode.ERR_UNKNOWN,"PdF文件添加个人签名时签名关键字为空", AgreementStdErrorNo.ERROR_PDF_ADDSIGNIMAGE_FAILD);
            }
            // Open document
            Document pdfDocument = new Document(pdfPath);

            List<PdfSignPos> pdfSignPosList = new ArrayList<>();

            for(SignPos signPos : listSignpos){
                String searchTxt = signPos.getKw();
                if(StringUtils.isBlank(searchTxt)){
                    throw new BizException(ErrorCode.ERR_UNKNOWN,"PdF文件添加个人签名时签名关键字为空", AgreementStdErrorNo.ERROR_PDF_ADDSIGNIMAGE_FAILD);
                }
                int index = Integer.parseInt(signPos.getIndex());
                int width = Integer.parseInt(signPos.getWidth());
                int height = Integer.parseInt(signPos.getHeight());
                int xoffset =  Integer.parseInt(signPos.getxOffset());
                int yoffset =  Integer.parseInt(signPos.getyOffset());
                // Create TextAbsorber object to find all instances of the input search phrase
                TextFragmentAbsorber textFragmentAbsorber = new TextFragmentAbsorber(searchTxt);
                // Accept the absorber for first page of document
                pdfDocument.getPages().accept(textFragmentAbsorber);
                // Get the extracted text fragments into collection
                TextFragmentCollection textFragmentCollection = textFragmentAbsorber.getTextFragments();
                // Loop through the Text fragments
                int isearchIndex = 1;
                boolean bIsAdd = false;
                for (TextFragment textFragment : (Iterable<TextFragment>) textFragmentCollection) {
                    if(isearchIndex == index){
                        int x = (int)textFragment.getRectangle().getLLX();
                        int y = (int)textFragment.getRectangle().getLLY();

                        PdfSignPos pdfSignPos = new PdfSignPos();
                        pdfSignPos.setPageIndex(textFragment.getPage().getNumber());
                        pdfSignPos.setX(x + xoffset);
                        pdfSignPos.setY(y + yoffset);
                        pdfSignPos.setxOffset(x + width + xoffset);
                        pdfSignPos.setyOffset(y + height + yoffset);
                        pdfSignPosList.add(pdfSignPos);
                        bIsAdd = true;
                        break;
                    }
                    isearchIndex ++;
                }
                if(!bIsAdd){
                    throw new BizException(ErrorCode.ERR_UNKNOWN,"PdF文件增加签字图片时没有查询到关键字:" + searchTxt, AgreementStdErrorNo.ERROR_PDF_ADDSIGNIMAGE_NOEXITS_QRYKEY);
                }
            }
            if(pdfSignPosList.isEmpty()){
                throw new BizException(ErrorCode.ERR_UNKNOWN,"PdF文件增加签字图片时所有关键字都没有查询到", AgreementStdErrorNo.ERROR_PDF_ADDSIGNIMAGE_NOEXITS_QRYKEY);
            }

            if(!PdfAddImage(pdfPath,image_data,outPath,pdfSignPosList)){
                throw new BizException(ErrorCode.ERR_UNKNOWN,"PdF文件查找关键字并增加签字图片发生失败:", AgreementStdErrorNo.ERROR_PDF_ADDSIGNIMAGE_FAILD);
            }
            logger.info("临时{}路径为:{}", fileType, outPath);
            if(pdfDocument != null){
                pdfDocument.close();
            }
            return outPath;
        }
        catch (BizException e){
            throw e;
        }
        catch (Exception e){
            logger.error("PdF文件查找关键字并增加签字图片发生异常:", e);
            throw new BizException(ErrorCode.ERR_UNKNOWN,"PdF文件查找关键字并增加签字图片发生异常:", AgreementStdErrorNo.ERROR_PDF_ADDSIGNIMAGE_EXP);
        }
    }

    public static void SeachTextAddImgOfPDf(String pdfPath,String searchTxt, String imgPath,String outPath) throws IOException {
        // Open document
        Document pdfDocument = new Document(pdfPath);
        // Create TextAbsorber object to find all instances of the input search phrase
        TextFragmentAbsorber textFragmentAbsorber = new TextFragmentAbsorber(searchTxt);
        // Accept the absorber for first page of document
        pdfDocument.getPages().accept(textFragmentAbsorber);
        // Get the extracted text fragments into collection
        TextFragmentCollection textFragmentCollection = textFragmentAbsorber.getTextFragments();
        // Loop through the Text fragments
        for (TextFragment textFragment : (Iterable<TextFragment>) textFragmentCollection) {

                try
                {

                    int x = (int)textFragment.getRectangle().getLLX();
                    int y = (int)textFragment.getRectangle().getLLY();
                    signPdf("C:\\Users\\<USER>\\Desktop\\127694.pdf","C:\\Users\\<USER>\\Desktop\\11.jpg",
                            new java.awt.Rectangle(x,y, 255, 109),textFragment.getPage().getNumber(),
                            "C:\\Users\\<USER>\\Desktop\\cairenhui.p12",
                            "C:\\Users\\<USER>\\Desktop\\4.pdf");
                    break;
                }
                catch (IOException ex){
                    throw ex;
                }
//            System.out.println("Text :- " + textFragment.getText());
//            System.out.println("Position :- " + textFragment.getPosition());
//            System.out.println("XIndent :- " + textFragment.getPosition().getXIndent());
//            System.out.println("YIndent :- " + textFragment.getPosition().getYIndent());
//            System.out.println("Font - Name :- " + textFragment.getTextState().getFont().getFontName());
//            System.out.println("Font - IsAccessible :- " + textFragment.getTextState().getFont().isAccessible());
//            System.out.println("Font - IsEmbedded - " + textFragment.getTextState().getFont().isEmbedded());
//            System.out.println("Font - IsSubset :- " + textFragment.getTextState().getFont().isSubset());
//            System.out.println("Font Size :- " + textFragment.getTextState().getFontSize());
//            System.out.println("Foreground Color :- " + textFragment.getTextState().getForegroundColor());
        }
    }

    /**
     * 获取license
     * @return
     */
    public static boolean getLicense(String licensePath) {
        boolean result = false;
        if(IsLoadLicense){
            return true;
        }
        InputStream is=null;
        try {
            is = new FileInputStream(licensePath);
            License aposeLic = new License();
            aposeLic.setLicense(is);
            result = true;
        } catch (Exception e) {
//            logger.error("-1",e);
        }finally {
            if(is!=null){
                try {
                    is.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        IsLoadLicense = result;
        return result;
    }

    /**
     * 获取license
     * @return
     */
    public static boolean getLicense() {
        boolean result = false;
        if(IsLoadLicense){
            return true;
        }
        try {
            String currentResource = URLDecoder.decode(AsposeWordUtil.class.getProtectionDomain().getCodeSource().getLocation().getFile(), "UTF-8");
            if (currentResource.endsWith(".jar")) {
                currentResource = currentResource.substring(0, currentResource.lastIndexOf("/") + 1);
            }
            URL url = AsposeWordUtil.class.getClassLoader().getResource("license.xml");
            String path = url.getPath();
            currentResource=path.substring(0,path.indexOf("license.xml"));
            File systemConfigFile = new File(currentResource + File.separator + "license.xml");



            InputStream is = new FileInputStream(systemConfigFile);
//			InputStream is = AsposeWordUtil.class.getClassLoader().getResourceAsStream(File.separator + "license.xml");

            com.aspose.words.License aposeLic = new com.aspose.words.License();
            aposeLic.setLicense(is);
            result = true;
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
        }
        IsLoadLicense = result;
        return result;
    }

//    /**
//     * 获取license
//     * @return
//     */
//    public static boolean getLicense() {
//        boolean result = false;
//        if(IsLoadLicense){
//            return true;
//        }
//        try {
//            InputStream is = AsposePdfUtil.class.getClassLoader().getResourceAsStream(File.separator + "license.xml");
//
//            License aposeLic = new License();
//            aposeLic.setLicense(is);
//            result = true;
//        } catch (Exception e) {
////            logger.error("-1",e);
//        }
//        IsLoadLicense = result;
//        return result;
//    }

    /**
     * 文档转换pdf 包含html doc docx
     * @param pdfPath
     * @param imgPath
     * @return
     */
    public static boolean PdfAddImage(String pdfPath, String image_data, String outPath, List<PdfSignPos> pdfSignPosList) throws IOException {
        if (!getLicense()) {
            return false;
        }
        // Open a document
        Document pdfDocument = null;
        InputStream imageStream = null;
        try
        {
            pdfDocument = new Document(pdfPath);
            // Set coordinates
//        int lowerLeftX = 100;
//        int lowerLeftY = 100;
//        int upperRightX = 355;
//        int upperRightY = 209;
            byte[] bImageData = ImageBase64Util.getStrToBytes(image_data);
            // Get the page you want to add the image to
            for(PdfSignPos pdfSignPos : pdfSignPosList){

                Page page = pdfDocument.getPages().get_Item(pdfSignPos.getPageIndex());
                // Load image into stream
//        FileInputStream imageStream = new FileInputStream(new File(imgPath));
                // Add an image to the Images collection of the page resources

                imageStream = new ByteArrayInputStream(bImageData);
                page.getResources().getImages().add(imageStream);
                // Using the GSave operator: this operator saves current graphics state
                page.getContents().add(new Operator.GSave());
                // Create Rectangle and Matrix objects
                Rectangle rectangle = new Rectangle(pdfSignPos.getX(), pdfSignPos.getY(), pdfSignPos.getxOffset(), pdfSignPos.getyOffset());
                Matrix matrix = new Matrix(new double[] { rectangle.getURX() - rectangle.getLLX(), 0, 0, rectangle.getURY() - rectangle.getLLY(), rectangle.getLLX(), rectangle.getLLY() });
                // Using ConcatenateMatrix (concatenate matrix) operator: defines how
                // image must be placed
                page.getContents().add(new Operator.ConcatenateMatrix(matrix));
                XImage ximage = page.getResources().getImages().get_Item(page.getResources().getImages().size());
                // Using Do operator: this operator draws image
                page.getContents().add(new Operator.Do(ximage.getName()));
                // Using GRestore operator: this operator restores graphics state
                page.getContents().add(new Operator.GRestore());
            }

            // Save the new PDF
            pdfDocument.save(outPath);

            return true;
        }
        catch (Exception e){
            logger.error("PdF文件增加签字图片发生异常:", e);
            throw new BizException(ErrorCode.ERR_UNKNOWN,"PdF文件增加签字图片发生异常:", AgreementStdErrorNo.ERROR_PDF_ADDSIGNIMAGE_EXP);
        }
        finally {
            if(imageStream != null){
                imageStream.close();
            }
            if(pdfDocument != null){
                pdfDocument.close();;
            }
        }
    }


    /**
     * 文档转换pdf 包含html doc docx
     * @param pdfPath
     * @param imgPath
     * @return
     */
    public static void signPdf(String pdfPath, String imgPath,java.awt.Rectangle signRect,int page,String certPath,String outPath) throws IOException {
        if (!getLicense()) {
            return;
        }

        // create PdfFileSignature object and bind input PDF files
        PdfFileSignature pdfSign = new PdfFileSignature();
        pdfSign.bindPdf(pdfPath);
        // create a rectangle for signature location
//        java.awt.Rectangle rect = new java.awt.Rectangle(100, 200, 255, 109);
        // set signature appearance
        pdfSign.setSignatureAppearance(imgPath);

        // create any of the three signature types
//        char[] PASSWORD = "123456".toCharArray();
        PKCS1 signature = new PKCS1(certPath,"123456");
        // PKCS7 signature = new PKCS7(myDir + "temp.pfx", "password"); // PKCS#7 or
        // PKCS7Detached signature = new PKCS7Detached("temp.pfx", "password"); // PKCS#7 detached
        signature.setShowProperties(false);
        signature.setAuthority("chenzd");
        signature.setContactInfo("cont");
        pdfSign.sign(page, "pdf", "contack", "CN", true, signRect, signature);
        // save output PDF file
        pdfSign.save(outPath);
    }

    /**
     * 通过水印的形式添加标签
     * @param srcFile
     * @param text
     * @param textWidth
     * @param textHeight
     */
    public static byte[] addMark(String srcFile, String mark,int textWidth, int textHeight) {


            if (!getLicense()) {
                throw new BizException("-1", "PDF模板添加水印，获取授权失败。");
            }
        PdfReader reader = null;
        try{
            // 待加水印的文件
            reader = new PdfReader(srcFile);
        }catch (Exception e){
            logger.info("获取PDF源文件失败，获取源文件路径:{}", srcFile);
            throw new BizException("-1", "获取PDF源文件失败，获取源文件路径:" + srcFile);
        }



        try {
            PdfReader.unethicalreading = true;

            ByteArrayOutputStream out = new ByteArrayOutputStream();

            // 加完水印的文件
            PdfStamper stamper = new PdfStamper(reader, out);

            //			PdfStamper stamper = new PdfStamper(reader, new FileOutputStream(
            //					destFile));

            int total = reader.getNumberOfPages() + 1;

            PdfContentByte content;
            // 设置字体
            BaseFont font = BaseFont.createFont(BaseFont.HELVETICA_BOLD, BaseFont.WINANSI, BaseFont.NOT_EMBEDDED);

            // 循环对每页插入水印
            for (int i = 1; i < total; i++) {

                // 水印的起始
                content = stamper.getUnderContent(i);
                // 开始
                content.beginText();
                // 设置颜色 默认为黑色
                content.setColorFill(BaseColor.BLACK);
                // 设置字体及字号
                content.setFontAndSize(font, 12);

                // 设置起始位置
                // content.setTextMatrix(400, 880);
                content.setTextMatrix(textWidth, textHeight);

                String outMark = mark + "_" + i;

                // 开始写入水印
                content.showTextAligned(cfca.com.itextpdf.text.Element.ALIGN_LEFT, outMark, textWidth, textWidth, 0);
                content.endText();

            }
            stamper.close();
            reader.close();
            return out.toByteArray();
        } catch (Exception e) {
            logger.error("PDF增加标签异常:", e);
            throw new BizException("-1", "PDF增加标签异常");
        }
    }

}
