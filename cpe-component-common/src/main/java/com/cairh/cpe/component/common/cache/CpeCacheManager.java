package com.cairh.cpe.component.common.cache;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 接受cpe组件推送消息(字典更新，营业部更新，城市，系统配置，操作员信息)
 */
@Slf4j
@Component
public class CpeCacheManager {


    @Autowired
    private CacheManager cacheManager;

    private static final Map<String, String> CACHE_NAME_MAPPING = new HashMap<String, String>() {
        private static final long serialVersionUID = 8396379675679733210L;

        {
            put("allbranch", "Allbranch"); // 营业部
//            put("allcitycode", "Allcitycode");// 城市
//            put("bankconfig", "BankConfig");// 银行配置
//            put("businesstype", "Businesstype");// 业务类型
//            put("fundcompany", "FundCompany");// 基金公司
            put("basedictionary", "BasedictionaryDB"); // 字典
//            put("syspropertyconfig", "DubboPropertySourceCPE");// 系统配置
            put("operatorinfo", "Operatorinfo");// 操作员信息
        }
    };

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CpeCacheMessage {
        /**
         * 缓存类型   字典 ：basedictionary 营业部：allbranch 城市：allcitycode 系统配置：syspropertyconfig 操作员：operatorinfo
         */
        private String cacheType;

    }


    /**
     * 接受cpe组件推送消息(字典更新，营业部更新，城市，系统配置，操作员)
     * <p>
     *
     * @param message
     */
    public void deleteCache(CpeCacheMessage message) {
        log.info("接受cpe组件推送消息(字典更新，营业部更新，城市，系统配置，操作员)--->{}", message);
        if (message != null && StringUtils.isNotEmpty(message.getCacheType())) {
            String cacheName = CACHE_NAME_MAPPING.get(message.getCacheType());
            if (StringUtils.isNotEmpty(cacheName)) {
                deleteCache(cacheName, null);
            }
        }
    }


    private void deleteCache(String cacheName, String cacheKey) {
        Collection<String> cacheNames = cacheManager.getCacheNames();
        log.info("Cache[{}]Key[{}] cacheNames {}.", cacheName, cacheKey, cacheNames);
        if (cacheNames.isEmpty()) {
            log.info("Cache[{}]Key[{}]有变化，但缓存空间为空.", cacheName, cacheKey);
            return;
        }
        Iterator<String> it = cacheNames.iterator();
        while (it.hasNext()) {
            Cache cache = cacheManager.getCache(it.next());
            if (StringUtils.isNotBlank(cacheName)) {
                if (StringUtils.equals(cacheName, cache.getName())) {
                    deleteCache(cache, cacheKey);
                }
            } else {
                deleteCache(cache, cacheKey);
            }
        }
    }

    private void deleteCache(Cache cache, String cacheKey) {
        log.info("Cache[{}]Key[{}] 开始准备更新", cache.getName(), cacheKey);
        if (StringUtils.isNotBlank(cacheKey)) {
            Pattern pattern = Pattern.compile("property_key=[A-Za-z0-9_.]+");
            Matcher matcher = pattern.matcher(cacheKey);
            if (matcher.find()) {
                String[] property_keys = matcher.group().substring(13).split(",");
                if (property_keys != null && property_keys.length > 0) {
                    for (String property_key : property_keys) {
                        cache.evict(property_key);
                        log.info("Cache[{}]Key[{}]有变化，缓存Key执行了清除", cache.getName(), property_key);
                    }
                }
            }
        } else {
            Object object = cache.getNativeCache();
            log.info("cache.getNativeCache [{}]", object);
            if (object != null) {
                if (object instanceof Map && (((Map<?, ?>) object).isEmpty())) {
                    log.info("Cache[{}]内容发生变化，系统检测到无缓存数据，无需更新", cache.getName());
                    return;
                }
                cache.clear();
                log.info("Cache[{}]内容发生变化，缓存执行了清除", cache.getName());
            }
        }
    }

}
