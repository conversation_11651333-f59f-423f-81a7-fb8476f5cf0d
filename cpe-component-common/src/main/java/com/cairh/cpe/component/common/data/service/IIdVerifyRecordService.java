package com.cairh.cpe.component.common.data.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cairh.cpe.component.common.data.entity.IdVerifyRecord;
import com.cairh.cpe.component.common.form.VerifyDetailQueryForm;
import com.cairh.cpe.component.common.form.VerifyQueryForm;
import com.cairh.cpe.component.common.form.response.VerifyDetailQueryResp;
import com.cairh.cpe.component.common.form.response.VerifyQueryResp;

/**
 * <p>
 * 身份认证记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-13
 */
public interface IIdVerifyRecordService extends IService<IdVerifyRecord> {

    /**
     * 根据三要素 插入或更新
     * @param idverifyrecord
     */
    void diyInsertOrUpdate(IdVerifyRecord idverifyrecord);


    /**
     * 公安认证查询
     * @param param
     * @return
     */
    Page<VerifyQueryResp> verifyQuery(VerifyQueryForm param);

    /**
     * 公安认证详情查询
     * @param param
     * @return
     */
    Page<VerifyDetailQueryResp> verifyDetailQuery(VerifyDetailQueryForm param) ;
}
