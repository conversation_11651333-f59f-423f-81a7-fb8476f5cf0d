package com.cairh.cpe.component.common.cache;

import com.fasterxml.jackson.annotation.JacksonAnnotationsInside;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * code数据 转换
 * 如果需要通过新属性名输出，则需要跟新属性写get方法，通过get方法获取字典值进行翻译
 *  	@TableField(exist = false)
 *  	@DataConvert(code_type = DataConvert.DICT, code_dict = "subsys_no")
 *  	private String subsys_no_tranfer;
 *
 *     public String getSubsys_no_tranfer() {
 *         if(subsys_no_tranfer == null){
 *             return String.valueOf(subsys_no);
 *         }
 *         return subsys_no_tranfer;
 *     }
 * 如果直接替换原属性内容，则直接在属性上加标注
 * 营业部，操作员翻译，不需要带code_dict
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE, ElementType.FIELD, ElementType.PARAMETER})
@JacksonAnnotationsInside
@JsonSerialize(using = DataConvertSerializer.class)
public @interface DataConvert {

	public static final String DICT = "dict";

	public static final String BRANCH = "branch";

	public static final String OPERATORINFO  = "operatorinfo";

	/**
	 * 数据类型,dict-字典转换，operator-用户，branch-营业部
	 * @return
	 */
	String code_type() default DICT;

	/**
	 * 字典编号
	 * @return
	 */
	String code_dict() default "";

}
