package com.cairh.cpe.component.common.data.service.impl;

import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.component.common.constant.Constant;
import com.cairh.cpe.component.common.data.entity.*;
import com.cairh.cpe.component.common.data.mapper.ExampaperMapper;
import com.cairh.cpe.component.common.data.mapper.ExamquestionMapper;
import com.cairh.cpe.component.common.data.service.IExamQuestionJourService;
import com.cairh.cpe.component.common.data.service.IExamQuestionOptionsService;
import com.cairh.cpe.component.common.data.service.IExamQuestionService;
import com.cairh.cpe.component.common.data.service.IExamTestResultService;
import com.cairh.cpe.component.common.form.ExamquestionDto;
import com.cairh.cpe.component.common.utils.BaseBeanUtil;
import com.cairh.cpe.component.common.utils.JourUtil;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.context.constant.ErrorCode;
import com.cairh.cpe.core.autoconfiure.env.CompositePropertySources;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 问卷试题 服务实现类
 * </p>
 */
@Service
public class ExamquestionServiceImpl extends ServiceImpl<ExamquestionMapper, ExamQuestion> implements IExamQuestionService {

    @Autowired
    private IExamTestResultService examtestresultService;

    @Autowired
    private IExamQuestionOptionsService examquestionoptionsService;

    @Autowired
    private ExampaperMapper exampaperMapper;

    @Autowired
    private IExamQuestionOptionsService iExamquestionoptionsService;

    @Autowired
    private IExamQuestionJourService examquestionjourService;

    @Autowired
    private CompositePropertySources compositePropertySources;

    @Override
    public void baseDelete(BaseUser baseUser, ExamQuestion entity) {
        //判断是否可以删除
        ExamQuestion examquestion = this.getById(entity.getSerial_id());
        checkIfDelete(examquestion.getExampaper_id());
        ExamquestionServiceImpl service = (ExamquestionServiceImpl) AopContext.currentProxy();
        service.deleteBatch(entity);

    }

    @Override
    public Map<String, Object> baseQueryGourp(BaseUser baseUser, ExamQuestion entity) {
        // 问卷id必传
        if(StringUtils.isBlank(entity.getExampaper_id())){
            throw new BizException(ErrorCode.ERR_PARAM_IN_NOT_EMPTY,"问卷id不能为空!");
        }
        String paperId = entity.getExampaper_id();
        List<ExamQuestion> examQuestionList = this.lambdaQuery()
                .eq(ExamQuestion::getExampaper_id, paperId)
                .orderByAsc(ExamQuestion::getQuestion_group)
                .list();
        List<String> groupList = new ArrayList<>();
        for (ExamQuestion examQuestion : examQuestionList) {
            String questionGroup = examQuestion.getQuestion_group();
            if (StringUtils.isNotBlank(questionGroup) && !groupList.contains(questionGroup)) {
                groupList.add(questionGroup);
            }
        }
        String randomReturn = compositePropertySources.getProperty("comp.risk.service.randomReturnQuestionGroup", "0");
        Map<String, Object> result = new HashMap<>(2);
        boolean random = false;
        // 需要支持随机取题
        if ("1".equals(randomReturn)) {
            random = true;
            groupList = randomReturnOneElement(groupList);
        }
        result.put("random", random);
        result.put("group", groupList);
        return result;
    }

    @Override
    public Map<String, List<ExamQuestion>> baseQueryQuestions(BaseUser baseUser,ExamQuestion entity) {
        // 问卷id与试题分组必传
        if(StringUtils.isBlank(entity.getExampaper_id())){
            throw new BizException(ErrorCode.ERR_PARAM_IN_NOT_EMPTY,"问卷id不能为空!");
        }
        // 默认传空格
        if(StringUtils.isEmpty(entity.getQuestion_group())){
            throw new BizException(ErrorCode.ERR_PARAM_IN_NOT_EMPTY,"试题分组不能为空!");
        }
        String paperId = entity.getExampaper_id();
        String questionGroup = entity.getQuestion_group();
        Map<String,List<ExamQuestion>> examQuestionMap = new HashMap<>();
        List<ExamQuestion> examQuestionDefaultList = this.lambdaQuery()
                .eq(ExamQuestion::getExampaper_id,paperId)
                .eq(ExamQuestion::getQuestion_group,StringUtils.SPACE)
                .orderByAsc(ExamQuestion::getOrder_no)
                .list();
        examQuestionMap.put("默认分组",examQuestionDefaultList);
        List<ExamQuestion> examQuestionAllList = this.lambdaQuery()
                .eq(ExamQuestion::getExampaper_id,paperId)
                .orderByAsc(ExamQuestion::getOrder_no)
                .list();
        List<ExamQuestion> examQuestionGroupList = new ArrayList<>();
        for(ExamQuestion examQuestion : examQuestionAllList){
                if (examQuestion.getQuestion_group().equals(questionGroup)) {
                    examQuestionGroupList.add(examQuestion);
                }
        }
        if(CollectionUtils.isNotEmpty(examQuestionGroupList)){
            //附带上默认分组
            examQuestionGroupList.addAll(examQuestionDefaultList);
            List<ExamQuestion> resList = examQuestionGroupList.stream()
                    .sorted(Comparator.comparing(ExamQuestion::getOrder_no))
                    .collect(Collectors.toList());
            examQuestionMap.put(questionGroup,resList);
        }
        return examQuestionMap;
    }

    public void checkIfDelete(String paper_id) {
        if (StringUtils.isNotBlank(paper_id)) {
            ExamPaper exampaper = exampaperMapper.selectById(paper_id);
            if (Constant.COMMON_VALID_STATUS.equals(exampaper.getStatus())) {
                throw new BizException(ErrorCode.ERR_SYSWARNING, "该试题已经绑定有效问卷，不允许删除！");
            }
        }
        //校验是否可以删除
        Integer count = Math.toIntExact(examtestresultService.lambdaQuery().eq(ExamTestResult::getExampaper_id, paper_id).count());
        if (count > 0) {
            throw new BizException(ErrorCode.ERR_SYSWARNING, "该试题所属问卷已有作答记录不可以删除");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteBatch(ExamQuestion entity) {
        this.removeById(entity.getSerial_id());
        iExamquestionoptionsService.deleteByQuestion(entity.getSerial_id());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void baseUpdate(BaseUser baseUser, ExamquestionDto entity) {
        updateNormal(baseUser, entity);
    }

    private void updateNormal(BaseUser baseUser, ExamquestionDto entity) {
        entity.setModify_by(baseUser.getStaff_no());
        this.updateById(entity);
        //修改试题时传进来的选项列表是空的代表要清空
        if (CollectionUtils.isEmpty(entity.getExamquestionoptionsList())) {
            examquestionoptionsService.deleteByQuestion(entity.getSerial_id());
        }
        examquestionoptionsService.saveOrUpdateOrDelBatch(baseUser, entity.getExamquestionoptionsList());
        JourUtil.writeJour(() -> saveJour(entity, true, Constant.BUSINESS_FLAG_MOD));
    }

    @Override
    public void baseSave(BaseUser baseUser, ExamquestionDto entity) {
        ExamQuestion examquestion = BaseBeanUtil.copyProperties(entity, ExamQuestion.class);
        examquestion.setCreate_by(baseUser.getStaff_no());
        examquestion.setModify_by(baseUser.getStaff_no());
        this.save(examquestion);
        JourUtil.writeJour(() -> saveJour(examquestion, false, Constant.BUSINESS_FLAG_ADD));
        List<ExamQuestionOptions> examquestionoptionsList = entity.getExamquestionoptionsList();
        if (CollectionUtils.isNotEmpty(examquestionoptionsList)) {
            examquestionoptionsList.forEach(x -> {
                x.setExamquestion_id(examquestion.getSerial_id());
                x.setExampaper_id(examquestion.getExampaper_id());
                x.setCreate_by(examquestion.getCreate_by());
                x.setModify_by(examquestion.getCreate_by());
            });
            iExamquestionoptionsService.saveBatch(examquestionoptionsList);
            JourUtil.writeJour(() -> examquestionoptionsService.saveJour(examquestionoptionsList, false, Constant.BUSINESS_FLAG_ADD));
        }
    }

    private boolean saveJour(ExamQuestion entity, boolean isUpdate, Integer business_flag) {
        if (isUpdate) {
            entity = getById(entity.getSerial_id());
        }
        ExamQuestionJour jour = BaseBeanUtil.copyProperties(entity, ExamQuestionJour.class);
        jour.setSerial_id(null)
                .setExamquestion_id(entity.getSerial_id())
                .setBusiness_flag(business_flag);
        return jour.insert();
    }

    /**
     * 从Set集合中随机取一个元素返回
     * @param list 原Set
     * @return 包含这个新元素的Set
     */
    private List<String> randomReturnOneElement(List<String> list) {
        if (list == null || list.isEmpty()) {
            return Collections.singletonList(" ");
        }
        if (list.size() == 1) {
            return list;
        }
        return Collections.singletonList(new ArrayList<>(list).get(RandomUtil.randomInt(list.size())));
    }
}
