package com.cairh.cpe.component.common.form;
import com.cairh.cpe.component.common.data.entity.NoticeRecord;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;
@Data
@EqualsAndHashCode(callSuper = false)
@ToString(callSuper = true)
public class NoticerecordForm extends BasePage<NoticeRecord> {
    private static final long serialVersionUID = 8932306278947878546L;

    /**日期范围*/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date [] curr_date_range;
    /**ID*/
    private String serial_id;
    /**渠道*/
    private String channel_type;
    /**发送目标(手机号-mobile、邮件-email、客户端推送-device_id)*/
    private String notice_target;
    /**内容标题*/
    private String notice_title;
    /**发送内容*/
    private String notice_content;
    /**验证码*/
    private String valid_code;
    /**发送状态 0 新增 1发送成功 2发送失败*/
    private Integer status;
    /**发送类型(0 短信 1 语音 2 邮件  3 微信 4 app推送)*/
    private String send_type;
    /**发送方式(0 实时  1 轮询)*/
    private String send_kind;
    /**结果描述*/
    private String result_info;
    /**重试次数*/
    private Integer redo_times;
    /**创建时间*/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date create_datetime;
    /**执行时间*/
    private Date update_datetime;
    /**通道名称(记录实现类名，体现不同的通道)*/
    private String factory_name;
    /***/
    private String remark;

    /** 业务类型 **/
    private String busin_type;

    /** 子业务类型 **/
    private String sub_busin_type;


}
