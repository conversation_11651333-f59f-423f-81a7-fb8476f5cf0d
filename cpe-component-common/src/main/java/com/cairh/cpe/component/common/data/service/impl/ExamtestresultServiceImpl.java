package com.cairh.cpe.component.common.data.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cairh.cpe.component.common.data.entity.ExamTestResult;
import com.cairh.cpe.component.common.data.mapper.ExamtestresultMapper;
import com.cairh.cpe.component.common.data.service.IExamTestResultService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.component.common.form.ExamTestResultDto;
import com.cairh.cpe.component.common.form.ExamTestResultForm;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 问卷结果 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-06
 */
@Service
public class ExamtestresultServiceImpl extends ServiceImpl<ExamtestresultMapper, ExamTestResult> implements IExamTestResultService {

    @Override
    public Page<ExamTestResultDto> queryPageExamTestResultInfo(ExamTestResultForm param) {
        Page<ExamTestResultDto> examTestResultDtoPage = baseMapper.diySelectPageParam(param);
        return examTestResultDtoPage;
    }
}
