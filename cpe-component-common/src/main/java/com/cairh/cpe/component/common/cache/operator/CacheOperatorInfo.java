package com.cairh.cpe.component.common.cache.operator;

import com.cairh.cpe.component.common.cache.ICache;
import com.cairh.cpe.esb.base.rpc.dto.resp.VBaseQryUserInfosResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年01月10日 17:44
 */
@Slf4j
@Component
public class CacheOperatorInfo implements ICache {

	@Autowired
	ICacheOperatorInfoService cacheOperatorInfoService;

	public VBaseQryUserInfosResponse baseDataQryOperatorInfo(String staff_no) {
		VBaseQryUserInfosResponse operatorInfo = null;
		List<VBaseQryUserInfosResponse> list = cacheOperatorInfoService.getOperatorinfos();
		for (Iterator<VBaseQryUserInfosResponse> iterator = list.iterator(); iterator.hasNext(); ) {
			VBaseQryUserInfosResponse userInfosResponse = iterator.next();
			if(StringUtils.equals(userInfosResponse.getStaff_no(), staff_no)){
				operatorInfo = userInfosResponse;
			}
		}
		return operatorInfo ;
	}

	@Override
	public void refresh() {
		cacheOperatorInfoService.refresh();
	}
}
