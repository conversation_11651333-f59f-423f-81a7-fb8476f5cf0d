package com.cairh.cpe.component.common.constant;

import com.cairh.cpe.component.common.data.entity.ElecAgreeSign;

/**
 * comp错误号定义，错误号段：110000-119999
 */
public class ErrorConstant {

    /**
     * 视频类异常error_no   110000-110300
     */

    /**
     * 视频最大排队数异常提示
     */
    public final static String VIDEO_MAX_QUEUE_ERROR_NO = "110000";
    /**
     * 该视频见证用户已不在排队队列中
     */
    public final static String VIDEO_USER_OUT_QUEUE_ERROR_NO = "110001";
    /**
     * 该队列不存在（用户video_level 为空）
     */
    public final static String VIDEO_QUEUE_INEXISTENCE_ERROR_NO = "110002";
    /**
     * 该用户信息不存在
     */
    public final static String VIDEO_USER_INEXISTENCE_ERROR_NO = "110003";

    /**
     * 该任务已被其它坐席接通
     */
    public final static String VIDEO_TASK_INEXISTENCE_ERROR_NO = "110004";
    /**
     * 视频任务接通失败
     */
    public final static String VIDEO_TASK_ANSWER_ERROR_NO = "110005";

    /**
     * 视频等待队列为空
     */
    public final static String VIDEO_ALL_QUEUE_INEXISTENCE_ERROR_NO = "110006";
    /**
     * 视频文件下载失败
     */
    public final static String VIDEO_DOWNLOAD_FILE_ERROR = "110007";
/////////////////////////////////////////////////////////////////////////////

    /**
     * 公用异常110301-公用异常110500，业务异常110501-119999
     */

    /////////////////////公用异常//////////////////////////
    /**
     * 保存文件异常
     */
    public final static String SAVE_FILE_ERROR = "110301";

    /**
     * 读取文件异常
     */
    public final static String READ_FILE_ERROR = "110302";

    /**
     * 删除文件异常
     */
    public final static String DELETE_FILE_ERROR = "110303";

    /**
     * 相同记录已存在
     */
    public final static String SAME_DATA_ALREADY_EXISTS = "110304";

    /**
     * 数据不存在
     */
    public final static String DATA_NOT_EXIST = "110305";

    /**
     * PDF生成异常
     */
    public final static String PDF_GENERATE_ERRRO = "110306";

    /**
     * HTML生成异常
     */
    public final static String HTML_GENERATE_ERRRO = "110307";

    /**
     * 序列化异常
     */
    public final static String SERIALIZATION_ERROR = "110308";

    /**
     * 文件目录不存在
     */
    public final static String FILE_DIRECTORY_NOT_EXIST = "110309";

    /**
     * 下载文件异常
     */
    public final static String DOWNLOAD_FILE_ERROR = "110310";

    /**
     * 文件类型不是pdf
     */
    public final static String FILE_TYPE_NOT_PDF = "110311";

    /**
     * 获取文件异常
     */
    public final static String GET_FILE_ERROR = "110312";

    /**
     * 上传文件异常
     */
    public final static String UPLOAD_FILE_ERROR = "110313";

    /**
     * 协议签署记录不存在
     */
    public final static String ELECAGREESIGN_NOT_EXIST = "110314";

    /**
     * 协议模板不存在
     */
    public final static String ELECAGREEMODEL_NOT_EXIST = "110315";

    /**
     * 三方文件归档失败
     */
    public final static String ARCHIVE_THIRD_FILE_FAIL = "110316";

    /**
     * 缺少必要的参数
     */
    public final static String MISSING_NECESSARY_PARAMETERS = "110317";

    //////////////////////////业务异常110501-119999////////////////////////

    /**
     * 模板文件文件格式不支持
     */
    public final static String TEMPLATE_FILE_FORMAT_NOT_SUPPORTED = "110501";

    /**
     * 调用签名接口生成签名pdf文件异常
     */
    public final static String CA_SIGNED_GENERATE_PDF_ERRROR = "110502";

    /**
     * 没有已签署的协议
     */
    public final static String NO_SIGNED_AGREEMENT = "110503";

    /**
     * 已签约的协议模板id为空
     */
    public final static String SIGNED_AGREEMENT_ID_IS_EMPTY = "110504";

    /**
     * 已签约的协议模板类型为空
     */
    public final static String SIGNED_AGREEMENT_TYPE_IS_EMPTY = "110505";

    /**
     * 依据协议模板未查询到对应的本地协议
     */
    public final static String NOT_QUERY_LOCAL_AGREEMENT = "110506";

    /**
     * 绑定本地协议与三方协议时未找到对应的协议模板
     */
    public final static String NOT_QUERY_AGREEMENT_TEMPLATE = "110507";

    /**
     * 签章协议pdf更新失败
     */
    public final static String SIGNATURE_AGREEMENT_PDF_UPDATE_FAILED = "110508";

    /**
     * 提交电子协议签署信息到HsT2系统失败
     */
    public final static String SUBMIT_HST2_FAIL = "110509";


    /**
     * 妥妥递api接口地址为空
     */
    public final static String TTD_API_URL_IS_EMPTY = "110510";

    /**
     * 妥妥递api接口appid为空
     */
    public final static String TTD_API_APPID_IS_EMPTY = "110511";

    /**
     * ttd创建普通投资者异常
     */
    public final static String TTD_API_CREATE_USER_ERRRO = "110512";

    /**
     * 请求妥妥递查询产品接口失败
     */
    public final static String TTD_API_QUERY_PRODUCT_ERROR = "110513";

    /**
     * 请求妥妥递新增订单接口失败
     */
    public final static String TTD_API_ADD_ORDER_ERROR = "110514";

    /**
     * 请求妥妥递订单文件列表接口失败
     */
    public final static String TTD_API_ORDER_FILE_LIST_ERROR = "110515";

    /**
     * 请求妥妥递签署文件查询接口失败
     */
    public final static String TTD_API_SIGN_SERVICE_FILE_SELECT_ERROR = "110516";

    /**
     * 请求妥妥递文件查询接口失败
     */
    public final static String TTD_API_FILE_QUERY_ERROR = "110517";

    /**
     * 请求妥妥递签署文件创建
     */
    public final static String TTD_API_SIGN_SERVICE_FILE_CREATE_ERROR = "110518";

    /**
     * 档案同步BOP时，未找到档案上传配置image_no对应的文件信息
     */
    public final static String SYNCH_BOP_NOT_QUERY_IMAGE_NO = "110519";

    /**
     * 档案同步BOP时，读取image_no对应的文件信息失败
     */
    public final static String SYNCH_BOP_READ_IMAGE_NO_ERROR = "110520";

    /**
     * 第三方协议签署失败
     */
    public final static String THIRD_AGREEMENT_SIGN_FAIL = "110521";

    /**
     * 调703000失败,未获取到scantask_id
     */
    public final static String T2_703000_FAIL = "110522";

    /**
     * 调703014失败,影像服务器设置查询出错
     */
    public final static String T2_703014_FAIL = "110523";

    /**
     * 调321720失败,账户系统参数获取出错
     */
    public final static String T2_321720_FAIL = "110524";

    /**
     * 调703026失败,档案任务流水信息获取获取出错
     */
    public final static String T2_703026_FAIL = "110525";

    /**
     * 调703073失败,档案子任务读取出错
     */
    public final static String T2_703073_FAIL = "110526";

    /**
     * 调709995失败,未获取到档案文件的md5码
     */
    public final static String T2_709995_FAIL = "110527";

    /**
     * 调703041失败,档案任务流水信息获取出错
     */
    public final static String T2_703041_FAIL = "110528";

    /**
     * 营业执照ocr识别错误
     */
    public final static String BUSINESS_LICENSE_OCR_ERROR = "110529";

    /**
     * 营业执照ocr解析错误
     */
    public final static String  BUSINESS_LICENSE_OCR_PARSE_ERROR = "110530";

    /**
     * 服务尝试获取异常
     */
    public final static String SERVICE_SELECT_EXCEPTION = "110543";
}
