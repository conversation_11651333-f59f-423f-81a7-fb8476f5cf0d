package com.cairh.cpe.component.common.form;


import com.cairh.cpe.component.common.constant.Constant;
import com.cairh.cpe.component.common.data.entity.ArchBuinessConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 * @desc 业务档案配置分页查询入参
 * @date 2022-05-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ToString(callSuper = true)
public class ArchbuinessconfigForm extends BasePage<ArchBuinessConfig> {

    private static final long serialVersionUID = -5810250866170056978L;

    /**
     * id
     */
    private String serial_id;

    /**
     * 产品代码
     */
    private String prod_code;

    /**
     * 产品名称
     */
    private String prod_name;

    /**
     * 机构标识
     */
    private String organ_flag;

    /**
     * 状态
     */
    private String status= Constant.COMMON_VALID_STATUS;

    /**
     * 归档文件编号
     */
    private String archfile_no;

    /**
     * 文件大小上限
     */
    private Integer file_size_max;

    /**
     * 限制数量
     */
    private Integer i_limit_amount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 子系统编号
     */
    private Integer subsys_no;

    /**
     * 创建人
     */
    private String create_by;

    /**
     * 创建日期时间
     */
    private Date create_datetime;
}