package com.cairh.cpe.component.common.form;


import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.ArrayList;

/**
 * <AUTHOR>
 * @since 2022-08-22
 */
public class BasePage<T> extends Page<T> {
    {
        OrderItem orderItem = new OrderItem();
        orderItem.setAsc(false);
        orderItem.setColumn("serial_id");
        ArrayList<OrderItem> orderItems = new ArrayList<>();
        orderItems.add(orderItem);
        super.setOrders(orderItems);
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("{");
        sb.append("\"size\":")
                .append(size);
        sb.append(",\"current\":")
                .append(current);
        sb.append(",\"orders\":")
                .append(orders);
        sb.append('}');
        return sb.toString();
    }
}