package com.cairh.cpe.component.common.form;


import com.cairh.cpe.component.common.data.entity.NoticeModelBusiness;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 * @desc
 * @date 2022-05-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ToString(callSuper = true)
public class NoticemodelbusinessForm extends BasePage<NoticeModelBusiness> {

    private static final long serialVersionUID = 3430891123384551292L;

    /**
     * ID
     */
    private String serial_id;

    /**
     * 模板编号
     */
    private String noticemodel_id;

    /**
     * 子系统编号
     */
    private Integer subsys_no;

    /**
     * 业务编号
     */
    private Integer busin_type;

    /**
     * 发送方式
     */
    private String send_type;

    /**
     * 机构标识
     */
    private String organ_flag;

    /**
     * 创建人
     */
    private String create_by;

    /**
     * 修改人
     */
    private String modify_by;

    /**
     * 状态
     */
    private String status;

    /**
     * 规则表达式
     */
    private String regular_expre;
}