package com.cairh.cpe.component.common.data.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.component.common.data.entity.FileRecordSub;
import com.cairh.cpe.component.common.data.mapper.FileRecordSubMapper;
import com.cairh.cpe.component.common.data.service.IFileRecordSubService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class FileRecordSubServiceImpl extends ServiceImpl<FileRecordSubMapper, FileRecordSub> implements IFileRecordSubService {

    @Autowired
    private FileRecordSubMapper fileRecordsubMapper;

    @Override
    public void createFileRecordsub(FileRecordSub fileRecordsub) {
        fileRecordsubMapper.insert(fileRecordsub);
    }
}
