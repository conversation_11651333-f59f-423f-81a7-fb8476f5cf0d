package com.cairh.cpe.component.common.constant;

/**
 * 常量
 *
 * <AUTHOR>
 */
public class Constant {

    //图形验证码类型
    public static final String IMAGE_VALIDATE_CODE_TYPE = "validateCode.type";

    //图形验证码数字类型
    public static final String IMAGE_VALIDATE_CODE_TYPE_NUMBER = "number";

    //图形背景模糊度
    public static final String IMAGE_VALIDATE_CODE_COMPLEXITY = "func.verification.code.complexity";

    //图形背景模糊度-0
    public static final String IMAGE_VALIDATE_CODE_COMPLEXITY_0 = "0";

    //图形背景模糊度-1
    public static final String IMAGE_VALIDATE_CODE_COMPLEXITY_1 = "1";

    //图形背景模糊度-2
    public static final String IMAGE_VALIDATE_CODE_COMPLEXITY_2 = "2";

    //jpeg类型
    public static final String IMAGE_TYPE_JPEG = "jpeg";

    //base64 jpeg前缀
    public static final String IMAGE_BASE64_PREFEX_JPEG = "data:image/jpeg;base64,";

    //SM2私钥名
    public static final String SM2_ENCRYPT_PRVKEY_NAME = "sm2.encrypt.privatekey";

    //默认环境
    public static final String ENVIRONMENT_CONFIG_DEFAULT = "2";

    //券商别名
    public static final String SECURITY_ALIAS = "security.alias";

    public static final String SUCCESS = "成功";

    //通用删除状态
    public static final String COMMON_DELETE_STATUS = "9";
    //通用初始状态
    public static final String COMMON_INIT_STATUS = "0";
    //通用正常状态
    public static final String COMMON_VALID_STATUS = "8";

    //视频组件-redis-队列组
    public static final String COMPONENT_VIDEO_REDIS_QUEUE_GROUP = "{cpe_esb_video}queue_group";

    //视频组件-redis-队列统一前缀
    public static final String COMPONENT_VIDEO_REDIS_VIDEO_QUEUE_UNIFIED_PREFIX = "{cpe_esb_video}video_queue";

    //视频组件-redis-用户统一前缀
    public static final String COMPONENT_VIDEO_REDIS_VIDEO_USER_UNIFIED_PREFIX = "{cpe_esb_video}video_user";

    //视频用户状态
    public static final String COMPONENT_VIDEO_REDIS_VIDEO_USER_STATUS = "status";

    //最近更新时间
    public static final String COMPONENT_VIDEO_REDIS_VIDEO_USER_LAST_UPDATE_STATUS_TIME = "last_status_update_time";

    //视频用户状态 - 已匹配
    public static final String COMPONENT_VIDEO_REDIS_VIDEO_USER_STATUS_MATCHED = "1";

    //视频用户状态 - 视频中
    public static final String COMPONENT_VIDEO_REDIS_VIDEO_USER_STATUS_VIDEOING = "2";

    //视频用户状态 - 视频完成(暂保留)
    public static final String COMPONENT_VIDEO_REDIS_VIDEO_USER_STATUS_VIDEOED = "3";

    //临时文件存放路径配置
    public static final String TEMPLATE_FILE_PATH = "comp.backend.temp.file.path";

    //临时文件存放路径默认值windows
    public static final String WIN_TEMPLATE_FILE_PATH_DEF = "d:/temp/";

    //临时文件存放路径默认值linux
    public static final String LINUX_TEMPLATE_FILE_PATH_DEF = "/temp/";

    //html文件后缀
    public static final String SUFFIX_HTML = ".html";

    //PDF文件后缀
    public static final String SUFFIX_PDF = ".pdf";
    /**话术占位符*/
    //券商占位符
    public static final String KEY_SECURITY_NAME ="SECURITY_NAME";
    //用户名占位符
    public static final String KEY_USER_NAME= "USER_NAME";
    //营业部占位符
    public static final String KEY_ORG_NAME= "ORG_NAME";
    //坐席员工号占位符
    public static final String KEY_EMP_NO ="EMP_NO";
    //坐席名称占位符
    public static final String KEY_EMP_NAME= "EMP_NAME";

    //sql condition 大于等于
    public static final String SQL_CONDITION_GE = "%s>=#{%s}";
    //sql condition 小于等于
    public static final String SQL_CONDITION_LE = "%s<=#{%s}";

    //文件下载地址
    public static final String FILE_DOWNLOAD_URL_PREFFIX = "comp.backend.file.down.url.preffix";
    //视频预览地址
    public static final String VIDEO_PREVIEW_URL_PREFFIX = "comp.backend.video.preview.url.preffix";
    //默认视频预览地址
    public static final String VIDEO_PREVIEW_URL_PREFFIX_DEF = "http://111.229.161.129:10004/comp/filemanage/authless/electPreviewVideo";
    //文件下载地址默认
    public static final String FILE_DOWNLOAD_URL_PREFFIX_DEF = "http://111.229.161.129:10004/comp/filemanage/electDownloadFile";

    //问卷同步服务
    public static final String EXAM_SYNC_SERVICE = "comp.backend.exam.sync.service.provider";
    //默认问卷同步服务
    public static final String EXAM_SYNC_SERVICE_DEF = "t2ExamSyncServiceImpl";
    //试卷类型字典项
    public static final String PAPER_TYPE_DICT_CODE = "paper_type";

    // websocket video menu_id
    public static final String WEBSOCKET_VIDEO_MENUID = "111301";

    //模糊查询条件表达式
    public static final String S_LIKE_CONCAT_CONCAT_S = "%s LIKE CONCAT('%%',CONCAT(#{%s},'%%'))";

    /*新增操作流水类型*/
    public static final Integer BUSINESS_FLAG_ADD = 1101;
    /*修改操作流水类型*/
    public static final Integer BUSINESS_FLAG_MOD = 1102;
    /*删除操作流水类型*/
    public static final Integer BUSINESS_FLAG_DEL = 1103;
    /** 协议发布新版本 **/
    public static final Integer BUSINESS_FLAG_AGREEMENT_NEW_VERSION = 1020;
    /** 协议作废 **/
    public static final Integer BUSINESS_FLAG_AGREEMENT_BAD = 1021;
    /** 审核通过 **/
    public static final Integer BUSINESS_FLAG_VERIFY_PASS = 1002;
    /** 审核驳回 **/
    public static final Integer BUSINESS_FLAG_VERIFY_REJECT = 1003;

    //协议模板抄写
    public static final String  TRANSCRIBE_TRUE = "1";
    //协议模板不抄写
    public static final String  TRANSCRIBE_FALSE = "0";

    public static final String BASE_COMMON_DATABASE_CHARSET = "base.common.database.charset";

    /*协议类型：文件*/
    public static final String AGREEMENT_FILE_TYPE_FILE = "2";

    /**  是否根据image_name命名 1：根据入参image_name命名 **/
    public static final String IS_USE_INCOME = "1";

    /** 档案待审核 **/
    public static final String VERIFY_STATUS_0 = "0";

    /** 档案审核通过 **/
    public static final String VERIFY_STATUS_1 = "1";

    /** 档案审核不通过 **/
    public static final String VERIFY_STATUS_2 = "2";

    /** 审核级别。  0:无，1：审核 **/
    public static final String COMP_AGREEMENT_SIGN_VERIFYLEVEL = "comp.agreement.sign.verifylevel";

    /** 协议模板下载是否加标签 0:否，1：是 **/
    public static final String COMP_AGREEMENT_MODEL_DOWNLOAD_ADDMARK = "comp.agreement.model.download.addmark";

    /** 审核级别 ：无 **/
    public static final String VERIFY_LEVEL_0 = "0";

    /** 审核级别 ：审核 **/
    public static final String VERIFY_LEVEL_1 = "1";

    /** 微信公众号appid **/
    public static final String COMP_NOTICE_WECHAT_APPID = "comp.notice.wechat.appid";

    /** 微信公众号secret **/
    public static final String COMP_NOTICE_WECHAT_SECRET = "comp.notice.wechat.secret";

    /** 微信公众号申请token的url **/
    public static final String COMP_NOTICE_WECHAT_TOKEN_URL = "comp.notice.wechat.token.url";

    /** 微信公众号消息推送url **/
    public static final String COMP_NOTICE_WECHAT_PUT_URL = "comp.notice.wechat.put.url";

    /** 公安认证 **/
    public static final String JOUR_KIND_1 = "1";

    /** 手机短信 **/
    public static final String JOUR_KIND_2 = "2";

    // online user identity, mainly for websocket session regulate
    public static final String ONLINE_USER_IDENTIFIER = "cpe_online_user_identifier";

    /** 基金产品同步服务提供商 **/
    public static final String SYNC_PRODUCT_PROVIDER = "comp.product.sync.service.provider";

    /** 基金产品同步服务提供商-海通证券实现 **/
    public static final String SYNC_PRODUCT_PROVIDER_HTZQ = "htSecSyncFundProductServiceImpl";

    /**
     * 协议签署重签次数
     */
    public static final String COMP_AGREEMENT_SIGN_RETRY_COUNT = "comp.agreement.sign.retry.count";

    /**
     * 协议签署重签天数
     */
    public static final String COMP_AGREEMENT_SIGN_RETRY_DAY = "comp.agreement.sign.retry.day";

    // 坐席离线状态
    public static final String VIDEO_OPERATOR_OFFLINE_STATUS = "5";
}
