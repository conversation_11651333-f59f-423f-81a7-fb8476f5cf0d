package com.cairh.cpe.component.common.support;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.cairh.cpe.component.common.utils.StringUtils;
import com.cairh.cpe.core.autoconfiure.env.CompositePropertySources;
import com.cairh.cpe.component.common.enums.EnableFlagEnum;
import com.cairh.cpe.esb.base.rpc.IVBaseUserInfoDubboService;
import com.cairh.cpe.esb.base.rpc.dto.req.VBaseUserInfoQryRequest;
import com.cairh.cpe.esb.base.rpc.dto.resp.VBaseUserInfoQryResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * 配置类
 *
 * <AUTHOR>
 * @since 2023/7/25 10:08
 */
@Slf4j
@Component
public class ConfigurationSupport {
    /**
     * 是否启动自动分配
     */
    public static final String COMP_VIDEO_AUTO_ALLOCATION_ENABLE = "comp.video.autoAllocation.enable";
    /**
     * 是否自动接听
     */
    public static final String COMP_VIDEO_AUTO_ALLOCATION_AUTO_ANSWER = "comp.video.autoAllocation.auto.answer";
    /**
     * 操作员视频坐席权限设置 （默认是0 全部角色或者 指定角色逗号隔开）
     */
    public static final String COMP_VIDEO_OPERATOR_AUTH= "comp.video.operator.auth";
    /**
     * 坐席接受自动分配任务超时时间(秒)
     */
    public static final String COMP_VIDEO_AUTO_ALLOCATION_TIMEOUT = "comp.video.autoAllocation.timeout";
    /**
     * 是否增加忽略按钮
     */
    public static final String COMP_VIDEO_AUTO_ALLOCATION_IGNORE_ENABLE = "comp.video.autoAllocation.ignore.enable";
    /**
     * 自动分配最大忽略任务次数
     */
    public static final String COMP_VIDEO_AUTO_ALLOCATION_MAX_TIMEOUT_COUNT = "comp.video.autoAllocation.max.timeout.count";
    /**
     * 是否自动排队
     */
    public static final String COMP_VIDEO_AUTO_ALLOCATION_AUTO_QUEUE = "comp.video.autoAllocation.auto.queue";
    /**
     * 自动排队休息时间(秒)
     */
    public static final String COMP_VIDEO_AUTO_ALLOCATION_REST_TIME = "comp.video.autoAllocation.rest.time";
    /**
     * 是否优先分配上一次接入的坐席
     */
    public static final String COMP_VIDEO_AUTO_ALLOCATION_PREVIOUS_OPERATOR = "comp.video.autoAllocation.previous.operator";

    /** 是否仅在分配任务后，才响铃视频提示音 */
    public static final String COMP_VIDEO_AUTO_ALLOCATION_PLAY_SOUND_WHEN_ASSIGNED = "comp.video.autoAllocation.play.sound.when.assigned";

    /**
     * 是否启用智能派单
     */
    public static final String VIDEO_AUTO_ASSIGN = "config.video.autoAssign.flag";

    @Resource
    private CompositePropertySources compositePropertySources;

    @DubboReference(check = false)
    private IVBaseUserInfoDubboService baseUserInfoDubboService;

    /**
     * 校验是否开启视频自动接听
     * @param
     * @return
     */
    public boolean ifVideoAutoAnswer() {
        if (!ifAutoAllocation()) {
            return false;
        }
        return StringUtils.equals(compositePropertySources.getProperty(COMP_VIDEO_AUTO_ALLOCATION_AUTO_ANSWER, EnableFlagEnum.FALSE.getEnableFlag()), "1");
    }

    /**
     * 校验该操作员是否拥有坐席权限
     * @param staffNo
     * @return
     */
    public boolean ifVideoOperationAuth(String staffNo) {
        if (StringUtils.isBlank(staffNo)) {
            return false;
        }
        String property = compositePropertySources.getProperty(COMP_VIDEO_OPERATOR_AUTH, null);
        //默认未配置 即所有员工均可成为 坐席
        if (StringUtils.isBlank(property)) {
            return true;
        }
        //判断设置角色 是否包含该用户
        VBaseUserInfoQryRequest requestDTO = new VBaseUserInfoQryRequest().setStaff_no(staffNo);
        VBaseUserInfoQryResponse vBaseUserInfoQryResponse = baseUserInfoDubboService.baseUserQryUserInfo(requestDTO);
        if (Objects.isNull(vBaseUserInfoQryResponse)) {
            log.debug(String.format("视频坐席角色权限校验，查询员工不存在：员工编号：[%s]",staffNo));
            return false;
        }
        boolean flag = containsAnyElement(
                StringUtils.split(property, StrUtil.COMMA),
                StringUtils.split(vBaseUserInfoQryResponse.getEn_roles(), StrUtil.COMMA));
        if (!flag) {
            log.debug(String.format("视频坐席角色权限校验，暂无权限：员工编号：[%s],[%s]",staffNo,vBaseUserInfoQryResponse.getUser_name()));
        }
        return flag;
    }

    /**
     * 判断数组2 中至少有一个元素存在于 数组1中
     * @param array1
     * @param array2
     * @return
     */
    public static boolean containsAnyElement(String[] array1, String[] array2) {
        Set<String> set1 = new HashSet<>(Arrays.asList(array1));
        Set<String> set2 = new HashSet<>(Arrays.asList(array2));

        for (String element : set2) {
            if (set1.contains(element)) {
                return true;
            }
        }

        return false;
    }
    /**
     * 是否启动自动分配(当智能派单开启时自动分配开启也不生效)
     *
     * @return boolean
     * <AUTHOR>
     * @since 2023/7/25 10:23
     */
    public boolean ifAutoAllocation() {
//        if (ifAutoAssign()) {
//            return false;
//        }
        return StringUtils.equals(compositePropertySources.getProperty(COMP_VIDEO_AUTO_ALLOCATION_ENABLE, EnableFlagEnum.FALSE.getEnableFlag()), "1");
    }

    /**
     * 是否启动智能派单
     */
    public boolean ifAutoAssign() {
        return StringUtils.equals(compositePropertySources.getProperty(VIDEO_AUTO_ASSIGN), "1");
    }

    /**
     * 坐席接受自动分配任务超时时间(秒)
     *
     * @return int
     * <AUTHOR>
     * @since 2023/7/25 10:22
     */
    public int getAutoAllocationTimeout() {
        return Integer.parseInt(compositePropertySources.getProperty(COMP_VIDEO_AUTO_ALLOCATION_TIMEOUT, "5"));
    }

    /**
     * 是否增加忽略按钮
     *
     * @return boolean
     * <AUTHOR>
     * @since 2023/7/25 10:23
     */
    public boolean ifAutoAllocationIgnoreEnable() {
        return StringUtils.equals(compositePropertySources.getProperty(COMP_VIDEO_AUTO_ALLOCATION_IGNORE_ENABLE, EnableFlagEnum.TRUE.getEnableFlag()), "1");
    }

    /**
     * 自动分配最大忽略任务次数
     *
     * @return int
     * <AUTHOR>
     * @since 2023/7/25 10:27
     */
    public int getAutoAllocationMaxTimeoutCount() {
        return Integer.parseInt(compositePropertySources.getProperty(COMP_VIDEO_AUTO_ALLOCATION_MAX_TIMEOUT_COUNT, "0"));
    }

    /**
     * 是否自动排队
     *
     * @return boolean
     * <AUTHOR>
     * @since 2023/7/25 10:43
     */
    public boolean ifAutoAllocationAutoQueue() {
        return StringUtils.equals(compositePropertySources.getProperty(COMP_VIDEO_AUTO_ALLOCATION_AUTO_QUEUE, EnableFlagEnum.FALSE.getEnableFlag()), "1");
    }

    /**
     * 自动排队休息时间(秒)
     *
     * @return long
     * <AUTHOR>
     * @since 2023/7/25 10:45
     */
    public Long getAutoAllocationRestTime() {
        String property = compositePropertySources.getProperty(COMP_VIDEO_AUTO_ALLOCATION_REST_TIME);
        return StringUtils.isBlank(property) ? null : Long.parseLong(property);
    }

    /**
     * 是否优先分配上一次接入的坐席
     *
     * @return boolean
     * <AUTHOR>
     * @since 2023/7/25 10:23
     */
    public boolean ifAutoAllocationPreviousOperator() {
        return StringUtils.equals(compositePropertySources.getProperty(COMP_VIDEO_AUTO_ALLOCATION_PREVIOUS_OPERATOR, EnableFlagEnum.FALSE.getEnableFlag()), "1");
    }

    /**
     * 是否仅在分配任务后，才响铃视频提示音
     *
     * @return boolean
     * <AUTHOR>
     * @since 2023/7/25 18:10
     */
    public boolean ifAutoAllocationPlaySound() {
        return StringUtils.equals(compositePropertySources.getProperty(COMP_VIDEO_AUTO_ALLOCATION_PLAY_SOUND_WHEN_ASSIGNED, EnableFlagEnum.FALSE.getEnableFlag()), "1");
    }

}
