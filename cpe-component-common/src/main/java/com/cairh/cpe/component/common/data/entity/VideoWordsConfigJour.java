package com.cairh.cpe.component.common.data.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 视频话术配置流水
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("Videowordsconfigjour")
public class VideoWordsConfigJour extends Model<VideoWordsConfigJour> {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "serial_id", type = IdType.ASSIGN_UUID)
    private String serial_id;

    /**
     * 视频话术配置id
     */
    @TableField("videowordsconfig_id")
    private String videowordsconfig_id;

    /**
     * 所属视频话术模板id
     */
    @TableField("videowordsmodel_id")
    private String videowordsmodel_id;

    /**
     * 话术类型
     */
    @TableField("words_type")
    private String words_type;

    /**
     * 话术内容
     */
    @TableField("words_content")
    private String words_content;

    /**
     * 状态
     */
    @TableField(value = "status")
    private String status;

    /**
     * 音频文件id
     */
    @TableField("voice_fileid")
    private String voice_fileid;

    /**
     * 排序
     */
    @TableField("order_no")
    private Long order_no;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String create_by;

    /**
     * 创建时间
     */
    @TableField(value = "create_datetime")
    private Date create_datetime;

    /**
     * 修改人
     */
    @TableField("modify_by")
    private String modify_by;

    /**
     * 修改时间
     */
    @TableField(value = "modify_datetime")
    private Date modify_datetime;

    /**
     * 答案黑名单
     */
    @TableField("error_answer")
    private String error_answer;

    /**
     * 答案白名单
     */
    @TableField("correct_answer")
    private String correct_answer;

    /**
     * 业务标识
     */
    @TableField(value = "business_flag")
    private Integer business_flag;

    /**
     * 是否系统播报
     */
    @TableField("auto_play")
    private String auto_play;

    @Override
    public Serializable pkVal() {
        return this.serial_id;
    }

}
