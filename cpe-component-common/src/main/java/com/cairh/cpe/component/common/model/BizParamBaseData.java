package com.cairh.cpe.component.common.model;

import lombok.Data;

@Data
public class BizParamBaseData {

    /**
     * 子系统编号
     */
    private String subsys_no;

    /**
     * 业务名称
     */
    private String busin_name;

    /**
     * 业务类型
     */
    private String busin_type;

    /**
     * 机构标识
     */
    private String organ_flag;

    /**
     * 客户端类型
     */
    private String app_id;

    /**
     * 客户营业部
     */
    private String branch_no;

    /**
     * 客户号
     */
    private String client_id;

    /**
     * 资金账号
     */
    private String fund_account;

    /**
     * 用户名
     */
    private String user_name;

    /**
     * 证件类型
     */
    private String id_kind;

    /**
     * 证件号
     */
    private String id_no;

    /**
     * 手机号
     */
    private String mobile_tel;

    /**
     * 视频类型
     */
    private String video_type;

    /**
     * 视频等级
     */
    private Integer video_level;

    /**
     * 服务提供商
     */
    private String service_vender;

    /**
     * 业务流水号(需保证唯一) Y
     */
    private String unique_id;

    /**
     * 跳转页面(joinQueue时必传,不再由配置取得)
     */
    private String page_addr;

    /**
     * 问卷类型
     */
    private String paper_type;

    /**
     * 问卷子类型
     */
    private String sub_paper_type;

    /**
     * 委托方式
     */
    private String op_entrust_way;

    /**
     * 操作员编号
     */
    private String operator_no;

    /**
     * 站点地址
     */
    private String op_station;

    /**
     * 渠道号
     */
    private String channel_code;

    /**
     * 产品TA编号
     */
    private String prodta_no;

    /**
     * 协议签署是否异步 1-异步，0-同步 默认异步
     */
    private String async_flag;

    /**
     * 手写印章编号
     */
    private String ca_seal_id;

    /**
     * 用户userId
     */
    private String ca_user_id;

    /**
     * 证件开始日期 （金汇厂商必填）
     */
    private String id_begindate;

    /**
     * 证件失效日期 （金汇厂商必填）
     */
    private String id_enddate;

    /**
     * 认证方式 （金汇厂商必填）
     */
    private String auth_type;

    /**
     * 邮箱 （金汇厂商必填）
     */
    private String e_mail;

    /**
     * ip地址 （金汇厂商必填）
     */
    private String ip_address;

    /**
     * mac地址 （金汇厂商必填）
     */
    private String mac_address;

    /**
     * 终端类型 （金汇厂商必填）
     */
    private String terminal_way;

    /**
     * 协议类型
     */
    private String agreement_type;
}
