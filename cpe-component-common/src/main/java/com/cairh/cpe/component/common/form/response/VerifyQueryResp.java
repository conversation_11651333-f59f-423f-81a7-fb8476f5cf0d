package com.cairh.cpe.component.common.form.response;

import com.cairh.cpe.component.common.cache.DataConvert;
import lombok.Data;

import java.io.Serializable;

/**
 * 公安认证查询
 * <AUTHOR>
 * @since 2022-09-02
 */
@Data
public class VerifyQueryResp implements Serializable {

    private static final long serialVersionUID = -5810250866170056970L;


    private String id;

    private String branch_no;

    @DataConvert(code_type = DataConvert.BRANCH)
    private String branch_no_tranfer;

    public String getBranch_no_tranfer() {
        if(branch_no_tranfer == null){
            return branch_no;
        }
        return branch_no_tranfer;
    }

    /**
     * 认证厂商
     */
    private String factory_name;

    @DataConvert(code_type = DataConvert.DICT, code_dict = "factory_name")
    private String factory_name_tranfer;

    public String getFactory_name_tranfer() {
        if(factory_name_tranfer == null){
            return factory_name;
        }
        return factory_name_tranfer;
    }


    /**
     * 认证日期
     */
    //@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private String auth_datetime;

    /**
     * 调用次数
     */
    private String total_num;

    /**
     * 计费次数
     */
    private String total_success;

    /**
     * 认证厂商中文
     */
    private String auth_config;

}