package com.cairh.cpe.component.common.utils;

import lombok.Data;

@Data
public class ServiceWrap<T> {
    private T service;
    private String serviceVender;


    public static <T> ServiceWrap<T> wrap(final T service, String serviceVender) {
        ServiceWrap<T> wrap = new ServiceWrap<>();
        wrap.setService(service);
        wrap.setServiceVender(serviceVender);
        return wrap;
    }
}
