package com.cairh.cpe.component.common.utils;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.cairh.cpe.core.autoconfiure.env.CompositePropertySources;
import com.cairh.cpe.component.common.constant.Constant;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.io.File;
import java.time.LocalDate;
import java.util.Date;
import java.util.Locale;

/**
 * <AUTHOR>
 * @since 2022-05-30
 */
public class FileUtil {

    private static Boolean IS_OS_WINDOWS;


    public static boolean isWindows() {
        if (IS_OS_WINDOWS == null) {
            IS_OS_WINDOWS = System.getProperty("os.name", "").toLowerCase(Locale.US).contains("win");
        }
        return IS_OS_WINDOWS;
    }

    public static String getTempPath() {
        CompositePropertySources compositePropertySources = SpringUtil.getBean(CompositePropertySources.class);
        String tempPath = compositePropertySources.getProperty(Constant.TEMPLATE_FILE_PATH);
        if (StringUtils.isBlank(tempPath)) {
            if (isWindows()) {
                tempPath = Constant.WIN_TEMPLATE_FILE_PATH_DEF;
            } else {
                tempPath = Constant.LINUX_TEMPLATE_FILE_PATH_DEF;
            }
        }
        if (!tempPath.endsWith("/") && !tempPath.endsWith("\\")) {
            tempPath = tempPath + "/";
        }
        tempPath = tempPath + String.format("temp/%s/", LocalDateTimeUtil.format(LocalDate.now(), DatePattern.PURE_DATE_FORMATTER));
        File tempFile = new File(tempPath);
        if (!tempFile.exists()) {
            tempFile.mkdirs();
        }
        return tempPath;
    }

    /**
     * 路径读取
     *
     * @param dateParm   时间
     * @param sourcePath 路径
     * @return
     */
    public static String getFilPath(Date dateParm, String sourcePath) {
        Date date = new Date();
        if (dateParm != null) {
            date = dateParm;
        }
        sourcePath = sourcePath.replace("/yyyyMMdd/", "/" + DateFormatUtils.format(date, "yyyyMMdd") + "/");
        sourcePath = sourcePath.replace("/", File.separator);
        return sourcePath;
    }
}