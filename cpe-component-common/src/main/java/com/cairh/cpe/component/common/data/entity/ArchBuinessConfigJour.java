package com.cairh.cpe.component.common.data.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.cairh.cpe.component.common.constant.Constant;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <p>
 * 业务档案配置流水
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("archbuinessconfigjour")
public class ArchBuinessConfigJour extends Model<ArchBuinessConfigJour> {

    /**
     * id
     */
    @TableId("serial_id")
    private String serial_id;

    /**
     * 业务档案配置id
     */
    @TableField("archbuinessconfig_id")
    private String archbuinessconfig_id;

    /**
     * 产品代码
     */
    @TableField("prod_code")
    private String prod_code;

    /**
     * 产品名称
     */
    @TableField(value = "prod_name",condition = Constant.S_LIKE_CONCAT_CONCAT_S)
    private String prod_name;

    /**
     * 机构标识
     */
    @TableField("organ_flag")
    private String organ_flag;

    /**
     * 状态
     */
    @TableField(value = "status")
    private String status;

    /**
     * 归档文件编号
     */
    @TableField(value = "archfile_no")
    private String archfile_no;

    /**
     * 文件大小上限
     */
    @TableField(value = "file_size_max")
    private Integer file_size_max;

    /**
     * 限制数量
     */
    @TableField(value = "i_limit_amount")
    private Integer i_limit_amount;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 子系统编号
     */
    @TableField("subsys_no")
    private Integer subsys_no;

    /**
     * 创建人
     */
    @TableField(value = "create_by")
    private String create_by;

    /**
     * 创建日期时间
     */
    @TableField(value = "create_datetime")
    private Date create_datetime;
}