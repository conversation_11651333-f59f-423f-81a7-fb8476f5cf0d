package com.cairh.cpe.component.common.data.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.component.common.data.entity.FileRecord;
import com.cairh.cpe.component.common.data.mapper.FileRecordMapper;
import com.cairh.cpe.component.common.data.service.IFileRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class FileRecordServiceImpl extends ServiceImpl<FileRecordMapper, FileRecord> implements IFileRecordService {
    @Autowired
    private FileRecordMapper fileRecordMapper;
    @Override
    public void createFileRecord(FileRecord fileRecord) {
        fileRecord.setStatus("0");
        this.saveOrUpdate(fileRecord);
    }

    @Override
    public String getFilePathById(String id) {
        FileRecord fileRecord = fileRecordMapper.selectById(id);
        if (fileRecord!=null){
            return fileRecord.getFile_path();
        }
        return null;
    }

    @Override
    public FileRecord getFileRecordById(String id) {
        return fileRecordMapper.selectById(id);
    }
}
