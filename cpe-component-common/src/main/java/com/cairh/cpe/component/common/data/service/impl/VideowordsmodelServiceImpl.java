package com.cairh.cpe.component.common.data.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.component.common.utils.BaseBeanUtil;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.context.constant.ErrorCode;
import com.cairh.cpe.context.expression.ExpressionParser;
import com.cairh.cpe.component.common.constant.Constant;
import com.cairh.cpe.component.common.data.entity.VideoWordsBusiness;
import com.cairh.cpe.component.common.data.entity.VideoWordsConfig;
import com.cairh.cpe.component.common.data.entity.VideoWordsConfigJour;
import com.cairh.cpe.component.common.data.entity.VideoWordsModel;
import com.cairh.cpe.component.common.data.entity.VideoWordsModelJour;
import com.cairh.cpe.component.common.data.mapper.VideowordsmodelMapper;
import com.cairh.cpe.component.common.data.service.IVideoWordsBusinessService;
import com.cairh.cpe.component.common.data.service.IVideoWordsConfigService;
import com.cairh.cpe.component.common.data.service.IVideoWordsConfigJourService;
import com.cairh.cpe.component.common.data.service.IVideoWordsModelService;
import com.cairh.cpe.component.common.form.QueryAndReplaceForm;
import com.cairh.cpe.component.common.form.VideowordsmodelDto;
import com.cairh.cpe.component.common.utils.JourUtil;
import com.cairh.cpe.component.common.utils.SeqUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringSubstitutor;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 视频话术模板表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-11
 */
@Slf4j
@Service
public class VideowordsmodelServiceImpl extends ServiceImpl<VideowordsmodelMapper, VideoWordsModel> implements IVideoWordsModelService {

    @Autowired
    private ExpressionParser expressionParser;

    @Autowired
    private IVideoWordsConfigService videowordsconfigService;

    @Autowired
    private VideowordsmodelMapper videowordsmodelMapper;

    @Autowired
    private IVideoWordsConfigJourService videowordsconfigjourService;

    @Autowired
    private IVideoWordsBusinessService videowordsbusinessService;

    @Autowired
    private RedisTemplate<Object, Object> redisTemplate;

    @Override
    public void baseSave(BaseUser baseUser, VideowordsmodelDto entity) {
        if (CollectionUtils.isEmpty(entity.getVideowordsconfigList())) {
            throw new BizException(ErrorCode.ERR_PARAM_IN_ERROR, "请为模板添加话术内容");
        }
        String[] split = entity.getBusin_type().split(",");
        List<String> businTypeList = Arrays.asList(split);
        entity.setBusin_type(null);
        //循环创建多个
        businTypeList.stream().forEach(businType->{
            entity.setBusin_type(businType);
            String staff_no = baseUser.getStaff_no();
            VideoWordsModel videowordsmodel = BaseBeanUtil.copyProperties(entity, VideoWordsModel.class);
            videowordsmodel.setSerial_id(SeqUtil.getSeqCode());
            videowordsmodel.setCreate_by(staff_no);
            videowordsmodel.setModify_by(staff_no);
            List<VideoWordsConfig> videowordsconfigList = entity.getVideowordsconfigList();
            VideowordsmodelServiceImpl service = (VideowordsmodelServiceImpl) AopContext.currentProxy();
            service.saveToDb(videowordsmodel, videowordsconfigList);
            JourUtil.writeJour(() -> writeJour(videowordsconfigList, videowordsmodel, false, Constant.BUSINESS_FLAG_ADD));
        });

    }

    boolean writeJour(List<VideoWordsConfig> videowordsconfigList, VideoWordsModel videowordsmodel, boolean isUpdate, Integer business_flag) {
        List<VideoWordsConfigJour> videowordsconfigjourList = null;
        if (isUpdate) {
            List<String> idlist = videowordsconfigList.stream().map(x -> x.getSerial_id()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(idlist)) {
                List<VideoWordsConfig> list = videowordsconfigService.listByIds(idlist);
                videowordsconfigjourList = BaseBeanUtil.copyToList(list, VideoWordsConfigJour.class);
            }
        } else {
            videowordsconfigjourList =
                    BaseBeanUtil.copyToList(videowordsconfigList, VideoWordsConfigJour.class);
        }
        if (CollectionUtils.isNotEmpty(videowordsconfigjourList)) {
            videowordsconfigjourList.forEach(x -> {
                x.setVideowordsconfig_id(x.getSerial_id());
                x.setSerial_id(null);
                x.setBusiness_flag(business_flag);
            });
        }
        videowordsconfigjourService.saveBatch(videowordsconfigjourList);
        VideoWordsModelJour videowordsmodeljour = BaseBeanUtil.copyProperties(videowordsmodel, VideoWordsModelJour.class);
        videowordsmodeljour.setSerial_id(null);
        videowordsmodeljour.setCreate_by(videowordsmodel.getModify_by());
        videowordsmodeljour.setBusiness_flag(business_flag);
        videowordsmodeljour.insert();
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void baseUpdate(BaseUser baseUser, VideowordsmodelDto entity) {
        if (CollectionUtils.isEmpty(entity.getVideowordsconfigList())) {
            throw new BizException(ErrorCode.ERR_PARAM_IN_ERROR, "请为模板添加话术内容");
        }
        String staff_no = baseUser.getStaff_no();
        String modelId = entity.getSerial_id();
        if (StringUtils.isBlank(modelId)) {
            //新增
            baseSave(baseUser, entity);
            return;
        }
        List<VideoWordsConfig> videowordsconfigList = entity.getVideowordsconfigList();
        List<String> serialIdList = videowordsconfigList.stream().filter(x -> StringUtils.isNotBlank(x.getSerial_id()))
                .map(VideoWordsConfig::getSerial_id).collect(Collectors.toList());
        VideoWordsModel videowordsmodel = BaseBeanUtil.copyProperties(entity, VideoWordsModel.class);
        //删除
        if (CollectionUtils.isNotEmpty(serialIdList)) {
            videowordsconfigService.remove(new QueryWrapper<VideoWordsConfig>().lambda()
                    .eq(VideoWordsConfig::getVideowordsmodel_id, modelId)
                    .notIn(VideoWordsConfig::getSerial_id, serialIdList));
        }
        for (int i = 0; i < videowordsconfigList.size(); i++) {
            VideoWordsConfig videowordsconfig = videowordsconfigList.get(i);
            videowordsconfig.setOrder_no(Long.valueOf(i));
            videowordsconfig.setModify_by(staff_no);
            if (StringUtils.isBlank(videowordsconfig.getSerial_id())) {
                videowordsconfig.setCreate_by(staff_no);
                videowordsconfig.setVideowordsmodel_id(modelId);
            }

        }
        videowordsconfigService.saveOrUpdateBatch(videowordsconfigList);
        videowordsmodel.updateById();
        JourUtil.writeJour(() -> writeJour(videowordsconfigList, videowordsmodel, true, Constant.BUSINESS_FLAG_MOD));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void baseDelete(String serial_id) {
        removeById(serial_id);
        videowordsconfigService.remove(new QueryWrapper<VideoWordsConfig>().lambda()
                .eq(VideoWordsConfig::getVideowordsmodel_id, serial_id));
    }

    @Override
    public List<VideowordsmodelDto> queryAndReplace(BaseUser baseUser, QueryAndReplaceForm form) {
        List<VideowordsmodelDto> resultList = new ArrayList<>();
        VideoWordsBusiness videowordsbusiness = BaseBeanUtil.copyProperties(form, VideoWordsBusiness.class);
        List<VideoWordsBusiness> list = videowordsbusinessService.list(new QueryWrapper<>(videowordsbusiness));
        Map<String, Object> params = parse_replace_str(form.getRegular_data());
        if (CollectionUtils.isEmpty(list)) {
            return resultList;
        }
        //规则表达式过滤
        List<String> model_id_list = list.stream()
                .filter(x -> {
                            String regularExpre = x.getRegular_expre();
                            if (StringUtils.isBlank(regularExpre)) {
                                return Boolean.TRUE;
                            }
                            return params.isEmpty() || expressionParser.parse(params, regularExpre, Boolean.class);
                        }
                ).map(VideoWordsBusiness::getVideowordsmodel_id)
                .collect(Collectors.toList());
        if (StringUtils.isNotBlank(form.getVideo_type())) {
            List<VideoWordsModel> videowordsmodelList = videowordsmodelMapper.selectList(
                    new QueryWrapper<VideoWordsModel>().in("serial_id", model_id_list).eq("video_type", form.getVideo_type())
            );
            model_id_list = videowordsmodelList.stream().map(x -> x.getSerial_id()).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(model_id_list)) {
            return resultList;
        }
        resultList = baseMapper.queryModelAndConfig(form.getVideo_type(), model_id_list);
        Map<Object, Object> videoMap = redisTemplate.opsForHash().entries(Constant.COMPONENT_VIDEO_REDIS_VIDEO_USER_UNIFIED_PREFIX + form.getSubsys_no() + form.getUnique_id());
        log.info("获取到redis用户信息{}",videoMap);
        Map<String, String> replaceMap = videoMap.entrySet().stream()
                .collect(Collectors.toMap(e -> String.valueOf(e.getKey()), e -> String.valueOf(e.getValue())));
        replaceMap.put("emp_name", baseUser.getUser_name());
        log.info("处理后用户信息用户信息{}",replaceMap);
        StringSubstitutor stringSubstitutor = new StringSubstitutor(replaceMap);
        resultList.forEach(x -> {
            if (StringUtils.isNotBlank(x.getClient_tips_words())){
                x.setClient_tips_words(stringSubstitutor.replace(x.getClient_tips_words()));
            }
            if (StringUtils.isNotBlank(x.getSend_msg_words())){
                x.setSend_msg_words((stringSubstitutor.replace(x.getSend_msg_words())));
            }
            List<VideoWordsConfig> videowordsconfigList = x.getVideowordsconfigList();
            if (CollectionUtil.isNotEmpty(videowordsconfigList)) {
                videowordsconfigList.forEach(videowordsconfig -> {
                    videowordsconfig.setWords_content(stringSubstitutor.replace(videowordsconfig.getWords_content()));
                });
            }
        });
        return resultList;
    }

    @Override
    public VideowordsmodelDto queryOne(BaseUser baseUser, String serial_id) {
        VideoWordsModel videowordsmodel = this.getById(serial_id);
        List<VideoWordsConfig> videowordsconfigList = videowordsconfigService.lambdaQuery()
                .eq(VideoWordsConfig::getVideowordsmodel_id, serial_id)
                .orderByAsc(VideoWordsConfig::getOrder_no)
                .list();
        VideowordsmodelDto videowordsmodelDto = BaseBeanUtil.copyProperties(videowordsmodel, VideowordsmodelDto.class);
        videowordsmodelDto.setVideowordsconfigList(videowordsconfigList);
        return videowordsmodelDto;
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveToDb(VideoWordsModel videowordsmodel, List<VideoWordsConfig> videowordsconfigList) {
        videowordsmodel.insert();
        for (int i = 0; i < videowordsconfigList.size(); i++) {
            VideoWordsConfig videowordsconfig = videowordsconfigList.get(i);
            videowordsconfig.setCreate_by(videowordsmodel.getCreate_by());
            videowordsconfig.setModify_by(videowordsmodel.getModify_by());
            videowordsconfig.setVideowordsmodel_id(videowordsmodel.getSerial_id());
            videowordsconfig.setOrder_no(Long.valueOf(i));
            videowordsconfig.setSerial_id(null);
        }
        videowordsconfigService.saveBatch(videowordsconfigList);
    }

    public static Map<String, Object> parse_replace_str(String replace_str) {
        Map<String, Object> params = new HashMap<String, Object>();
        if (StringUtils.isNotBlank(replace_str)) {
            try {
                replace_str = replace_str.replace("“", "\"").replace("”", "\"");
                params = JSONObject.parseObject(replace_str, Map.class);

            } catch (Exception e) {
                log.error("json格式不正确：" + e.getMessage(), e);
                Map<String, String> busin_param = new HashMap<String, String>();
                busin_param.put("replace_str", replace_str);
                throw new BizException(ErrorCode.ERR_UNKNOWN, "json格式不正确：" + busin_param.toString(), e);
            }
        }
        return params;
    }
}
