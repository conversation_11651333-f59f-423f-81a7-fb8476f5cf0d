package com.cairh.cpe.component.common.data.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.component.common.constant.Constant;
import com.cairh.cpe.component.common.data.entity.ExamResultComment;
import com.cairh.cpe.component.common.data.entity.ExamResultCommentJour;
import com.cairh.cpe.component.common.data.mapper.ExamResultCommentMapper;
import com.cairh.cpe.component.common.data.service.IExamResultCommentJourService;
import com.cairh.cpe.component.common.data.service.IExamResultCommentService;
import com.cairh.cpe.component.common.utils.BaseBeanUtil;
import com.cairh.cpe.component.common.utils.JourUtil;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.context.constant.ErrorCode;
import com.cairh.cpe.util.math.BigDecimalUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 问卷评级规则 服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ExamResultCommentServiceImpl extends ServiceImpl<ExamResultCommentMapper, ExamResultComment> implements IExamResultCommentService {

    @Autowired
    private IExamResultCommentJourService examresultcommentjourService;

    @Override
    public void baseUpdate(BaseUser baseUser, List<ExamResultComment> examResultComments) {
        List<ExamResultComment> all = this.lambdaQuery().list();
        Map<String, List<ExamResultComment>> resultCommentMap = all.stream().collect(Collectors.groupingBy(ExamResultComment::getExampaper_id));
        for (ExamResultComment entity : examResultComments) {
            if (BigDecimalUtil.le(entity.getMax_score(), entity.getMin_score())) {
                throw new BizException(ErrorCode.ERR_PARAM_IN_ERROR, "分值区间设置不合理！");
            }
            List<ExamResultComment> list = resultCommentMap.get(entity.getExampaper_id());
            if (CollectionUtils.isNotEmpty(list)) {
                for (ExamResultComment examResultComment : list) {
                    if (cross(entity, examResultComment) && !StringUtils.equals(entity.getSerial_id(), examResultComment.getSerial_id())) {
                        throw new BizException(ErrorCode.ERR_PARAM_IN_ERROR, "分值区间存在重叠！");
                    }
                }
            } else {
                list = new ArrayList<>();
            }
            list.add(entity);
            if (entity.getSerial_id() == null) {
                entity.setCreate_by(baseUser.getStaff_no());
            }
            entity.setModify_by(baseUser.getStaff_no());
        }
        this.saveOrUpdateBatch(examResultComments);
        JourUtil.writeJour(() -> saveJour(examResultComments, true, Constant.BUSINESS_FLAG_MOD));
    }

    @Override
    public void baseSave(BaseUser baseUser, List<ExamResultComment> examResultComments) {
        List<ExamResultComment> all = this.lambdaQuery().list();
        Map<String, List<ExamResultComment>> resultCommentMap = all.stream().collect(Collectors.groupingBy(ExamResultComment::getExampaper_id));
        for (ExamResultComment entity : examResultComments) {
            if (StringUtils.isBlank(entity.getExampaper_id())){
                throw new BizException(ErrorCode.ERR_PARAM_IN_ERROR, "请选择要设置的试卷！");
            }
            if (BigDecimalUtil.le(entity.getMax_score(), entity.getMin_score())) {
                throw new BizException(ErrorCode.ERR_PARAM_IN_ERROR, "分值区间设置不合理！");
            }
            List<ExamResultComment> list = resultCommentMap.get(entity.getExampaper_id());
            if (CollectionUtils.isNotEmpty(list)) {
                for (ExamResultComment examResultComment : list) {
                    if (cross(entity, examResultComment)) {
                        throw new BizException(ErrorCode.ERR_PARAM_IN_ERROR, "分值区间存在重叠！");
                    }
                }
            } else {
                list = new ArrayList<>();
            }
            list.add(entity);
            entity.setCreate_by(baseUser.getStaff_no());
            entity.setModify_by(baseUser.getStaff_no());
        }
        this.saveBatch(examResultComments);
        JourUtil.writeJour(() -> saveJour(examResultComments, false, Constant.BUSINESS_FLAG_ADD));


    }

    /**
     * 比较两个区间是否能重叠
     * 左闭右开原则
     *
     * @param entity
     * @param examResultComment
     * @return
     */
    private boolean cross(ExamResultComment entity, ExamResultComment examResultComment) {
        BigDecimal end = Optional.ofNullable(entity.getMax_score()).orElse(BigDecimal.ZERO).min(Optional.ofNullable(examResultComment.getMax_score()).orElse(BigDecimal.ZERO));
        BigDecimal begin = Optional.ofNullable(entity.getMin_score()).orElse(BigDecimal.ZERO).max(Optional.ofNullable(examResultComment.getMin_score()).orElse(BigDecimal.ZERO));
        if (BigDecimalUtil.gt(end, begin)) {
            return true;
        }
        return false;
    }

    private boolean saveJour(List<ExamResultComment> examResultComments, boolean isUpdate, Integer business_flag) {
        List<ExamResultCommentJour> examresultcommentjours = null;
        if (isUpdate) {
            List<String> idlist = examResultComments.stream().map(x -> x.getSerial_id()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(idlist)) {
                List<ExamResultComment> list = listByIds(idlist);
                examresultcommentjours = BaseBeanUtil.copyToList(list, ExamResultCommentJour.class);
            }
        } else {
            examresultcommentjours =
                    BaseBeanUtil.copyToList(examResultComments, ExamResultCommentJour.class);
        }
        if (CollectionUtils.isNotEmpty(examresultcommentjours)) {
            examresultcommentjours.forEach(x -> {
                x.setExamresultcomment_id(x.getSerial_id());
                x.setSerial_id(null);
                x.setBusiness_flag(business_flag);
            });
            examresultcommentjourService.saveBatch(examresultcommentjours);
        }
        return true;
    }

}
