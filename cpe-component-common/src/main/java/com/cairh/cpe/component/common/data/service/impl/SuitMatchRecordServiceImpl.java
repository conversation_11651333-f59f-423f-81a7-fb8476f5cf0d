package com.cairh.cpe.component.common.data.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.component.common.data.entity.SuitMatchRecord;
import com.cairh.cpe.component.common.data.mapper.SuitMatchRecordMapper;
import com.cairh.cpe.component.common.data.service.ISuitMatchRecordService;
import com.cairh.cpe.component.common.utils.StringUtils;
import com.cairh.cpe.esb.base.rpc.IVBaseUserInfoDubboService;
import com.cairh.cpe.esb.base.rpc.dto.req.VBaseUserInfoQryRequest;
import com.cairh.cpe.esb.base.rpc.dto.resp.VBaseUserInfoQryResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
/**
 * <p>
 * 适当性匹配流水 服务实现类
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class SuitMatchRecordServiceImpl extends ServiceImpl<SuitMatchRecordMapper, SuitMatchRecord> implements ISuitMatchRecordService {
    @DubboReference(check = false)
    private IVBaseUserInfoDubboService userInfoDubboService;
    @Override
    public void addInfo(SuitMatchRecord suitMatchRecord) {
        try {
            if(StringUtils.isNotBlank(suitMatchRecord.getOperator_no())) {
                VBaseUserInfoQryRequest requestDTO = new VBaseUserInfoQryRequest().setStaff_no(suitMatchRecord.getOperator_no());
                VBaseUserInfoQryResponse vBaseUserInfoQryResponse = userInfoDubboService.baseUserQryUserInfo(requestDTO);
                suitMatchRecord.setOperator_name(vBaseUserInfoQryResponse.getUser_name());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        super.save(suitMatchRecord);
    }
}
