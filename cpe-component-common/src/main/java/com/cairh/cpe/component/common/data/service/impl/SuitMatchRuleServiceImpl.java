package com.cairh.cpe.component.common.data.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.component.common.utils.BaseBeanUtil;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.context.constant.ErrorCode;
import com.cairh.cpe.component.common.constant.Constant;
import com.cairh.cpe.component.common.data.entity.SuitMatchRule;
import com.cairh.cpe.component.common.data.entity.SuitMatchRuleJour;
import com.cairh.cpe.component.common.data.mapper.SuitMatchRuleMapper;
import com.cairh.cpe.component.common.data.service.ISuitMatchRuleService;
import com.cairh.cpe.component.common.data.service.ISuitMatchRuleJourService;
import com.cairh.cpe.component.common.utils.JourUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 适当性匹配规则 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-23
 */
@Slf4j
@Service
public class SuitMatchRuleServiceImpl extends ServiceImpl<SuitMatchRuleMapper, SuitMatchRule> implements ISuitMatchRuleService {

    @Autowired
    private ISuitMatchRuleJourService suitmatchrulejourService;

    @Override
    public boolean save(SuitMatchRule entity) {
        List<SuitMatchRule> list = this.lambdaQuery()
                .eq(SuitMatchRule::getProd_code, entity.getProd_code())
                .eq(SuitMatchRule::getProdta_no, entity.getProdta_no())
                .eq(SuitMatchRule::getSuit_prop_type, entity.getSuit_prop_type())
                .list();
        String[] split = StringUtils.split(entity.getEn_cust_suit_prop(), ",");
        if (CollectionUtils.isNotEmpty(list)) {
            for (SuitMatchRule suitmatchrule : list) {
                if (StringUtils.equals(entity.getSuit_flag(), suitmatchrule.getSuit_flag())) {
                    throw new BizException(ErrorCode.ERR_PARAM_IN_ERROR, "适当性匹配标志存在重复");
                }
                for (String item : split) {
                    if (("," + suitmatchrule.getEn_cust_suit_prop() + ",").contains("," + item + ",")) {
                        throw new BizException(ErrorCode.ERR_PARAM_IN_ERROR, "允许客户适当性元素存在重复");
                    }
                }
            }
        }
        super.save(entity);
        log.info("保存一条适当性匹配规则成功，serial_id为:{}", entity.getSerial_id());
        JourUtil.writeJour(() -> saveJour(entity, false, Constant.BUSINESS_FLAG_ADD));
        return true;
    }

    private boolean saveJour(SuitMatchRule entity, boolean isUpdate, Integer business_flag) {
        if (isUpdate) {
            entity = getById(entity.getSerial_id());
        }
        SuitMatchRuleJour suitmatchrulejour = BaseBeanUtil.copyProperties(entity.selectById(), SuitMatchRuleJour.class);
        suitmatchrulejour.setSerial_id(null);
        suitmatchrulejour.setSuitmatchrule_id(entity.getSerial_id());
        suitmatchrulejour.setBusiness_flag(business_flag);
        return suitmatchrulejour.insert();
    }

    public boolean saveJour(List<SuitMatchRule> entityList, boolean isUpdate, Integer business_flag) {
        List<SuitMatchRuleJour> suitmatchrulejourList = new ArrayList<>();
        if (isUpdate) {
            List<String> updateList = entityList.stream().map(x -> x.getSerial_id()).collect(Collectors.toList());
            List<SuitMatchRule> insertList = entityList.stream().filter(x -> StringUtils.isBlank(x.getSerial_id())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(updateList)) {
                List<SuitMatchRule> list = listByIds(updateList);
                suitmatchrulejourList.addAll(BaseBeanUtil.copyToList(list, SuitMatchRuleJour.class));
            }
            if (CollectionUtils.isNotEmpty(insertList)) {
                suitmatchrulejourList.addAll(BaseBeanUtil.copyToList(insertList, SuitMatchRuleJour.class));
            }
        } else {
            suitmatchrulejourList = BaseBeanUtil.copyToList(entityList, SuitMatchRuleJour.class);

        }

        if (CollectionUtils.isNotEmpty(suitmatchrulejourList)) {
            suitmatchrulejourList.forEach(x -> {
                x.setSuitmatchrule_id(x.getSerial_id());
                if (StringUtils.isBlank(x.getSerial_id())) {
                    x.setBusiness_flag(Constant.BUSINESS_FLAG_ADD);
                }else {
                    x.setSerial_id(null);
                    x.setBusiness_flag(business_flag);
                }
            });
            suitmatchrulejourService.saveBatch(suitmatchrulejourList);
        }
        return true;

    }

    @Override
    public boolean updateById(SuitMatchRule entity) {
        super.updateById(entity);
        log.info("更新一条适当性匹配规则成功，serial_id为:{}", entity.getSerial_id());
        JourUtil.writeJour(() -> saveJour(entity, true, Constant.BUSINESS_FLAG_MOD));
        return true;
    }


    @Override
    public List<String> saveOrUpdateOrDelBatch(List<SuitMatchRule> entityList) {
        HashMap<String, List<SuitMatchRule>> suitmatchruleListMap = new HashMap<>();
        for (SuitMatchRule entity : entityList) {
            String key = entity.getProd_code() + entity.getProdta_no() + entity.getSuit_prop_type();
            List<SuitMatchRule> checkedList = suitmatchruleListMap.get(key);
            if ((CollectionUtils.isNotEmpty(checkedList))) {
                String[] split = StringUtils.split(entity.getEn_cust_suit_prop(), ",");
                for (SuitMatchRule suitmatchrule : checkedList) {
                    if (StringUtils.equals(entity.getSuit_flag(), suitmatchrule.getSuit_flag())) {
                        if (!StringUtils.equals(entity.getSerial_id(), suitmatchrule.getSerial_id())) {
                            throw new BizException(ErrorCode.ERR_PARAM_IN_ERROR, "适当性匹配标志存在重复");
                        }
                    }
                    for (String item : split) {
                        if (("," + suitmatchrule.getEn_cust_suit_prop() + ",").contains("," + item + ",")) {
                            if (!StringUtils.equals(entity.getSerial_id(), suitmatchrule.getSerial_id())) {
                                throw new BizException(ErrorCode.ERR_PARAM_IN_ERROR, "允许客户适当性元素存在重复");
                            }
                        }
                    }
                }
            }
            if (Objects.isNull(checkedList)) {
                checkedList = new ArrayList<>();
            }
            checkedList.add(entity);
            suitmatchruleListMap.put(key, checkedList);

        }
        String prodCode = entityList.get(0).getProd_code();
        String prodtaNo = entityList.get(0).getProdta_no();
        List<String> serialIdList = entityList.stream().filter(x -> StringUtils.isNotBlank(x.getSerial_id()))
                .map(SuitMatchRule::getSerial_id).collect(Collectors.toList());
        SuitMatchRuleServiceImpl service = (SuitMatchRuleServiceImpl) AopContext.currentProxy();
        service.toDb(entityList, prodCode, prodtaNo, serialIdList);
        return serialIdList;
    }

    @Transactional(rollbackFor = Exception.class)
    public void toDb(List<SuitMatchRule> entityList, String prodCode, String prodtaNo, List<String> serialIdList) {
        if (CollectionUtils.isNotEmpty(serialIdList)) {
            List<SuitMatchRule> list = this.list(new QueryWrapper<SuitMatchRule>().lambda()
                    .eq(SuitMatchRule::getProdta_no, prodtaNo)
                    .eq(SuitMatchRule::getProd_code, prodCode)
                    .notIn(SuitMatchRule::getSerial_id, serialIdList));
            List<String> idList = list.stream().map(x -> x.getSerial_id()).collect(Collectors.toList());
            this.removeByIds(idList);
        }
        this.saveOrUpdateBatch(entityList);
    }

    @Override
    public void baseSaveBatch(List<SuitMatchRule> entityList) {
        HashMap<String, List<SuitMatchRule>> suitmatchruleListMap = new HashMap<>();
        for (SuitMatchRule entity : entityList) {
            String key = entity.getProd_code() + entity.getProdta_no() + entity.getSuit_prop_type();
//            if (!suitmatchruleListMap.containsKey(key)) {
//                List<Suitmatchrule> list = this.lambdaQuery()
//                        .eq(Suitmatchrule::getProd_code, entity.getProd_code())
//                        .eq(Suitmatchrule::getProdta_no, entity.getProdta_no())
//                        .eq(Suitmatchrule::getSuit_prop_type, entity.getSuit_prop_type())
//                        .list();
//                suitmatchruleListMap.put(key, list);
//            }
            List<SuitMatchRule> checkedList = suitmatchruleListMap.get(key);
            if ((CollectionUtils.isNotEmpty(checkedList))) {
                String[] split = StringUtils.split(entity.getEn_cust_suit_prop(), ",");
                for (SuitMatchRule suitmatchrule : checkedList) {
                    if (StringUtils.equals(entity.getSuit_flag(), suitmatchrule.getSuit_flag())) {
                        throw new BizException(ErrorCode.ERR_PARAM_IN_ERROR, "适当性匹配标志存在重复");
                    }
                    for (String item : split) {
                        if (("," + suitmatchrule.getEn_cust_suit_prop() + ",").contains("," + item + ",")) {
                            throw new BizException(ErrorCode.ERR_PARAM_IN_ERROR, "允许客户适当性元素存在重复");
                        }
                    }
                }
            }
            if (Objects.isNull(checkedList)) {
                checkedList = new ArrayList<>();
            }
            checkedList.add(entity);
            suitmatchruleListMap.put(key, checkedList);

        }
        saveBatch(entityList);
        JourUtil.writeJour(() ->
                saveJour(entityList, false, Constant.BUSINESS_FLAG_ADD));

    }
}
