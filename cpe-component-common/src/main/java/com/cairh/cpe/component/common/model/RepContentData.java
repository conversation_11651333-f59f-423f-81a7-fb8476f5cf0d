package com.cairh.cpe.component.common.model;

import lombok.Data;

import java.util.List;

/**
 * Assemblyaccept表 rep_content字段结构
 */
@Data
public class RepContentData {
    /**
     * 协议签署 签署id
     */
    private List<String> agreement_signids;

    /**
     * 视频模块 图片文件编号
     */
    private List<String> video_picture_filerecords;

    /**
     * 视频模块 视频文件编号
     */
    private List<String> video_video_filerecords;

    /**
     * 视频模块 视频评价信息
     */
    private VideoCommentRepContentData video_comment;

    /**
     * 问卷模块 问卷试算结果
     */
    private ExamCaculateExamResponse risk_caculate;
}


