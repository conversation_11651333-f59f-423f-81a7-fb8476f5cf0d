package com.cairh.cpe.component.common.cache.mq;

import com.cairh.cpe.mq.operator.AbstractBroadcastMQOperator;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 更新后进行通知
 * 此topic和网厅公用，都用于接收后更新缓存用
 * 更新字典 ：basedictionary 营业部：allbranch 城市：allcitycode 系统配置：syspropertyconfig 操作员：operatorinfo都会发消息
 * 网厅目前接收后更新，字典 ：basedictionary 营业部：allbranch 城市：allcitycode 系统配置：syspropertyconfig
 * 平台目前接收后更新，字典 ：basedictionary 营业部：allbranch 操作员：operatorinfo
 * <AUTHOR>
 * @date 2023年11月06日 10:46
 */
@Component
public class CacheUpdateMQOperator extends AbstractBroadcastMQOperator<CacheUpdateMQOperator.SendMsg> {


	@Getter
	@Value("${cpe.topic.cacheUpdateBroadcastMQOperator:cpe-mq-cache-update}")
	private String destinationName;

	@Data
	@AllArgsConstructor
	public static class SendMsg {

		// cacheType：字典 ：basedictionary 营业部：allbranch 城市：allcitycode 系统配置：syspropertyconfig 操作员：operatorinfo
		private String cacheType;
	}

}