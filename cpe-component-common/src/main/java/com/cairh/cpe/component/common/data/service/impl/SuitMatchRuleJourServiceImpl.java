package com.cairh.cpe.component.common.data.service.impl;

import com.cairh.cpe.component.common.data.entity.SuitMatchRuleJour;
import com.cairh.cpe.component.common.data.mapper.SuitMatchRuleJourMapper;
import com.cairh.cpe.component.common.data.service.ISuitMatchRuleJourService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 适当性匹配规则 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-23
 */
@Service
public class SuitMatchRuleJourServiceImpl extends ServiceImpl<SuitMatchRuleJourMapper, SuitMatchRuleJour> implements ISuitMatchRuleJourService {

}
