package com.cairh.cpe.component.common.cache.branch;

import com.cairh.cpe.esb.base.rpc.dto.resp.VBaseAllBranchQryResponse;
import com.cairh.cpe.esb.base.rpc.dto.resp.support.Basedictionary;

import java.util.List;

/**
 * <AUTHOR>
 * @since 1.0.0
 */
public interface ICacheBranchService {

    /**
     * 返回营业部
     * @return
     */
    public List<VBaseAllBranchQryResponse> getAllbranchs() ;


        public void refresh();
}
