package com.cairh.cpe.component.common.cache.branch;

import com.cairh.cpe.component.common.cache.ICache;
import com.cairh.cpe.esb.base.rpc.dto.resp.VBaseAllBranchQryResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年01月10日 17:44
 */
@Slf4j
@Component
public class CacheBranch implements ICache {

	@Autowired
	ICacheBranchService cacheBranchService;

	public VBaseAllBranchQryResponse baseDataQryBranch(String branch_no) {
		VBaseAllBranchQryResponse branch = null;
		List<VBaseAllBranchQryResponse> list = cacheBranchService.getAllbranchs();
		for (Iterator<VBaseAllBranchQryResponse> iterator = list.iterator(); iterator.hasNext(); ) {
			VBaseAllBranchQryResponse branchQryResponse = iterator.next();
			if(StringUtils.equals(branchQryResponse.getBranch_no(), branch_no)){
				branch = branchQryResponse;
			}
		}
		return branch ;
	}

	@Override
	public void refresh() {
		cacheBranchService.refresh();
	}
}
