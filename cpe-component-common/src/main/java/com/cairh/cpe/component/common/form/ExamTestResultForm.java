package com.cairh.cpe.component.common.form;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.cairh.cpe.component.common.data.entity.ExamTestResult;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;

/**
 * 客户测评记录菜单分页查询入参
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ToString(callSuper = true)
public class ExamTestResultForm extends BasePage<ExamTestResult>{

    {
        OrderItem orderItem1 = new OrderItem();
        OrderItem orderItem2 = new OrderItem();
        orderItem1.setAsc(false);
        orderItem1.setColumn("submit_datetime");
        orderItem2.setAsc(false);
        orderItem2.setColumn("serial_id");
        ArrayList<OrderItem> orderItems = new ArrayList<>();
        orderItems.add(orderItem1);
        orderItems.add(orderItem2);
        super.setOrders(orderItems);
    }

    /**
     * 时间范围 逗号隔开
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date[] curr_date_range;
    /**
     * 问卷类型
     */
    private String paper_type;

    /**
     * 机构标志
     */
    private String organ_flag;
    /**
     * 问卷名称
     */
    private String paper_name;

    /**
     * 客户号
     */
    private String client_id;

    /**
     * 分数
     */
    private BigDecimal score;
    /**
     * 所属营业部 编号
     */
    private BigDecimal branch_no;

    /**
     * 问卷提交时间 开始
     */
    private String submit_datetime_start;

    /**
     * 问卷提交时间 结束
     */
    private String submit_datetime_end;
}
