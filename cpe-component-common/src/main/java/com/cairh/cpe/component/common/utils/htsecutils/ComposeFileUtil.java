package com.cairh.cpe.component.common.utils.htsecutils;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;

@Slf4j
public class ComposeFileUtil {

    // FFmpeg全路径
    private static final String FFMPEG_PATH_WIN = ComposeFileUtil.class.getClassLoader().getResource("ffmpeg-amd64.exe").getPath();

    private static final String FFMPEG_PATH_LINUX = ComposeFileUtil.class.getClassLoader().getResource("ffmpeg-amd64").getPath();
    
    public static boolean isLinux() {
        String os = System.getProperty("os.name");
        if (os.toLowerCase().startsWith("win")) {
            return false;
        }
        return true;
    }

    private static String ffmpegPath() {
        if (isLinux()) {
            return FFMPEG_PATH_LINUX;
        }
        return FFMPEG_PATH_WIN;
    }

    /**
     * 具体合成视频函数
     *
     * @param videoInputPath 原视频的全路径
     * @param audioInputPath 音频的全路径
     * @param videoOutPath   视频与音频结合之后的视频的路径
     */
    public static void convetor(String videoInputPath, String audioInputPath, String videoOutPath) throws Exception {
        Process process = null;
        InputStream errorStream = null;
        InputStreamReader inputStreamReader = null;
        BufferedReader br = null;
        try {
            if (!isLinux() && videoInputPath.startsWith("/")) {
                videoInputPath = videoInputPath.substring(1);
            }
            // ffmpeg命令
			/*String command = ffmpegPath() + " -i " + videoInputPath + " -i " + audioInputPath
					+ " -c:v copy -c:a aac -strict experimental " + " -map 0:v:0 -map 1:a:0 " + " -y " + videoOutPath;
			*/
            String command = ffmpegPath() + " -i " + videoInputPath + " -i " + audioInputPath + " -b:v 500k "
                    + " -vcodec copy " + videoOutPath;
            process = Runtime.getRuntime().exec(command);
            errorStream = process.getErrorStream();
            inputStreamReader = new InputStreamReader(errorStream);
            br = new BufferedReader(inputStreamReader);
            // 用来收集错误信息的
            String str = "";
            while ((str = br.readLine()) != null) {
                log.error(str);
            }
            process.waitFor();
        } catch (IOException e) {
            log.error("出现io异常", e);
            throw e;
        } finally {
            if (br != null) {
                br.close();
            }
            if (inputStreamReader != null) {
                inputStreamReader.close();
            }
            if (errorStream != null) {
                errorStream.close();
            }
        }
    }


    public static void mp3toMav(String audioInputPath, String audioOutPath) throws Exception {
        Process process = null;
        InputStream errorStream = null;
        InputStreamReader inputStreamReader = null;
        BufferedReader br = null;
        try {
            // -y 表示允许覆盖已经存在的mav文件
            String command = ffmpegPath() + " -i " + audioInputPath + " -y -f wav -ar 8000 " + audioOutPath;
            process = Runtime.getRuntime().exec(command);
            errorStream = process.getErrorStream();
            inputStreamReader = new InputStreamReader(errorStream);
            br = new BufferedReader(inputStreamReader);
            // 用来收集错误信息的
            String str = "";
            while ((str = br.readLine()) != null) {
                log.error(str);
            }
            process.waitFor();
        } catch (IOException e) {
            log.error("出现io异常", e);
            throw e;
        } finally {
            if (br != null) {
                br.close();
            }
            if (inputStreamReader != null) {
                inputStreamReader.close();
            }
            if (errorStream != null) {
                errorStream.close();
            }
        }
    }

    public static void main(String[] args) throws Exception {
        ComposeFileUtil.mp3toMav("D:/winess/2977de36b00e4d1eb52ad76e212d80d0.mp3","D:/winess/2977de36b00e4d1eb52ad76e212d80d0.wav");
    }
}
