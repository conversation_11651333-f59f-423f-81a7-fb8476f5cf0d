package com.cairh.cpe.component.common.utils.htsecutils;

import cn.hutool.extra.spring.SpringUtil;
import com.cairh.cpe.core.autoconfiure.env.CompositePropertySources;
import com.cairh.cpe.protocol.gateway.request.htsec.esb.EsbResponseXmlBean;
import com.cairh.cpe.protocol.gateway.request.htsec.esb.MessageRequestHead;
import com.cairh.cpe.protocol.gateway.request.htsec.zhzx.ZhzxRequestResponseHead;
import com.cairh.cpe.protocol.gateway.request.htsec.zhzx.ZhzxResponseXmlBean;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;


/**
 * @description：海通Esb、账户中心 接口功能服务
 * TODO 这里的配置信息可能要改成取配置文件，后面这块需要改，这里先全部给成默认的
 * @author： wzj
 * @create： 2023/8/21 14:26
 */
@Slf4j
@Component
public class HtSecCommonUtils {

    private static CompositePropertySources compositePropertySources = SpringUtil.getBean(CompositePropertySources.class);

    public static final String SERVER_MAC = compositePropertySources.getProperty("station", "A820660D0E18");
    public static final String SERVER_IP = compositePropertySources.getProperty("ipAddress", "**************");
    public static final String STATION = compositePropertySources.getProperty("station", "A820660D0E18");


    /**
     * ========================海通 后台配置 相关信息====================
     */
    private static final String COSUMERCODE = compositePropertySources.getProperty("COSUMERCODE", "21");
    public static final String SYSTEM_USER = compositePropertySources.getProperty("systemUser", "SYSTEM");
    public static final String BRANCH_CODE = compositePropertySources.getProperty("branchCode", "000");
    public static final String SYSCODE = compositePropertySources.getProperty("sysCode", "090003");//090002 010006 090003
    public static final String APP_CODE = compositePropertySources.getProperty("zhzx_appCode", "WSKH");// 渠道代码 WSKH 网上开户 SJKH-CRH

    /**
     *
     */
    public static final String RES_TYPE = compositePropertySources.getProperty("resType", "5");// 操作来源 5-网开 4-手机


    /**
     * ========================海通 -网厅 -掌厅 相关信息====================
     */


    /**
     * 海通后台获取请求 head
     *
     * @return MessageRequestHead
     */
    public static MessageRequestHead getHtEsbHead() {
        return getHtEsbHead("", "", "", "", "");
    }

    public static MessageRequestHead getHtEsbHead(String consumerCode, String reqSN, String empCode, String branchCode, String mac) {
        consumerCode = StringUtils.isBlank(consumerCode) ? COSUMERCODE : consumerCode;
        empCode = StringUtils.isBlank(empCode) ? SYSTEM_USER : empCode;
        branchCode = StringUtils.isBlank(branchCode) ? BRANCH_CODE : consumerCode;
        mac = StringUtils.isBlank(mac) ? STATION : consumerCode;
        return formatEsbHead(consumerCode, reqSN, empCode, branchCode, mac);
    }


    /**
     * 组装Esb head
     *
     * @return MessageRequestHead
     */
    public static MessageRequestHead formatEsbHead(String consumerCode, String reqSN, String empCode, String branchCode, String mac) {
        /**
         * 请求头
         */
        MessageRequestHead header = new MessageRequestHead();
        header.setConsumerCode(consumerCode);
        //header.setInterfaceCode(interfaceCode);
        header.setReqSN(reqSN);
        header.setEmpCode(empCode);
        header.setBranchCode(branchCode);
        header.setMac(mac);
        return header;
    }


    /**
     * 海通账户中心头获取
     *
     * @return
     */
    public static ZhzxRequestResponseHead getHtZhzxHead() {
        return getHtZhzxHead("", "", "");
    }

    public static ZhzxRequestResponseHead getHtZhzxHead(String openDesc, String sysCode, String resType) {
        openDesc = StringUtils.isBlank(openDesc) ? getTZM(APP_CODE) : openDesc;
        sysCode = StringUtils.isBlank(sysCode) ? SYSCODE : sysCode;
        resType = StringUtils.isBlank(resType) ? RES_TYPE : resType;
        return formatZhzxHead(openDesc, sysCode, resType);
    }


    /**
     * 组装账户中心 head
     *
     * @return MessageRequestHead
     */
    public static ZhzxRequestResponseHead formatZhzxHead(String openDesc, String sysCode, String resType) {
        /**
         * 请求头
         */
        ZhzxRequestResponseHead head = new ZhzxRequestResponseHead();
        head.setOpenDesc(openDesc);
        head.setSysCode(sysCode);
        head.setResType(resType);
        return head;
    }

    /**
     * 营业部转换
     * @param branchCode
     * @return
     */
    public static String convertCounter(String branchCode){
        if(!StringUtils.isBlank(branchCode)  && branchCode.length() >=4 ){
            branchCode = branchCode.substring(branchCode.length() - 3);
        }
        return branchCode;
    }

    /**
     * 取特征码
     */
    public static String getTZM(String czly) {

        String mac = SERVER_MAC;
        mac = mac.replaceAll("\\W", "");
        String ip = SERVER_IP;
        return format(10, czly) + format(16, mac) + format(16, ip);
    }


    public static String format(int length, String str_m) {
        String str = "                                          ";
        String _temp = str_m + str;
        str_m = _temp.toUpperCase().substring(0, length);
        return str_m;
    }

    public static boolean isSucceed(String retCode) {
        if (StringUtils.equals("0", retCode) || StringUtils.equals("0000", retCode)) {
            return true;
        }
        return false;
    }

    /**
     * 判断返回的 response 的信息是否不为空
     * @param esbResponse
     * @return
     */
    public static boolean isResponseSucceed(EsbResponseXmlBean esbResponse) {
        if (Objects.nonNull(esbResponse) && Objects.nonNull(esbResponse.getBody())
                && Objects.nonNull(esbResponse.getBody().getRequestResponse())
                && Objects.nonNull(esbResponse.getBody().getRequestResponse().getResponseBody())) {
            return true;
        }
        return false;
    }

    public static boolean isResponseSucceed(ZhzxResponseXmlBean zhzxResponse) {
        if (Objects.nonNull(zhzxResponse) && Objects.nonNull(zhzxResponse.getResponse())) {
            return true;
        }
        return false;
    }
}
