package com.cairh.cpe.component.common.data.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.component.common.constant.Constant;
import com.cairh.cpe.component.common.data.entity.ArchBuinessConfig;
import com.cairh.cpe.component.common.data.entity.ArchBuinessConfigJour;
import com.cairh.cpe.component.common.data.mapper.ArchbuinessconfigMapper;
import com.cairh.cpe.component.common.data.service.IArchbuinessconfigService;
import com.cairh.cpe.component.common.utils.JourUtil;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 归档业务配置服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-14
 */
@Service
public class ArchbuinessconfigServiceImpl extends ServiceImpl<ArchbuinessconfigMapper, ArchBuinessConfig> implements IArchbuinessconfigService {

    @Override
    public void baseSave(ArchBuinessConfig entity) {
        save(entity);
        JourUtil.writeJour(entity, ArchBuinessConfigJour.class, Constant.BUSINESS_FLAG_ADD,"setArchbuinessconfig_id");
    }

    @Override
    public void baseUpdate(ArchBuinessConfig entity) {
        updateById(entity);
        JourUtil.writeJour(entity, ArchBuinessConfigJour.class, Constant.BUSINESS_FLAG_MOD,"setArchbuinessconfig_id");

    }
}
