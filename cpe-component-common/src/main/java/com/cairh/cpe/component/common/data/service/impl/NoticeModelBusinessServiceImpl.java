package com.cairh.cpe.component.common.data.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.component.common.utils.BaseBeanUtil;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.context.constant.ErrorCode;
import com.cairh.cpe.component.common.constant.Constant;
import com.cairh.cpe.component.common.data.entity.NoticeModelBusiness;
import com.cairh.cpe.component.common.data.entity.NoticeModelBusinessJour;
import com.cairh.cpe.component.common.data.mapper.NoticeModelBusinessMapper;
import com.cairh.cpe.component.common.data.service.INoticeModelBusinessService;
import com.cairh.cpe.component.common.data.service.INoticeModelBusinessJourService;
import com.cairh.cpe.component.common.utils.JourUtil;
import lombok.AllArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 消息模板业务表 服务实现类
 *
 * <AUTHOR>
 * @since 2022-05-07
 */
@Service
@AllArgsConstructor
public class NoticeModelBusinessServiceImpl extends ServiceImpl<NoticeModelBusinessMapper, NoticeModelBusiness> implements INoticeModelBusinessService {

    private final INoticeModelBusinessJourService INoticemodelbusinessjourService;

    @Override
    public NoticeModelBusiness queryByModelId(String noticemodel_id) {
        List<NoticeModelBusiness> noticemodelbusinessList = lambdaQuery().eq(NoticeModelBusiness::getNoticemodel_id, noticemodel_id).list();
        if (CollectionUtils.isEmpty(noticemodelbusinessList)) {
            return new NoticeModelBusiness();
        } else if (noticemodelbusinessList.size() > 1) {
            throw new BizException(ErrorCode.ERR_PARAM_IN_ERROR, "查到多个规则，请联系管理员");
        }
        return noticemodelbusinessList.get(0);
    }

    @Override
    public boolean save(NoticeModelBusiness entity) {
        List<NoticeModelBusiness> noticemodelbusinessList = lambdaQuery().eq(NoticeModelBusiness::getNoticemodel_id, entity.getNoticemodel_id()).list();
        if (CollectionUtils.isNotEmpty(noticemodelbusinessList)) {
            throw new BizException(ErrorCode.ERR_PARAM_IN_ERROR, "该模板已配置规则,请勿重复配置");
        }
        super.save(entity);
        JourUtil.writeJour(() -> saveJour(entity, false, Constant.BUSINESS_FLAG_ADD));
        return true;
    }

    private boolean saveJour(NoticeModelBusiness entity, boolean isUpdate, Integer business_flag) {
        if (isUpdate) {
            entity = getById(entity.getSerial_id());
        }
        NoticeModelBusinessJour noticemodelbusinessjour = getNoticemodelbusinessjour(entity);
        noticemodelbusinessjour.setBusiness_flag(business_flag);
        return INoticemodelbusinessjourService.save(noticemodelbusinessjour);
    }

    private NoticeModelBusinessJour getNoticemodelbusinessjour(NoticeModelBusiness entity) {
        NoticeModelBusinessJour noticemodelbusinessjour = BaseBeanUtil.copyProperties(entity, NoticeModelBusinessJour.class);
        noticemodelbusinessjour.setSerial_id(null);
        noticemodelbusinessjour.setNoticemodelbusiness_id(entity.getSerial_id());
        return noticemodelbusinessjour;
    }

    @Override
    public boolean updateById(NoticeModelBusiness entity) {
        List<NoticeModelBusiness> noticemodelbusinessList = lambdaQuery().eq(NoticeModelBusiness::getNoticemodel_id, entity.getNoticemodel_id()).list();
        if (CollectionUtils.isNotEmpty(noticemodelbusinessList)) {
            for (NoticeModelBusiness noticemodelbusiness : noticemodelbusinessList) {
                if (!StringUtils.equals(noticemodelbusiness.getSerial_id(),entity.getSerial_id())){
                    throw new BizException(ErrorCode.ERR_PARAM_IN_ERROR, "该模板已配置规则,请勿重复配置");
                }
            }
        }
        super.updateById(entity);
        JourUtil.writeJour(() -> saveJour(entity, true, Constant.BUSINESS_FLAG_MOD));
        return true;
    }
}
