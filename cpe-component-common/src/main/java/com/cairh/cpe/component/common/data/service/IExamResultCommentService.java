package com.cairh.cpe.component.common.data.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.component.common.data.entity.ExamResultComment;

import java.util.List;

/**
 * 问卷评级规则 服务
 *
 * <AUTHOR>
 */
public interface IExamResultCommentService extends IService<ExamResultComment> {

    /**
     * 保存问卷评级规则
     * @param baseUser
     * @param examResultComments
     */
    void baseSave(BaseUser baseUser, List<ExamResultComment> examResultComments);

    /**
     * 修改问卷评级规则
     * @param baseUser
     * @param examResultComments
     */
    void baseUpdate(BaseUser baseUser, List<ExamResultComment> examResultComments);
}
