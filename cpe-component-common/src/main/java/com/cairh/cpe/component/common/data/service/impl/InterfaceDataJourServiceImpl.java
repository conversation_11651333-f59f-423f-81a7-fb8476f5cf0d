package com.cairh.cpe.component.common.data.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.component.common.data.entity.FileRecord;
import com.cairh.cpe.component.common.data.entity.InterfaceDataJour;
import com.cairh.cpe.component.common.data.entity.ServiceQualityMonitor;
import com.cairh.cpe.component.common.data.entity.VideoRecord;
import com.cairh.cpe.component.common.data.mapper.FileRecordMapper;
import com.cairh.cpe.component.common.data.mapper.InterfaceDataJourMapper;
import com.cairh.cpe.component.common.data.mapper.ServiceQualityMonitorMapper;
import com.cairh.cpe.component.common.data.mapper.VideoRecordMapper;
import com.cairh.cpe.component.common.data.service.IInterfaceDataJourService;
import com.cairh.cpe.component.common.data.service.IServiceQualityMonitorService;
import com.cairh.cpe.component.common.data.service.IVideoRecordService;
import com.cairh.cpe.component.common.form.AgreemodelbusinessForm;
import com.cairh.cpe.component.common.form.AgreemodelbusinessVo;
import com.cairh.cpe.component.common.form.InterfaceDataJourDisplayForm;
import com.cairh.cpe.component.common.form.InterfaceDataJourDisplayVo;
import com.cairh.cpe.component.common.form.InterfaceDataJourForm;
import com.cairh.cpe.component.common.form.InterfaceDataJourVo;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.context.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class InterfaceDataJourServiceImpl extends ServiceImpl<InterfaceDataJourMapper, InterfaceDataJour> implements IInterfaceDataJourService {

	private static String YYYYMMDD = "yyyyMMdd";

	@Override
	public void saveJour(String jour_kind, int success_num, int fail_num) {
		InterfaceDataJour interfaceDataJour = new InterfaceDataJour();
		interfaceDataJour.setJour_kind(jour_kind);
		interfaceDataJour.setFail_num(fail_num);
		interfaceDataJour.setSuccess_num(success_num);
		interfaceDataJour.setJour_initdate(toInteger(LocalDateTime.now()));
		this.save(interfaceDataJour);
	}

	@Override
	public Page<InterfaceDataJourVo> queryByPage(InterfaceDataJourForm param) {
		return baseMapper.diySelectPage(param);
	}

	@Override
	public List<InterfaceDataJourDisplayVo> datadisplay(InterfaceDataJourDisplayForm param) {
		return baseMapper.datadisplay(param);
	}

	private Integer toInteger(LocalDateTime localDateTime){
		String dateStr=localDateTime.format(DateTimeFormatter.ofPattern(YYYYMMDD));
		return Integer.parseInt(dateStr);
	}
}