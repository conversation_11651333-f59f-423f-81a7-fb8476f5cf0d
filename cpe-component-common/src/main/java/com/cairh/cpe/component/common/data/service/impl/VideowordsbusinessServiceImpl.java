package com.cairh.cpe.component.common.data.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.component.common.data.entity.VideoWordsConfig;
import com.cairh.cpe.component.common.form.QueryVideowordsForm;
import com.cairh.cpe.component.common.utils.BaseBeanUtil;
import com.cairh.cpe.component.common.utils.MessageUtil;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.context.constant.ErrorCode;
import com.cairh.cpe.component.common.constant.Constant;
import com.cairh.cpe.component.common.data.entity.VideoWordsBusiness;
import com.cairh.cpe.component.common.data.entity.VideoWordsBusinessJour;
import com.cairh.cpe.component.common.data.entity.VideoWordsModel;
import com.cairh.cpe.component.common.data.mapper.VideowordsbusinessMapper;
import com.cairh.cpe.component.common.data.mapper.VideowordsmodelMapper;
import com.cairh.cpe.component.common.data.service.IVideoWordsBusinessService;
import com.cairh.cpe.component.common.form.VideowordsbusinessDto;
import com.cairh.cpe.component.common.form.VideowordsbusinessForm;
import com.cairh.cpe.component.common.utils.JourUtil;
import com.cairh.cpe.esb.base.rpc.IVBaseUserInfoDubboService;
import com.cairh.cpe.esb.base.rpc.dto.req.VBaseUserInfoQryRequest;
import com.cairh.cpe.esb.base.rpc.dto.resp.VBaseUserInfoQryResponse;
import com.cairh.cpe.util.lang.SecurityUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 视频话术规则配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-11
 */
@Service
public class VideowordsbusinessServiceImpl extends ServiceImpl<VideowordsbusinessMapper, VideoWordsBusiness> implements IVideoWordsBusinessService {

    @Autowired
    private VideowordsmodelMapper videowordsmodelMapper;

    @DubboReference(check = false, lazy = true)
    private IVBaseUserInfoDubboService baseUserInfoDubboService;

    @Override
    public VideowordsbusinessDto queryOne(String serial_id) {
        VideoWordsBusiness videowordsbusiness = this.getById(serial_id);
        VideoWordsModel videowordsmodel = videowordsmodelMapper.selectById(videowordsbusiness.getVideowordsmodel_id());
        VideowordsbusinessDto videowordsbusinessDto = BaseBeanUtil.copyProperties(videowordsbusiness, VideowordsbusinessDto.class);
        BaseBeanUtil.copyProperties(videowordsmodel, videowordsbusinessDto);
        return videowordsbusinessDto;
    }

    @Override
    public Page<VideowordsbusinessDto> queryByPage(VideowordsbusinessForm param) {
        return baseMapper.diySelectPage(param);
    }

    @Override
    public void baseSave(BaseUser baseUser, VideoWordsBusiness entity) {
        List<VideoWordsBusiness> videowordsbusinessList = lambdaQuery().eq(VideoWordsBusiness::getVideowordsmodel_id, entity.getVideowordsmodel_id())
                .list();
        if (CollectionUtils.isNotEmpty(videowordsbusinessList)) {
            throw new BizException(ErrorCode.ERR_PARAM_IN_ERROR, "该模板已配置规则,请勿重复配置");
        }
        entity.setModify_by(baseUser.getStaff_no());
        entity.setCreate_by(baseUser.getStaff_no());
        this.save(entity);
        JourUtil.writeJour(() -> writeJour(entity, false, Constant.BUSINESS_FLAG_ADD));
    }

    @Override
    public void baseUpdate(BaseUser baseUser, VideoWordsBusiness entity) {
        List<VideoWordsBusiness> videowordsbusinessList = lambdaQuery().eq(VideoWordsBusiness::getVideowordsmodel_id, entity.getVideowordsmodel_id())
                .list();
        if (CollectionUtils.isNotEmpty(videowordsbusinessList)) {
            for (VideoWordsBusiness videowordsbusiness : videowordsbusinessList) {
                if (!StringUtils.equals(videowordsbusiness.getVideowordsmodel_id(), entity.getVideowordsmodel_id())) {
                    throw new BizException(ErrorCode.ERR_PARAM_IN_ERROR, "该模板已配置规则,请勿重复配置");
                }
            }
        }
        entity.setModify_by(baseUser.getStaff_no());
        this.updateById(entity);
        JourUtil.writeJour(() -> writeJour(entity, true, Constant.BUSINESS_FLAG_MOD));
    }

    @Override
    public void baseDelete(BaseUser baseUser, VideoWordsBusiness entity) {
        removeById(entity.getSerial_id());
    }

    @Override
    public VideowordsbusinessDto queryByModelId(String videowordsmodel_id) {
        List<VideoWordsBusiness> videowordsbusinessList = lambdaQuery().eq(VideoWordsBusiness::getVideowordsmodel_id, videowordsmodel_id)
                .list();
        if (CollectionUtils.isEmpty(videowordsbusinessList)) {
            return null;
        } else if (videowordsbusinessList.size() > 1) {
            throw new BizException(ErrorCode.ERR_PARAM_IN_ERROR, "查到多个规则，请联系管理员");
        }
        VideoWordsBusiness videowordsbusiness = videowordsbusinessList.get(0);
        VideoWordsModel videowordsmodel = videowordsmodelMapper.selectById(videowordsbusiness.getVideowordsmodel_id());
        VideowordsbusinessDto videowordsbusinessDto = BaseBeanUtil.copyProperties(videowordsbusiness, VideowordsbusinessDto.class);
        videowordsbusinessDto.setModel_name(videowordsmodel.getModel_name());
        videowordsbusinessDto.setVideo_type(videowordsmodel.getVideo_type());
        videowordsbusinessDto.setModel_type(videowordsmodel.getModel_type());
        return videowordsbusinessDto;
    }

    public boolean writeJour(VideoWordsBusiness entity, boolean isUpdate, Integer business_flag) {
        if (isUpdate) {
            entity = getById(entity.getSerial_id());
        }
        VideoWordsBusinessJour videowordsbusinessjour = BaseBeanUtil.copyProperties(entity, VideoWordsBusinessJour.class);
        videowordsbusinessjour.setSerial_id(null);
        videowordsbusinessjour.setVideowordsbusiness_id(entity.getSerial_id());
        videowordsbusinessjour.setBusiness_flag(business_flag);
        videowordsbusinessjour.insert();
        return true;
    }

    @Override
    public String renderData(String context, Map<String, String> render_data){
        if(StringUtils.isNotBlank(context) && MapUtils.isNotEmpty(render_data)){
            //进行渲染
            //新增 从业人员执业编号EMP_CERT
            try {
                VBaseUserInfoQryRequest request = new VBaseUserInfoQryRequest();
                request.setStaff_no(render_data.get("operator_no"));
                VBaseUserInfoQryResponse vBaseUserInfoQryResponse = baseUserInfoDubboService.baseUserQryUserInfo(request);
                render_data.put("0",vBaseUserInfoQryResponse.getProfession_cert());
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            context = MessageUtil.getMessage(context, render_data);
        }
        return context;
    }

}

