package com.cairh.cpe.component.common.data.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.component.common.constant.ErrorConstant;
import com.cairh.cpe.component.common.data.entity.FileRecord;
import com.cairh.cpe.component.common.data.entity.ServiceQualityMonitor;
import com.cairh.cpe.component.common.data.entity.VideoRecord;
import com.cairh.cpe.component.common.data.mapper.FileRecordMapper;
import com.cairh.cpe.component.common.data.mapper.ServiceQualityMonitorMapper;
import com.cairh.cpe.component.common.data.service.IServiceQualityMonitorService;
import com.cairh.cpe.component.common.data.service.IVideoRecordService;
import com.cairh.cpe.component.common.form.ServiceQualityMonitorForm;
import com.cairh.cpe.component.common.form.VideoRecordQuery;
import com.cairh.cpe.component.common.support.DubboProxyService;
import com.cairh.cpe.component.common.utils.BaseBeanUtil;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.esb.base.rpc.IVBaseAllBranchDubboService;
import com.cairh.cpe.esb.base.rpc.dto.req.VBaseAllBranchQryRequest;
import com.cairh.cpe.esb.base.rpc.dto.req.VBaseDictQryRequest;
import com.cairh.cpe.esb.base.rpc.dto.resp.VBaseAllBranchQryResponse;
import com.cairh.cpe.esb.base.rpc.dto.resp.VBaseDictQryResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

/**
 * 服务质量监控
 */
@Slf4j
@Service
public class ServiceQualityMonitorServiceImpl extends ServiceImpl<ServiceQualityMonitorMapper, ServiceQualityMonitor> implements IServiceQualityMonitorService {

	@Autowired
	private FileRecordMapper fileRecordMapper;


	@Autowired
	private IVideoRecordService videoRecordService;

	@Autowired
	private DubboProxyService dubboProxyService;

	@DubboReference(check = false, lazy = true)
	private IVBaseAllBranchDubboService baseAllBranchDubboService;

	@Override
	public void insert(BaseUser baseUser, ServiceQualityMonitor entity) {
		try{
			String unique_id = entity.getUnique_id();

			VideoRecordQuery param = new VideoRecordQuery();
			param.setUnique_id(unique_id);
			VideoRecord videoRecordQuery = BaseBeanUtil.copyProperties(param, VideoRecord.class);
			QueryWrapper videoRecordQueryWrapper = new QueryWrapper<>(videoRecordQuery);
			param.setSize(1);
			Page pageInfo = videoRecordService.page(param,videoRecordQueryWrapper);
			List<VideoRecord> videoRecordList =  pageInfo.getRecords();
			if(CollectionUtils.isEmpty(videoRecordList)){
				throw new BizException(ErrorConstant.DATA_NOT_EXIST, "视频流水记录不存在,unique_id:" + unique_id);
			}
			VideoRecord videoRecord = videoRecordList.get(0);
			entity.setApp_id(videoRecord.getApp_id());
			entity.setBranch_no(videoRecord.getBranch_no());
			entity.setBusin_type(videoRecord.getBusin_type());
			entity.setBusin_name(videoRecord.getBusin_name());
			entity.setClient_id(videoRecord.getClient_id());
			entity.setBusin_flag(videoRecord.getBusin_flag());
			entity.setFund_account(videoRecord.getFund_account());
			entity.setMobile_tel(videoRecord.getMobile_tel());
			entity.setId_no(videoRecord.getId_no());
			entity.setId_kind(videoRecord.getId_kind());
			entity.setFull_name(videoRecord.getFull_name());
			entity.setOrgan_flag(videoRecord.getOrgan_flag());

			entity.setOperator_name(baseUser.getUser_name());
			entity.setOperator_no(baseUser.getStaff_no());

			entity.setSubsys_no(entity.getSubsys_no());
			String filerecord_id = entity.getFilerecord_id();
			FileRecord fileRecord = fileRecordMapper.selectById(filerecord_id);
			Date update_datetime = fileRecord.getUpdate_datetime();
			entity.setUpload_datetime(update_datetime);

			this.save(entity);
		}
		catch (Exception ex){
			log.info("服务质量监控添加失败，失败原因：", ex);
		}
	}

	@Override
	public Page<ServiceQualityMonitor> queryByPage(BaseUser baseUser, ServiceQualityMonitorForm param) {
		ServiceQualityMonitor serviceQualityMonitor = BaseBeanUtil.copyProperties(param, ServiceQualityMonitor.class);
		QueryWrapper serviceQualityMonitorQueryWrapper = new QueryWrapper<>(serviceQualityMonitor);

		// 获取查询的时间范围，如果入参中有则使用入参中的时间范围，没有则默认查近7天的
		String begin, end;
		String[] queryDateRange = param.getQuery_date_range();
		if (queryDateRange != null) {
			begin = queryDateRange[0];
			end = queryDateRange[1];
		} else {
			Date now = new Date();
			begin = DateUtil.format(DateUtil.offsetDay(now, -7), "yyyy-MM-dd 00:00:00");
			end =  DateUtil.format(now, "yyyy-MM-dd HH:mm:ss");
			// 将实际查询的时间范围反显给前端
			param.setQuery_date_range(new String[]{begin, end});
		}
		serviceQualityMonitorQueryWrapper.apply("create_datetime >= TO_DATE({0},'yyyy-MM-dd HH24:mi:ss')", begin);
		serviceQualityMonitorQueryWrapper.apply("create_datetime <= TO_DATE({0},'yyyy-MM-dd HH24:mi:ss')", end);

		if(StringUtils.isNotBlank(param.getUser_name())){
			serviceQualityMonitorQueryWrapper.like("full_name", param.getUser_name());
		}

		Page pageInfo = this.page(param,serviceQualityMonitorQueryWrapper);
		VBaseDictQryRequest dictQryRequestAppId = new VBaseDictQryRequest();
		dictQryRequestAppId.setDict_code("app_id");
		List<VBaseDictQryResponse> dictQryResponseAppIdList = dubboProxyService.baseDataQryDict(dictQryRequestAppId);

		VBaseDictQryRequest dictQryRequestSubSysNo = new VBaseDictQryRequest();
		dictQryRequestSubSysNo.setDict_code("subsys_no");
		List<VBaseDictQryResponse> dictQryResponseSubSysNoList = dubboProxyService.baseDataQryDict(dictQryRequestSubSysNo);


		List<ServiceQualityMonitor> riskUserInfoList = pageInfo.getRecords();
		for (Iterator<ServiceQualityMonitor> iterator = riskUserInfoList.iterator(); iterator.hasNext(); ) {
			ServiceQualityMonitor monitor = iterator.next();
			String app_id = monitor.getApp_id();
			Integer subsys_no = monitor.getSubsys_no();

			for (Iterator<VBaseDictQryResponse> iterator2 = dictQryResponseAppIdList.iterator(); iterator2.hasNext(); ) {
				VBaseDictQryResponse dict = iterator2.next();
				if(dict.getDict_code().equals("app_id") && dict.getSub_code().equals(app_id)){
					monitor.setApp_id(dict.getSub_name());
					break;
				}
			}

			for (Iterator<VBaseDictQryResponse> iterator3 = dictQryResponseSubSysNoList.iterator(); iterator3.hasNext(); ) {
				VBaseDictQryResponse dict = iterator3.next();
				if(dict.getDict_code().equals("subsys_no") && dict.getSub_code().equals(subsys_no.toString())){
					monitor.setSubsys_no_str(dict.getSub_name());
					break;
				}
			}

			List<VBaseAllBranchQryResponse> vBaseAllBranchQryResponses = baseAllBranchDubboService.baseDataQryAllBranch(new VBaseAllBranchQryRequest().setBranch_no(monitor.getBranch_no()).setStatus("8"));
			monitor.setBranch_no(vBaseAllBranchQryResponses.get(0).getBranch_name());
		}
		return pageInfo;
	}
}