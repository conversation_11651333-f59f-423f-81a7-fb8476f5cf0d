package com.cairh.cpe.component.common.form;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2022-06-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class QueryAndReplaceForm {

    /**
     * id
     */
    private String serial_id;

    /**
     * 业务类型
     */
    private String busin_type;

    /**
     * 系统编号
     */
    private Integer subsys_no;

    /**
     * 机构标志
     */
    private String organ_flag;

    /**
     * 规则
     */
    private String regular_data;

    /**
     * 产品代码
     */
    private String prod_code;

    /**
     * 产品TA编号
     */
    private String prodta_no;


    /**
     *
     */
    private String unique_id;

    /**
     * 视频方式
     */
    private String video_type;



}