package com.cairh.cpe.component.common.utils;

import com.aspose.words.*;
import com.cairh.cpe.context.BizException;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLDecoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AsposeWordUtil {

	private static Logger logger = LoggerFactory.getLogger(AsposeWordUtil.class);

	private static boolean IsLoadLicense = false;


	public static void main(String[] args) {


		Map<String, String> hs = new HashMap<String, String>();
		IsLoadLicense = true;
		replaceAnd2Pdf("D:\\var\\www\\identifyImgs\\uploadimg\\temp\\11.html", "D:\\var\\www\\identifyImgs\\uploadimg\\temp\\11.pdf", hs);
		IsLoadLicense = false;
		if (!getLicense("D:\\project\\idea\\xpe-sisap-arch\\WebCodes\\src\\main\\resources\\license.xml")) {
			return;
		}
		replaceAnd2Pdf("D:\\var\\www\\identifyImgs\\uploadimg\\temp\\11.html", "D:\\var\\www\\identifyImgs\\uploadimg\\temp\\22.pdf", hs);
		replaceAnd2Pdf("D:\\var\\www\\identifyImgs\\uploadimg\\temp\\11.html", "D:\\var\\www\\identifyImgs\\uploadimg\\temp\\33.pdf", hs);

	}
	/**
	 * 删除单个文件
	 * @param sPath 被删除文件的文件名
	 * @return 文件存且删除失败返回false，否则返回true
	 */
	public static boolean deleteFile(String path) throws Exception {
		if (!getLicense()) {
			return false;
		}
		File file = new File(path);
		// 路径为文件且不为空则进行删除
		if (file.exists() && file.isFile()) {
			return file.delete();
		}
		return true;
	}

	/**
	 * 替换占位符，并转成pdf
	 * @param docPath
	 * @param pdfPath
	 * @param dataMap
	 * @return
	 */
	public static void replaceAnd2Pdf(String docPath, String htmlPath, Map<String, String> dataMap) {
		if (!getLicense()) {
			return;
		}
		try {
			Document doc = new Document(docPath);// 新建文档对象

			doReplaceAnd2Pdf(htmlPath, dataMap, doc);
		} catch (Exception e) {
			logger.error("转换PDF发送异常:", e);
			// e.printStackTrace();
		}
	}

	public static void replaceAnd2Pdf(InputStream inputStream, String htmlPath, Map<String, String> dataMap) {
		if (!getLicense()) {
			return;
		}
		try {
			Document doc = new Document(inputStream);// 新建文档对象

			doReplaceAnd2Pdf(htmlPath, dataMap, doc);
		} catch (Exception e) {
			logger.error("转换PDF发送异常:", e);
			// e.printStackTrace();
		}
	}

	/**
	 * 文档转换pdf 包含html doc docx
	 * @param docPath
	 * @param pdfPath
	 * @param dataMap
	 * @return
	 */
	public static void replaceAnd2Pdf(String docPath, String htmlPath) {
		if (!getLicense()) {
			return;
		}
		try {
			Document doc = new Document(docPath);// 新建文档对象
			doc.save(htmlPath, SaveOptions.createSaveOptions(SaveFormat.PDF));
		} catch (Exception e) {
			logger.error("转换PDF发送异常:", e);
			throw new BizException("-1","转换PDF发送异常:" + e.getMessage());
			// e.printStackTrace();
		}
	}

	private static void doReplaceAnd2Pdf(String htmlPath, Map<String, String> dataMap, Document doc) throws Exception {
		NodeCollection shapeCollection = doc.getChildNodes(NodeType.SHAPE, true);// 查询文档中所有形状
		Node[] shapes = shapeCollection.toArray();// 序列化
		DocumentBuilder builder = new DocumentBuilder(doc);// 新建文档节点
		if (shapes.length > 0) {// 如果文档存在图片
			for (Node node : shapes) {
				Shape shape = (Shape)node;
				if (shape.getShapeType() == ShapeType.TEXT_BOX) {// 如果shape类型是TEXT_BOX类型
					try {
						String strText = shape.getText();
						if (strText == null) {
							continue;
						}
						strText = strText.trim();
						// 起始位置和结束位置
						int leftNum = strText.indexOf("${");
						int rightNum = strText.lastIndexOf('}');
						// 如果是占位符
						if (0 <= leftNum && leftNum < rightNum) {
							// 截取中间的内容
							strText = strText.substring(leftNum + 2, rightNum).trim();

							String strValue = null;
							if (dataMap.containsKey(strText)) {
								strValue = dataMap.get(strText).toString();
							}

							if (strValue == null) {
								continue;
							}
							builder.moveTo(shape);// 移动到图片位置
							builder.write(strValue);// 插入替换文本
							shape.remove();// 移除图形
						}
					} catch (Exception e) {
						logger.error("转换pdf发送错误:", e);
					}
				}
			}
			// String extName = fileName.substring(fileName.lastIndexOf("."));
			// String mainName = fileName.substring(0, fileName .lastIndexOf("."));
			// doc.save(filePath + mainName + "_done" + extName);// 保存修改后的文档
			// doc.save(filePath + mainName + "_done" + ".pdf", SaveOptions.createSaveOptions(SaveFormat.PDF));
			// log.info("filename---->" + mainName + "_done" + extName);
		}
		doc.save(htmlPath, SaveOptions.createSaveOptions(SaveFormat.PDF));
	}

	/**
	 * 替换占位符，并转成Html
	 * @param docPath
	 * @param pdfPath
	 * @param dataMap
	 * @return
	 */
	public static void replaceAnd2Html(String docPath, String htmlPath, Map<String, String> dataMap) {
		if (!getLicense()) {
			return;
		}
		try {
			Document doc = new Document(docPath);// 新建文档对象

			doReplaceAnd2Html(htmlPath, dataMap, doc);
		} catch (Exception e) {
			logger.error("转换HTML发送异常:", e);
		}
	}

	public static void replaceAnd2Html(InputStream inputStream, String htmlPath, Map<String, String> dataMap) {
		if (!getLicense()) {
			return;
		}
		try {
			Document doc = new Document(inputStream);// 新建文档对象

			doReplaceAnd2Html(htmlPath, dataMap, doc);
		} catch (Exception e) {
			logger.error("转换HTML发送异常:", e);
		}
	}

	private static void doReplaceAnd2Html(String htmlPath, Map<String, String> dataMap, Document doc) throws Exception {
		NodeCollection shapeCollection = doc.getChildNodes(NodeType.SHAPE, true);// 查询文档中所有形状
		Node[] shapes = shapeCollection.toArray();// 序列化
		DocumentBuilder builder = new DocumentBuilder(doc);// 新建文档节点
		if (shapes.length > 0) {// 如果文档存在图片
			for (Node node : shapes) {
				Shape shape = (Shape) node;
				if (shape.getShapeType() == ShapeType.TEXT_BOX) {// 如果shape类型是TEXT_BOX类型
					try {
						String strText = shape.getText();
						if (strText == null) {
							continue;
						}
						strText = strText.trim();
						// 起始位置和结束位置
						int leftNum = strText.indexOf("${");
						int rightNum = strText.lastIndexOf('}');
						// 如果是占位符
						if (0 <= leftNum && leftNum < rightNum) {
							// 截取中间的内容
							strText = strText.substring(leftNum + 2, rightNum).trim();

							String strValue = null;
							if (dataMap.containsKey(strText)) {
								strValue = dataMap.get(strText).toString();
							}

							if (strValue == null) {
								continue;
							}
							builder.moveTo(shape);// 移动到图片位置
							builder.write(strValue);// 插入替换文本
							shape.remove();// 移除图形
						}
					} catch (Exception e) {
						logger.error("转换HTML发送错误:", e);
					}
				}
			}
			// String imagePath = htmlPath.replace(".html", "") + File.separator;
			// File imagesDir = new File(imagePath);
			// if(!imagesDir.exists())
			// imagesDir.mkdirs();
			// hso.setImagesFolder(imagePath);
			// hso.setFontsFolder(imagePath);
			// hso.setTempFolder(imagePath);
			// hso.setFontsFolder(value);
			// String extName = fileName.substring(fileName.lastIndexOf("."));
			// String mainName = fileName.substring(0, fileName .lastIndexOf("."));
			// doc.save(filePath + mainName + "_done" + extName);// 保存修改后的文档
			// doc.save(filePath + mainName + "_done" + ".pdf", SaveOptions.createSaveOptions(SaveFormat.PDF));
		}
		HtmlSaveOptions hso = new HtmlSaveOptions(SaveFormat.HTML);
		hso.setExportImagesAsBase64(true);
		doc.save(htmlPath, hso);
		// log.info("filename---->" + mainName + "_done" + extName);
		logger.info("转换html成功:" + htmlPath);
	}

	/**
	 * 获取license
	 * @return
	 */
	public static boolean getLicense(String licensePath) {
		boolean result = false;
		if(IsLoadLicense){
			return true;
		}
		try {
			InputStream is = new FileInputStream(licensePath);
			License aposeLic = new License();
			aposeLic.setLicense(is);
			result = true;
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
		}
		IsLoadLicense = result;
		return result;
	}

	/**
	 * 获取license
	 * @return
	 */
	public static boolean getLicense() {
		if (IsLoadLicense) {
			return true;
		}

		try {
			InputStream is = AsposeWordUtil.class.getClassLoader().getResourceAsStream("license.xml");
			if (is != null) {
				com.aspose.words.License aposeLic = new com.aspose.words.License();
				aposeLic.setLicense(is);
				IsLoadLicense = true;
				return true;
			} else {
				logger.error("license.xml 文件不存在！");
			}
		} catch (IOException e) {
			logger.error("An error occurred while loading license.xml: " + e.getMessage(), e);
		} catch (Exception e) {
			logger.error("An unexpected error occurred while loading license.xml: " + e.getMessage(), e);
		}

		return false;
	}
	
	/**
	 * 合并word文档
	 * @param srcDocPaths 待合并文档路径列表
	 * @param desDocPath 合并后文档路径
	 * @return true：成功，false：失败
	 */
	public static boolean mergeDocx(List<String> srcDocPaths,String desDocPath){
		boolean result = false;
		if (!getLicense()) {
			return result;
		}
		if(srcDocPaths ==null || srcDocPaths.isEmpty()){
			logger.error("待合并文档路径列表不能为空");
			return result;
		}
		if(StringUtils.isBlank(desDocPath)){
			logger.error("合并后文档路径不能为空");
			return result;
		}
		try {
			long start = System.currentTimeMillis();
			Document dstDoc = new Document(srcDocPaths.get(0));
			for(int i=1;i<srcDocPaths.size();i++){
				Document srcDoc = new Document(srcDocPaths.get(i));
				srcDoc.getFirstSection().getPageSetup().setSectionStart(SectionStart.NEW_PAGE);
				dstDoc.appendDocument(srcDoc, ImportFormatMode.KEEP_SOURCE_FORMATTING);
			}
			dstDoc.save(desDocPath);
			long end = System.currentTimeMillis();
			logger.info("合并文档{}结束，共耗时{}",desDocPath,(end-start));
			result = true;
		} catch (Exception e) {
			logger.error("合并word发生异常", e);
		}
		return result;
	}
	
	/**
	 * 替换word文档中的占位符
	 * @param docPath 待替换文档
	 * @param dataMap 需要替换的占位符
	 */
	@SuppressWarnings("rawtypes")
	public static boolean replaceByAspose(String docPath,String desDocPath,Map<String, String> dataMap){
		if (!getLicense()) {
			return false;
		}
		try {
			Document doc = new Document(docPath);// 新建文档对象
			NodeCollection shapeCollection = doc.getChildNodes(NodeType.SHAPE, true);// 查询文档中所有形状
			Node[] shapes = shapeCollection.toArray();// 序列化
			DocumentBuilder builder = new DocumentBuilder(doc);// 新建文档节点
			if (shapes.length > 0) {// 如果文档存在图片
				for (Node node : shapes) {
					Shape shape = (Shape)node;
					if (shape.getShapeType() == ShapeType.TEXT_BOX) {// 如果shape类型是TEXT_BOX类型
						try {
							String strText = shape.getText();
							if (strText == null) {
								continue;
							}
							strText = strText.trim();
							// 起始位置和结束位置
							int leftNum = strText.indexOf("${");
							int rightNum = strText.lastIndexOf('}');
							// 如果是占位符
							if (0 <= leftNum && leftNum < rightNum) {
								// 截取中间的内容
								strText = strText.substring(leftNum + 2, rightNum).trim();

								String strValue = null;
								if (dataMap.containsKey(strText)) {
									strValue = dataMap.get(strText).toString();
								}

								if (strValue == null) {
									continue;
								}
								builder.moveTo(shape);// 移动到图片位置
								builder.setBold(true);
								builder.write(strValue);// 插入替换文本
								shape.remove();// 移除图形
							}
						} catch (Exception e) {
							logger.error("转换错误:", e);
							return false;
						}
					}
				}
			}
			doc.save(desDocPath, SaveOptions.createSaveOptions(SaveFormat.DOCX));
		}catch (Exception e) {
			logger.error("转换异常:", e);
			return false;
		}
		return true;
	}
	

	
	public static byte[] readFile(String filePath) {
		// TODO Auto-generated method stub
		FileInputStream in = null;
		byte[] fileContent = null;
		try {
			in = new FileInputStream(filePath);
			fileContent = IOUtils.toByteArray(in);
			IOUtils.closeQuietly(in);
		} catch (Exception e) {
			logger.error("读取文件异常", e);
		} finally {
			if (in != null) {
				try {
					in.close();
				} catch (IOException e) {
					// TODO Auto-generated catch block
					logger.error("关闭流失败", e);
				}
			}
		}
		return fileContent;
	}
}
