package com.cairh.cpe.component.common.form;

import com.cairh.cpe.component.common.data.entity.ElecAgreeModel;
import com.cairh.cpe.component.common.data.entity.ElecAgreeModelSub;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 风险评测变更详情
 */
@EqualsAndHashCode(callSuper = false)
@Data
@ToString(callSuper = true)
public class RiskInfoDetailDto{

    /**
     * 风险评测变更详情子项列表
     */
    private List<RiskInfoDetailSub> resultList;


}