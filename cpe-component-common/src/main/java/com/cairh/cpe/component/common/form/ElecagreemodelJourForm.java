package com.cairh.cpe.component.common.form;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.cairh.cpe.component.common.data.entity.ElecAgreeModel;
import com.cairh.cpe.component.common.data.entity.ElecAgreeModelJour;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.ArrayList;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper=false)
@ToString(callSuper = true)
public class ElecagreemodelJourForm extends BasePage<ElecAgreeModelJour> {

    {
        OrderItem orderItem1 = new OrderItem();
        OrderItem orderItem2 = new OrderItem();
        orderItem1.setAsc(false);
        orderItem2.setAsc(false);
        orderItem1.setColumn("modify_datetime");
        orderItem2.setColumn("serial_id");
        ArrayList<OrderItem> orderItems = new ArrayList<>();
        orderItems.add(orderItem1);
        orderItems.add(orderItem2);
        super.setOrders(orderItems);
    }
    /**
     * 协议模板id
     */
    private String serial_id;

    /**
     * 协议编号
     */
    private String agreement_no;

    /**
     * 协议名称
     */
    private String agreement_name;

    /**
     * 协议版本
     */
    private String agreement_version;

    /**
     * 协议内容
     */
    private String agreement_content;

    /**
     * 协议文件类型
     */
    private String agreement_file_type;

    /**
     * 协议文件ID
     */
    private String agreement_file_id;

    /**
     * 协议状态
     */
    private String agreement_status;

    /**
     * 加密类型
     */
    private String encrypt_type;

    /**
     * 加密值
     */
    private String encrypt_content;

    private String create_by;

    /**
     * 创建日期时间 (申请时间查询条件)
     */
    @JsonFormat(pattern = "yyyyMMdd HHmmss")
    private Date create_datetime;

    private String modify_by;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyyMMdd HHmmss")
    private Date modify_datetime;

    /**
     * 生效日期
     */
    private Date effective_datetime;

    /**
     * 失效日期
     */
    private Date invalid_datetime;

    private String seal_pos;

    private String sign_pos;

    /**
     * 来源标志
     */
    private String source_flag;

    private String ex_param;

    /**
     * 归历史标志
     */
    private String tohis_flag;

    /**
     * 归历史时间
     */
    private Date tohis_datetime;

    /**
     * 协议类型
     */
    private String agreement_type;

    /**
     * 审核状态 0（待审核），1（审核通过），2（审核不通过）
     */
    private String verify_status;

    /**
     * 审核操作人  保存审核人，如审核驳回保存审核驳回人工号
     */
    private String verify_by;

    /**
     * 审核操作时间 保存审核，或驳回时间
     */
    @JsonFormat(pattern = "yyyyMMdd HHmmss")
    private Date verify_datetime;


    /**
     * 拓展名称
     */
    @TableField("ex_name")
    private String ex_name;

    /**
     * 来源标志(外部接入方式)
     */
    @TableField("third_source_type")
    private String third_source_type;

    /**
     * 签署方式
     */
    @TableField("agreement_sign_type")
    private String agreement_sign_type;

    /** 姓名 **/
    private String user_name;

    /** 操作内容 **/
    private String operate_info;

    /** 操作标志 **/
    private Integer business_flag;
}
