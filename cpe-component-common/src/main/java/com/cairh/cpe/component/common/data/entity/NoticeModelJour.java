package com.cairh.cpe.component.common.data.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 消息模板流水表
 *
 * <AUTHOR>
 * @since 2022-05-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("Noticemodeljour")
public class NoticeModelJour extends Model<NoticeModelJour> {

    private static final long serialVersionUID = 2021591235248547236L;

    /**
     * ID
     */
    @TableId(value = "serial_id",type = IdType.ASSIGN_UUID)
    private String serial_id;

    /**
     * 消息模板表ID
     */
    @TableField("noticemodel_id")
    private String noticemodel_id;

    /**
     * 模板编号
     */
    @TableField("model_no")
    private String model_no;

    /**
     * 模板名称
     */
    @TableField("model_name")
    private String model_name;

    /**
     * 模板内容标题
     */
    @TableField("model_title")
    private String model_title;

    /**
     * 模板内容
     */
    @TableField("model_content")
    private String model_content;

    /**
     * 状态
     */
    @TableField(value = "status")
    private String status;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String create_by;

    /**
     * 创建日期时间
     */
    @TableField(value = "create_datetime")
    private Date create_datetime;

    /**
     * 修改人
     */
    @TableField("modify_by")
    private String modify_by;

    /**
     * 修改时间
     */
    @TableField(value = "modify_datetime")
    private Date modify_datetime;

    @TableField(value = "business_flag")
    private Integer business_flag;


    @Override
    public Serializable pkVal() {
        return this.serial_id;
    }

}
