package com.cairh.cpe.component.common.data.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 视频话术模板流水
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("Videowordsmodeljour")
public class VideoWordsModelJour extends Model<VideoWordsModelJour> {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "serial_id", type = IdType.ASSIGN_UUID)
    private String serial_id;

    /**
     * 视频话术模板id
     */
    @TableField("videowordsmodel_id")
    private String videowordsmodel_id;

    /**
     * 模板名称
     */
    @TableField("model_name")
    private String model_name;

    /**
     * 模板类型
     */
    @TableField("model_type")
    private String model_type;

    /**
     * 视频方式
     */
    @TableField("video_type")
    private String video_type;

    /**
     * 状态
     */
    @TableField(value = "status")
    private String status;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String create_by;

    /**
     * 创建时间
     */
    @TableField(value = "create_datetime")
    private Date create_datetime;

    /**
     * 修改人
     */
    @TableField("modify_by")
    private String modify_by;

    /**
     * 修改时间
     */
    @TableField(value = "modify_datetime")
    private Date modify_datetime;

    /**
     * 发送消息话术
     */
    @TableField("send_msg_words")
    private String send_msg_words;

    /**
     * 用户提示话术
     */
    @TableField("client_tips_words")
    private String client_tips_words;

    /**
     * 适用性别
     */
    @TableField("sex_apply")
    private String sex_apply;

    /**
     * 业务标识
     */
    @TableField(value = "business_flag")
    private Integer business_flag;
    /**
     * 播放倍率
     */
    @TableField("play_rate")
    private String play_rate;


    @Override
    public Serializable pkVal() {
        return this.serial_id;
    }

}
