package com.cairh.cpe.component.common.data.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.component.common.data.entity.VideoAssignJour;
import com.cairh.cpe.component.common.data.mapper.VideoAssignJourMapper;
import com.cairh.cpe.component.common.data.service.IVideoAssignJourService;
import com.cairh.cpe.component.common.form.VideoAssignJourForm;
import com.cairh.cpe.component.common.model.VideoAssignJourEntity;
import com.cairh.cpe.component.common.utils.BaseBeanUtil;
import com.cairh.cpe.esb.base.rpc.IVBaseUserInfoDubboService;
import com.cairh.cpe.esb.base.rpc.dto.req.VBaseUserInfoQryRequest;
import com.cairh.cpe.esb.base.rpc.dto.resp.VBaseUserInfoQryResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 功能说明: 视频派单流水表
 * 公司名称: 杭州财人汇网络股份有限公司
 * 开发人员: <EMAIL>
 * 开发时间: 2023-12-28 14:24
 */
@Service
public class VideoAssignJourServiceImpl extends ServiceImpl<VideoAssignJourMapper, VideoAssignJour> implements IVideoAssignJourService {

    @DubboReference(check = false, lazy = true)
    private IVBaseUserInfoDubboService userInfoDubboService;

    @Override
    public Page<VideoAssignJourEntity> queryByPage(VideoAssignJourForm form) {
        Page<VideoAssignJourEntity> result = new Page<>();
        List<String> staffNos = new ArrayList<>();
        if(StringUtils.isNotBlank(form.getStaff_name())){
            Map<String, String> staffMap = userInfoDubboService.queryAllStaffNameAndNo();
            //  遍历staffMap将value中包含form.staffName的key放入到staffNos集合中
            staffMap.forEach((k, v) -> {
                if (v.contains(form.getStaff_name())) {
                    staffNos.add(k);
                }
            });
        }
        VideoAssignJour videoAssignJour = BaseBeanUtil.copyProperties(form, VideoAssignJour.class);
        // 兼容多选处理
        videoAssignJour.setVideo_assign_type(null);
        LambdaQueryWrapper<VideoAssignJour> videoAssignJourQueryWrapper = new LambdaQueryWrapper<>(videoAssignJour);
        // 多选处理
        if (StringUtils.isNotBlank(form.getVideo_assign_type())) {
            videoAssignJourQueryWrapper.in(VideoAssignJour::getVideo_assign_type, Arrays.asList(form.getVideo_assign_type().split(",")));
        }
        // 时间处理
        Date[] curr_date_range = form.getCurr_date_range();
        //判断时间范围，查询条件封装
        if (curr_date_range != null) {
            videoAssignJourQueryWrapper.ge(VideoAssignJour::getCurr_datetime, curr_date_range[0]);
            videoAssignJourQueryWrapper.le(VideoAssignJour::getCurr_datetime, curr_date_range[1]);
        }
        if(CollectionUtils.isNotEmpty(staffNos)){
            videoAssignJourQueryWrapper.in(VideoAssignJour::getStaff_no, staffNos);
        }
        // 判断是否为空，为空则返回
        if(StringUtils.isNotBlank(form.getStaff_name()) && CollectionUtils.isEmpty(staffNos)){
            return result;
        }
        VideoAssignJourForm dataResult = page(form, videoAssignJourQueryWrapper);
        BeanUtils.copyProperties(dataResult, result);

        if (CollectionUtils.isNotEmpty(dataResult.getRecords())) {
            result.setRecords(dataResult.getRecords().stream().map(item -> {
                VideoAssignJourEntity entity = new VideoAssignJourEntity();
                BeanUtils.copyProperties(item, entity);
                VBaseUserInfoQryResponse userInfo = userInfoDubboService.baseUserQryUserInfo(new VBaseUserInfoQryRequest().setStaff_no(item.getStaff_no()));
                if (null != userInfo) {
                    entity.setStaff_name(userInfo.getUser_name());
                }
                return entity;
            }).collect(Collectors.toList()));
        }
        return result;
    }
}
