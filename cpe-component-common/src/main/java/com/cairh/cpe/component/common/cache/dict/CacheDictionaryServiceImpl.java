package com.cairh.cpe.component.common.cache.dict;

import com.cairh.cpe.esb.base.rpc.IVBaseDictDubboService;
import com.cairh.cpe.esb.base.rpc.dto.req.VBaseDictQryRequest;
import com.cairh.cpe.esb.base.rpc.dto.resp.VBaseDictQryResponse;
import com.cairh.cpe.esb.base.rpc.dto.resp.support.Basedictionary;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class CacheDictionaryServiceImpl implements ICacheDictionaryService {

    public final static String ENABLE_STATUS = "8";

    @DubboReference(check = false, lazy = true)
    private IVBaseDictDubboService iBaseDictionaryService;

    /**
     * 返回字典list
     * @return
     */
    @Override
    @Cacheable(value = "compBasedictionaryDB", key = "#root.targetClass.simpleName+'.'+#root.method.name+':'+#dict_code", condition = "#dict_code!='' ")
    public List<Basedictionary> getBasedictionaryList(String dict_code) {
        List<Basedictionary> basedictionaries =
                this.queryBaseDictionaryListByCode(dict_code).stream().sorted(Comparator.comparing(Basedictionary::getOrder_no)).collect(Collectors.toList());
        return basedictionaries;
    }


    private List<Basedictionary> queryBaseDictionaryListByCode(String dictCode) {
        if(StringUtils.isEmpty(dictCode)){
            return new ArrayList<>();
        }
        VBaseDictQryRequest vBaseDictQryRequest = new VBaseDictQryRequest();
        vBaseDictQryRequest.setDict_code(dictCode);
            vBaseDictQryRequest.setStatus(ENABLE_STATUS);
        List<VBaseDictQryResponse> vBaseDictQryResponseList = iBaseDictionaryService.baseDataQryDict(vBaseDictQryRequest);
        return covertList(vBaseDictQryResponseList);
    }

    private List<Basedictionary> covertList(List<VBaseDictQryResponse> vBaseDictQryResponseList){
        List<Basedictionary> resList = new ArrayList<>();
        if(org.apache.commons.collections.CollectionUtils.isNotEmpty(vBaseDictQryResponseList)){
            for(VBaseDictQryResponse item : vBaseDictQryResponseList){
                Basedictionary basedictionary = new Basedictionary();
                basedictionary.setDict_code(item.getDict_code());
                basedictionary.setDict_name(item.getDict_name());
                basedictionary.setSub_code(item.getSub_code());
                basedictionary.setSub_name(item.getSub_name());
                basedictionary.setRemark(item.getRemark());
                if(item.getOrder_no()!=null){
                    basedictionary.setOrder_no((item.getOrder_no()));
                }
                basedictionary.setStatus(item.getStatus());
                resList.add(basedictionary);
            }
        }
        return resList;
    }

    @Scheduled(fixedDelay = 10 * 60 * 1000)
    @CacheEvict(value = "compBasedictionaryDB", allEntries = true)
    public void refresh() {
        log.debug("Cache[compBasedictionaryDB]执行清除.");
    }

}
