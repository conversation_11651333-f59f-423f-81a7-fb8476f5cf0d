package com.cairh.cpe.component.common.form;


import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.cairh.cpe.component.common.data.entity.InterfaceDataJour;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.ArrayList;

@Data
@EqualsAndHashCode(callSuper=false)
@ToString(callSuper = true)
public class InterfaceDataJourDisplayForm extends BasePage<InterfaceDataJour> {
    {
        OrderItem orderItem1 = new OrderItem();
        orderItem1.setAsc(true);
        orderItem1.setColumn("jour_kind");
        ArrayList<OrderItem> orderItems = new ArrayList<>();
        orderItems.add(orderItem1);
        super.setOrders(orderItems);
    }
    /**
     * 开始日期
     */
    private Integer begin_date;

    /**
     * 结束日期
     */
    private Integer end_date;

}
