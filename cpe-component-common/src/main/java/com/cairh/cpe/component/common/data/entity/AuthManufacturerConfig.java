package com.cairh.cpe.component.common.data.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cairh.cpe.component.common.cache.DataConvert;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 高清人脸比对配置
 * </p>
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("AUTHMANUFACTURERCONFIG")
public class AuthManufacturerConfig {
    /**
     * ID
     */
    @TableId(value = "serial_id",type = IdType.ASSIGN_UUID)
    private String serial_id;
    /**
     * 实现名
     */
    @TableField("interface_impl")
    private  String interface_impl;

    //翻译
    @TableField(exist = false)
    @DataConvert(code_type = DataConvert.DICT, code_dict = "factory_name")
    private String interface_impl_tranfer;

    public String getInterface_impl_tranfer() {
        if(interface_impl_tranfer == null){
            return interface_impl;
        }
        return interface_impl_tranfer;
    }

    /**
     * 最低分数
     */
    @TableField("score_start")
    private BigDecimal score_start;
    /**
     * 最高分数
     */
    @TableField("score_end")
    private BigDecimal score_end;
    /**
     * 备注
     */
    @TableField("remark")
    private  String remark;
    /**
     * 修改人
     */
    @TableField("modify_by")
    private String modify_by;

    /**
     * 修改人姓名
     */
    @TableField(exist = false)
    @DataConvert(code_type = DataConvert.OPERATORINFO)
    private String modify_by_tranfer;
    public String getModify_by_tranfer() {
        if(modify_by_tranfer == null){
            return modify_by;
        }
        return modify_by_tranfer;
    }

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @TableField("modify_datetime")
    private Date modify_datetime;

}
