package com.cairh.cpe.component.common.form;

import com.cairh.cpe.component.common.data.entity.ExamQuestion;
import com.cairh.cpe.component.common.data.entity.ExamQuestionOptions;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-07-15
 */
@EqualsAndHashCode(callSuper = false)
@Data
@ToString(callSuper = true)
public class ExamquestionDto extends ExamQuestion {

    /**
     * 选项列表
     */
    List<ExamQuestionOptions> examquestionoptionsList;
}