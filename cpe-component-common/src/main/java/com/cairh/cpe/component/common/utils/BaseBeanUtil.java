package com.cairh.cpe.component.common.utils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ReflectUtil;
import org.springframework.beans.BeanUtils;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * bean relate
 *
 * <AUTHOR>
 */
public class BaseBeanUtil extends BeanUtils {

    /**
     * cast bean to map
     *
     * @param bean baseBean
     * @return map, ignore null value
     */
    public static Map<String, Object> beanToMap(Object bean) {
        return BeanUtil.beanToMap(bean, MapUtil.newHashMap(), false, true);
    }

    /**
     * 属性copy
     *
     * @param source 源
     * @param type   目标类型
     */
    public static <T> T copyProperties(Object source, Class<T> type,String... ignoreProperties) {
        final T target = ReflectUtil.newInstanceIfPossible(type);
        BeanUtil.copyProperties(source, target,ignoreProperties);
        return target;
    }

    /**
     * 属性copy
     *
     * @param source 源
     * @param type   目标类型
     */
    public static <T> T copyProperties(Object source, Class<T> type) {
        final T target = ReflectUtil.newInstanceIfPossible(type);
        BeanUtil.copyProperties(source, target, CopyOptions.create().ignoreNullValue());
        return target;
    }

    /**
     * list copy
     *
     * @param collection 源list
     * @param type       目标类型
     * @return copied list
     */
    public static <T> List<T> copyToList(Collection<?> collection, Class<T> type) {
        return BeanUtil.copyToList(collection, type, CopyOptions.create().ignoreNullValue());
    }
}
