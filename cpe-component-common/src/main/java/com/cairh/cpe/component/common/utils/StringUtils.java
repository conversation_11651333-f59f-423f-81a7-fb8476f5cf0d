package com.cairh.cpe.component.common.utils;

import org.slf4j.helpers.FormattingTuple;
import org.slf4j.helpers.MessageFormatter;

import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 字符串工具类
 *
 * <AUTHOR>
 * @since XPE-SP1-PACK2
 */
public class StringUtils extends org.apache.commons.lang3.StringUtils {

    /** 占位符前缀 */
    public static final String PLACEHOLDER_REFIX = "${";

    /** 占位符后缀 */
    public static final String PLACEHOLDER_SUFFIX = "}";

    /** 占位符正则表达式串 */
    public static final String PLACEHOLDER_REGEX = "\\$\\{(.*?)\\}";

    /**
     * 是否全部为空
     */
    public static boolean isAllBlank(String... strings) {
        boolean notBlank = false;
        for (String string : strings) {
            if (StringUtils.isNotBlank(string)) {
                notBlank = true;
                break;
            }
        }
        return !notBlank;
    }

    /**
     * log4j方式格式化字符串
     */
    public static String format(String messagePattern, Object... argArray) {
        FormattingTuple formattingTuple =
                MessageFormatter.arrayFormat(messagePattern, argArray);
        return formattingTuple.getMessage();
    }

    /**
     * 使用map格式化字符串中的占位符，使用默认的设置
     */
    public static String formatWithPlaceholderWithDefaultSettings(
            String origin, Map<String, String> map) {
        return formatWithPlaceholder(origin, map, PLACEHOLDER_REFIX,
                PLACEHOLDER_SUFFIX, PLACEHOLDER_REGEX);
    }

    /**
     * 使用map格式化字符串中的占位符
     */
    public static String formatWithPlaceholder(String origin,
                                               Map<String, String> map,
                                               String prefix,
                                               String suffix, String regex) {
        String string = origin;
        if (isAnyBlank(origin, prefix, suffix, regex)) {
            // 参数传入错误，直接返回原始串
            return string;
        }
        // 替换
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(string);
        while (matcher.find()) {
            String key = matcher.group(1);
            String value = map.get(key) == null ? "" : map.get(key);
            string = string.replace(prefix + key + suffix, value);
        }
        return string;
    }

    /**
     * 半角转全角
     */
    public static String encodeXSS(String s) {
        if (s == null || "".equals(s)) {
            return s;
        }
        StringBuilder builder = new StringBuilder();
        for (int i = 0; i < s.length(); i++) {
            char c = s.charAt(i);
            switch (c) {
                // handle the '<' and '>' which can be used for constructing <script> and </script>
                case '>':
                    builder.append('＞');
                    break;
                case '<':
                    builder.append('＜');
                    break;
                // since the html can support the characters using $#number format
                // so here also need to escape '#','&' and quote symbol
                case '\'':
                    builder.append('‘');
                    break;
                case '\"':
                    builder.append('“');
                    break;
                case '&':
                    builder.append('＆');
                    break;
                case '\\':
                    builder.append('＼');
                    break;
                case '#':
                    builder.append('＃');
                    break;
                case ':':
                    builder.append('：');
                    break;
                case '(':
                    builder.append('（');// 全角左括号
                    break;
                case ')':
                    builder.append('）');// 全角右括号
                    // if not the special characters ,then output it directly
                    break;
                default:
                    builder.append(c);
                    break;
            }
        }
        return builder.toString();
    }

    /**
     * 使用给定的{@link Pattern}进行字符串的分隔
     */
    public static String[] split(String text, Pattern pattern) {
        if (StringUtils.isBlank(text) || pattern == null) {
            return new String[0];
        }
        return pattern.split(text);
    }
}
