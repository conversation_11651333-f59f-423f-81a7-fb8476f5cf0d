package com.cairh.cpe.component.common.data.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.component.common.data.entity.VideoRecord;
import com.cairh.cpe.component.common.data.mapper.VideoRecordMapper;
import com.cairh.cpe.component.common.data.service.IVideoRecordService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 视频流水表 服务实现类
 *
 * <AUTHOR>
 */
@Service
public class VideoRecordServiceImpl extends ServiceImpl<VideoRecordMapper, VideoRecord> implements IVideoRecordService {

    @Override
    public List<String>  queryVideoRecordByBusinFlag() {
        return this.baseMapper.getVideoRecordByBusinFlag();
    }
}
