package com.cairh.cpe.component.common.data.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.component.common.constant.Constant;
import com.cairh.cpe.component.common.data.entity.FundProduct;
import com.cairh.cpe.component.common.data.entity.FundProductJour;
import com.cairh.cpe.component.common.data.mapper.FundProductMapper;
import com.cairh.cpe.component.common.data.service.IFundProductJourService;
import com.cairh.cpe.component.common.data.service.IFundProductService;
import com.cairh.cpe.component.common.utils.BaseBeanUtil;
import com.cairh.cpe.component.common.utils.JourUtil;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.context.BizException;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@AllArgsConstructor
public class FundProductServiceImpl extends ServiceImpl<FundProductMapper, FundProduct> implements IFundProductService {

    private final IFundProductJourService fundProductJourService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveFund(BaseUser baseUser, FundProduct fundProduct) {
        FundProduct product = getOne(new LambdaUpdateWrapper<FundProduct>().eq(FundProduct::getProd_code,fundProduct.getProd_code()));
        if(product != null){
            throw new BizException("该产品代码对应的产品已经存在！");
        }
        fundProduct.setCreate_by(baseUser.getStaff_no());
        fundProduct.setModify_by(baseUser.getStaff_no());
        super.save(fundProduct);
        JourUtil.writeJour(() -> saveJour(fundProduct,false, Constant.BUSINESS_FLAG_ADD));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateFund(BaseUser baseUser, FundProduct fundProduct) {
        fundProduct.setCreate_by(baseUser.getStaff_no());
        fundProduct.setModify_by(baseUser.getStaff_no());
        super.updateById(fundProduct);
        JourUtil.writeJour(() -> saveJour(fundProduct,true, Constant.BUSINESS_FLAG_MOD));

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteFund(BaseUser baseUser, FundProduct fundProduct) {
        fundProduct.setCreate_by(baseUser.getStaff_no());
        fundProduct.setModify_by(baseUser.getStaff_no());
        JourUtil.writeJour(() -> saveJour(fundProduct,true, Constant.BUSINESS_FLAG_DEL));
        super.removeById(fundProduct);
    }


    private boolean saveJour(FundProduct entity, boolean isUpdate, Integer business_flag) {
        if (isUpdate){
            entity=getById(entity.getSerial_id());
        }
        FundProductJour fundProductJour = BaseBeanUtil.copyProperties(entity, FundProductJour.class);
        fundProductJour.setSerial_id(null);
        fundProductJour.setBusiness_flag(business_flag);
        return fundProductJourService.save(fundProductJour);
    }
}
