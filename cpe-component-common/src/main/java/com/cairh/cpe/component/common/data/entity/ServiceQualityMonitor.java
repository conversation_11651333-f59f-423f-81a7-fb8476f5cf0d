package com.cairh.cpe.component.common.data.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.cairh.cpe.common.backend.annotation.Desensitize;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

import static com.cairh.cpe.common.backend.masking.mode.DataMaskingMode.ID_NO;
import static com.cairh.cpe.common.backend.masking.mode.DataMaskingMode.MOBILE_TEL;

/**
 * <p>
 * 服务质量监控
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("SERVICEQUALITYMONITOR")
public class ServiceQualityMonitor extends Model<ServiceQualityMonitor> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "serial_id",type = IdType.ASSIGN_UUID)
    private String serial_id;


    //客户营业部
    @TableField(value = "branch_no")
    private String branch_no;

    //客户姓名
    @TableField(value = "full_name")
    private String full_name;

    //客户证件类型
    @TableField(value = "id_kind")
    private String id_kind;

    //客户证件号码
    @TableField(value = "id_no")
    @Desensitize(mode = ID_NO)
    private String id_no;

    //客户号
    @TableField(value = "client_id")
    private String client_id;

    //资金账号
    @TableField(value = "fund_account")
    private String fund_account;

    //手机号
    @TableField(value = "mobile_tel")
    @Desensitize(mode = MOBILE_TEL)
    private String mobile_tel;

    //操作员编号
    @TableField(value = "operator_no")
    private String operator_no;

    //操作员姓名
    @TableField(value = "operator_name")
    private String operator_name;

    //业务类型
    @TableField(value = "busin_type")
    private String busin_type;

    //业务名称
    @TableField(value = "busin_name")
    private String busin_name;

    //子系统编号
    @TableField(value = "subsys_no")
    private Integer subsys_no;

    @TableField(exist = false)
    private String subsys_no_str;

    //业务流水唯一ID
    @TableField(value = "unique_id")
    private String unique_id;

    //机构标志
    @TableField(value = "organ_flag")
    private String organ_flag;

    //APPID
    @TableField(value = "app_id")
    private String app_id;

    //操作类型
    @TableField(value = "busin_flag")
    private String busin_flag;


    //文件ID
    @TableField(value = "filerecord_id")
    private String filerecord_id;

    //上传时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @TableField(value = "upload_datetime")
    private Date upload_datetime;

    //开始时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @TableField(value = "begin_datetime")
    private Date begin_datetime;

    //结束时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @TableField(value = "end_datetime")
    private Date end_datetime;

    /**
     * 创建日期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @TableField(value = "create_datetime")
    private Date create_datetime;


    /**
     * 归历史标识
     */
    @TableField("tohis_flag")
    private String tohis_flag;

    /**
     * 归历史时间
     */
    @TableField("tohis_datetime")
    private Date tohis_datetime;

    /**
     * 清算日期
     */
    @TableField("date_clear")
    private Integer date_clear;

}
