package com.cairh.cpe.component.common.data.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.component.common.utils.BaseBeanUtil;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.context.constant.ErrorCode;
import com.cairh.cpe.component.common.constant.Constant;
import com.cairh.cpe.component.common.data.entity.AuthorizeInfo;
import com.cairh.cpe.component.common.data.mapper.AuthorizeInfoMapper;
import com.cairh.cpe.component.common.data.service.IAuthorizeinfoService;
import com.cairh.cpe.component.common.form.AuthorizeinfoForm;
import com.cairh.cpe.component.common.form.request.CreateAuthorizeinfoRequest;
import com.cairh.cpe.component.common.form.request.DeleteAuthorizeinfoRequest;
import com.cairh.cpe.component.common.form.request.UpdateAuthorizeinfoRequest;
import com.cairh.cpe.util.crypt.SM4Util;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
public class AuthorizeinfoServiceImpl extends ServiceImpl<AuthorizeInfoMapper, AuthorizeInfo> implements IAuthorizeinfoService {

    @Override
    public Page<AuthorizeInfo> queryByPage(AuthorizeinfoForm request) {
        LambdaQueryWrapper<AuthorizeInfo> queryWrapper = new LambdaQueryWrapper<>(AuthorizeInfo.class);
        if (StringUtils.isNotBlank(request.getApp_id())) {
            queryWrapper.eq(AuthorizeInfo::getApp_id, request.getApp_id());
        }

//        if (StringUtils.isNotBlank(request.getSecret_key())) {
//            queryWrapper.eq(Authorizeinfo::getSecret_key, request.getSecret_key());
//        }

//        if (StringUtils.isNotBlank(request.getStatus())) {
//            queryWrapper.eq(Authorizeinfo::getStatus, request.getStatus());
//        }

        if (Objects.nonNull(request.getExpire_date_str())) {
            queryWrapper.eq(AuthorizeInfo::getExpire_date_str, request.getExpire_date_str());
        }

        queryWrapper.eq(AuthorizeInfo::getStatus, Constant.COMMON_VALID_STATUS);


        IPage<AuthorizeInfo> respPage = this.page(request, queryWrapper);
        Page<AuthorizeInfo> pageBranch = BaseBeanUtil.copyProperties(respPage, Page.class);

        // 解密
        respPage.getRecords().forEach(v -> v.setSecret_key(SM4Util.decode(v.getSecret_key())));

        return pageBranch;
    }

    @Override
    public void createAuthorizeInfo(BaseUser baseUser, CreateAuthorizeinfoRequest requset) {
        if (StringUtils.isBlank(requset.getApp_id())) {
            throw new BizException(ErrorCode.ERR_PARAM_IN_NOT_EMPTY, "[app_id]不能为空");
        }

        if (StringUtils.isBlank(requset.getSecret_key())) {
            throw new BizException(ErrorCode.ERR_PARAM_IN_NOT_EMPTY, "[secret_key]密钥不能为空");
        }

        if (Objects.isNull(requset.getExpire_date_str())) {
            throw new BizException(ErrorCode.ERR_PARAM_IN_NOT_EMPTY, "[expire_date_str]过期时间不能为空");
        }

        if (selectByAppid(requset.getApp_id()) != null) {
            throw new BizException(ErrorCode.ERR_PARAM_IN_ERROR, "[app_id]已经存在");
        }


        AuthorizeInfo authorizeinfo = new AuthorizeInfo();
        authorizeinfo.setApp_id(requset.getApp_id());
        authorizeinfo.setSecret_key(SM4Util.encode(requset.getSecret_key()));
        authorizeinfo.setExpire_date_str(requset.getExpire_date_str());
        if (StringUtils.isNotBlank(requset.getRemark())) {
            authorizeinfo.setRemark(requset.getRemark());
        } else {
            authorizeinfo.setRemark(" ");
        }
        authorizeinfo.setStatus(Constant.COMMON_VALID_STATUS);
        this.baseMapper.insert(authorizeinfo);
    }

    @Override
    public void modifyAuthorizeInfo(BaseUser baseUser, UpdateAuthorizeinfoRequest request) {
        if (StringUtils.isBlank(request.getSerial_id())) {
            throw new BizException(ErrorCode.ERR_PARAM_IN_NOT_EMPTY, "[serial_id]编号不能为空");
        }

        AuthorizeInfo authorizeinfo = this.baseMapper.selectById(request.getSerial_id());

        if (authorizeinfo == null) {
            throw new BizException(ErrorCode.ERR_PARAM_IN_NOT_EMPTY, "[serial_id]编号对应的数据不存在");
        }

        if (StringUtils.isNotBlank(request.getSecret_key())) {
            authorizeinfo.setSecret_key(SM4Util.encode(request.getSecret_key()));
        }

        if (StringUtils.isNotBlank(request.getRemark())) {
            authorizeinfo.setRemark(request.getRemark());
        }

        if (Objects.nonNull(request.getExpire_date_str())) {
            authorizeinfo.setExpire_date_str(request.getExpire_date_str());
        }

        this.baseMapper.updateById(authorizeinfo);
    }

    @Override
    public void deleteAuthorizeInfo(BaseUser baseUser, DeleteAuthorizeinfoRequest request) {
        if (StringUtils.isBlank(request.getSerial_id())) {
            throw new BizException(ErrorCode.ERR_PARAM_IN_NOT_EMPTY, "[serial_id]编号不能为空");
        }

        AuthorizeInfo authorizeinfo = this.baseMapper.selectById(request.getSerial_id());

        if (authorizeinfo == null) {
            throw new BizException(ErrorCode.ERR_PARAM_IN_NOT_EMPTY, "[serial_id]编号对应的数据不存在");
        }

        // 删除状态
        authorizeinfo.setStatus(Constant.COMMON_DELETE_STATUS);
        this.baseMapper.updateById(authorizeinfo);
    }


    public AuthorizeInfo selectByAppid(String appId) {
        LambdaQueryWrapper<AuthorizeInfo> queryWrapper = new LambdaQueryWrapper<AuthorizeInfo>();
        queryWrapper.eq(AuthorizeInfo::getApp_id, appId)
                .eq(AuthorizeInfo::getStatus, Constant.COMMON_VALID_STATUS);

        return this.baseMapper.selectOne(queryWrapper);
    }

    @Override
    public AuthorizeInfo getByAppIdAndSecretKey(String appId, String secretKey) {
        LambdaQueryWrapper<AuthorizeInfo> queryWrapper = new LambdaQueryWrapper<AuthorizeInfo>()
                .eq(AuthorizeInfo::getApp_id, appId)
                .eq(AuthorizeInfo::getSecret_key, secretKey);
        return baseMapper.selectOne(queryWrapper);
    }

}
