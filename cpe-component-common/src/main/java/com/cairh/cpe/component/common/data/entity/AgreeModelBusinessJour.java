package com.cairh.cpe.component.common.data.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 协议业务参数流水表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("agreemodelbusinessjour")
public class AgreeModelBusinessJour extends Model<AgreeModelBusinessJour> {

    private static final long serialVersionUID = 1L;

    /**
     * 协议业务参数id
     */
    @TableId("serial_id")
    private String serial_id;

    /**
     * 协议模板id
     */
    @TableField("elecagreemodel_id")
    private String elecagreemodel_id;

    /**
     * 子系统编号
     */
    @TableField("subsys_no")
    private Integer subsys_no;

    /**
     * 业务编号
     */
    @TableField("busin_type")
    private Integer busin_type;

    /**
     * 机构标识
     */
    @TableField("organ_flag")
    private String organ_flag;

    @TableField("agreement_type")
    private String agreement_type;

    /**
     * 强制阅读标识
     */
    @TableField("agreement_read_type")
    private String agreement_read_type;

    /**
     * 轮播模式
     */
    @TableField("auto_read_type")
    private String auto_read_type;

    /**
     * 强制阅读时间
     */
    @TableField("force_read_time")
    private String force_read_time;

    @TableField("create_by")
    private String create_by;

    /**
     * 创建日期时间
     */
    @TableField(value = "create_datetime")
    private Date create_datetime;

    @TableField("modify_by")
    private String modify_by;

    /**
     * 修改时间
     */
    @TableField(value = "modify_datetime")
    private Date modify_datetime;

    /**
     * 状态
     */
    @TableField(value = "status")
    private String status;

    /**
     * 规则表达式
     */
    @TableField("regular_expre")
    private String regular_expre;

    /**
     * 归历史标志
     */
    @TableField(value = "tohis_flag")
    private String tohis_flag;

    /**
     * 归历史时间
     */
    @TableField("tohis_datetime")
    private Date tohis_datetime;

    @TableField("agreemodelbusiness_id")
    private String agreemodelbusiness_id;

    @TableField(value = "business_flag")
    private Integer business_flag;

    /**
     * 排序
     */
    @TableField("order_no")
    private String order_no;

    /**
     * 清算日期
     */
    @TableField("date_clear")
    private Integer date_clear;

    @Override
    public Serializable pkVal() {
        return this.serial_id;
    }

}
