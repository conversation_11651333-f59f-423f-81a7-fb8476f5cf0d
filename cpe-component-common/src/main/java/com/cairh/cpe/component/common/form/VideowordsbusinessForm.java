package com.cairh.cpe.component.common.form;

import com.cairh.cpe.component.common.data.entity.VideoWordsBusiness;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 * @since 2022-07-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ToString(callSuper = true)
public class VideowordsbusinessForm extends BasePage<VideoWordsBusiness> {

    {
        super.setOrders(null);
    }

    /**
     * id
     */
    private String serial_id;

    /**
     * 视频话术模板id
     */
    private String videowordsmodel_id;

    /**
     * 视频方式
     */
    private String video_type;

    /**
     * 子系统编号
     */
    private Integer subsys_no;

    /**
     * 产品代码
     */
    private String prod_code;

    /**
     * 产品TA编号
     */
    private String prodta_no;

    /**
     * 业务编号
     */
    private Integer busin_type;

    /**
     * 机构标识
     */
    private String organ_flag;

    /**
     * 创建人
     */
    private String create_by;

    /**
     * 修改人
     */
    private String modify_by;

    /**
     * 状态
     */
    private String status;

    /**
     * 模板名称
     */
    private String model_name;

    /**
     * 模板类型
     */
    private String model_type;

    /**
     * 规则表达式
     */
    private String regular_expre;
}