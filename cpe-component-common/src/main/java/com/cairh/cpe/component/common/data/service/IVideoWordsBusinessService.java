package com.cairh.cpe.component.common.data.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.component.common.data.entity.VideoWordsBusiness;
import com.cairh.cpe.component.common.form.VideowordsbusinessDto;
import com.cairh.cpe.component.common.form.VideowordsbusinessForm;

import java.util.Map;

/**
 * <p>
 * 视频话术规则配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-11
 */
public interface IVideoWordsBusinessService extends IService<VideoWordsBusiness> {

    /**
     * 查询单条视频话术规则配置
     * @param serial_id
     * @return
     */
    VideowordsbusinessDto queryOne(String serial_id);

    /**
     * 自定义分页查询
     * @param param
     * @return
     */
    Page<VideowordsbusinessDto> queryByPage(VideowordsbusinessForm param);

    /**
     * 新增一条视频话术规则
     * @param baseUser
     * @param entity
     */
    void baseSave(BaseUser baseUser, VideoWordsBusiness entity);

    /**
     * 更新
     * @param baseUser
     * @param entity
     */
    void baseUpdate(BaseUser baseUser, VideoWordsBusiness entity);

    /**
     * 删除
     * @param baseUser
     * @param entity
     */
    void baseDelete(BaseUser baseUser, VideoWordsBusiness entity);

    /**
     * 根据模板id查询
     * @param videowordsmodel_id
     * @return
     */
    VideowordsbusinessDto queryByModelId(String videowordsmodel_id);

    /**
     * 进行渲染
     * @return
     */
    String renderData(String context, Map<String, String> render_data);

}

