package com.cairh.cpe.component.common.data.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.component.common.data.entity.VideoWordsModel;
import com.cairh.cpe.component.common.form.QueryAndReplaceForm;
import com.cairh.cpe.component.common.form.VideowordsmodelDto;

import java.util.List;

/**
 * <p>
 * 视频话术模板表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-11
 */
public interface IVideoWordsModelService extends IService<VideoWordsModel> {

    /**
     * 新增话术模板
     *
     * @param baseUser 用户信息
     * @param entity   新增话术模板
     */
    void baseSave(BaseUser baseUser, VideowordsmodelDto entity);

    /**
     * 编辑话术模板
     *
     * @param baseUser
     * @param entity
     */
    void baseUpdate(BaseUser baseUser, VideowordsmodelDto entity);

    /**
     * 删除话术模板
     */
    void baseDelete(String serial_id);

    /**
     * 查询话术模板详情并替换占位符
     *
     * @return
     */
    List<VideowordsmodelDto> queryAndReplace(BaseUser baseUser, QueryAndReplaceForm entity);

    /**
     * 查询话术模板详情
     * @param baseUser
     * @return
     */
    VideowordsmodelDto queryOne(BaseUser baseUser, String serial_id);
}
