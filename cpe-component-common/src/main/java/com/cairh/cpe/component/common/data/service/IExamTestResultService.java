package com.cairh.cpe.component.common.data.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cairh.cpe.component.common.data.entity.ExamTestResult;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cairh.cpe.component.common.form.ExamTestResultDto;
import com.cairh.cpe.component.common.form.ExamTestResultForm;

/**
 * <p>
 * 问卷结果 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-06
 */
public interface IExamTestResultService extends IService<ExamTestResult> {
        Page<ExamTestResultDto> queryPageExamTestResultInfo(ExamTestResultForm param);
}
