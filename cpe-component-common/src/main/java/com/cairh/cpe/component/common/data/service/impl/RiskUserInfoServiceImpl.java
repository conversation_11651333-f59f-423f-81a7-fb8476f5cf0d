package com.cairh.cpe.component.common.data.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.component.common.data.entity.RiskInfo;
import com.cairh.cpe.component.common.data.entity.RiskUserInfo;
import com.cairh.cpe.component.common.data.mapper.RiskInfoMapper;
import com.cairh.cpe.component.common.data.mapper.RiskUserInfoMapper;
import com.cairh.cpe.component.common.data.service.IRiskUserInfoService;
import com.cairh.cpe.component.common.form.RiskInfoDetailDto;
import com.cairh.cpe.component.common.form.RiskInfoDetailSub;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;


@Slf4j
@Service
public class RiskUserInfoServiceImpl extends ServiceImpl<RiskUserInfoMapper, RiskUserInfo> implements IRiskUserInfoService {


    @Autowired
    private RiskInfoMapper riskInfoMapper;

    @Override
    public RiskInfoDetailDto queryDetail(RiskUserInfo entity) {
        /**
         * 1.查到当前风测用户信息
         * 2.排倒序找到最新和次新两条数据，获取examtestresult_id
         * 3.通过examtestresult_id查询riskinfo表，获取两次的风测结果
         * 3.风测结果进行比对，将不同项的返回给页面
         */
        RiskUserInfo riskUserInfo = this.getById(entity.getSerial_id());
        List<RiskUserInfo> riskUserInfoList = new ArrayList<>();
        if (StringUtils.isNotBlank(riskUserInfo.getClient_id())) {
            riskUserInfoList = this.lambdaQuery().eq(RiskUserInfo::getClient_id, riskUserInfo.getClient_id()).orderByDesc(RiskUserInfo::getModify_datetime).list();
        }
        //如果只有1条则返回空，无变更，如果有多条取最新和次新进行比较
        RiskInfoDetailDto riskInfoDetailDto = new RiskInfoDetailDto();

        if (CollectionUtils.isEmpty(riskUserInfoList) || riskUserInfoList.size() == 1) {
            List<RiskInfoDetailSub> riskInfoDetailSubList = new ArrayList<>();
            riskInfoDetailDto.setResultList(riskInfoDetailSubList);
        } else {

            //如果是最后一条(排的倒序)，也就是初始插入的数据，为无变更
            RiskUserInfo riskUserInfoFrist = riskUserInfoList.get(riskUserInfoList.size() - 1);
            if (StringUtils.equals(riskUserInfoFrist.getSerial_id(), entity.getSerial_id())) {
                List<RiskInfoDetailSub> riskInfoDetailSubList = new ArrayList<>();
                riskInfoDetailDto.setResultList(riskInfoDetailSubList);
                return riskInfoDetailDto;
            }
            List<RiskInfoDetailSub> resultList = new ArrayList<>();

            int j = -1;
            for (int i = 0; i < riskUserInfoList.size(); i++) {
                if (StringUtils.equals(riskUserInfoList.get(i).getSerial_id(), entity.getSerial_id())) {
                    j = i;
                    break;
                }
            }

            RiskUserInfo newRiskUserInfo = riskUserInfoList.get(j);
            RiskUserInfo oldRiskUserInfo = riskUserInfoList.get(j + 1);

            String newExamtestresultId = newRiskUserInfo.getExamtestresult_id();
            String oldExamtestresultId = oldRiskUserInfo.getExamtestresult_id();


            LambdaQueryWrapper<RiskInfo> newQueryWrapper = new LambdaQueryWrapper<RiskInfo>();
            newQueryWrapper.eq(RiskInfo::getExamtestresult_id, newExamtestresultId);
            RiskInfo newRiskInfo = riskInfoMapper.selectOne(newQueryWrapper);

            LambdaQueryWrapper<RiskInfo> oldQueryWrapper = new LambdaQueryWrapper<RiskInfo>();
            oldQueryWrapper.eq(RiskInfo::getExamtestresult_id, oldExamtestresultId);
            RiskInfo oldRiskInfo = riskInfoMapper.selectOne(oldQueryWrapper);

            //客户风险等级 原字典13003
            String newCorpRiskLevel = newRiskInfo.getCorp_risk_level();
            String oldCorpRiskLevel = oldRiskInfo.getCorp_risk_level();
            log.info("进行比较：客户:{}，客户风险等级, {}和{}比较，结果:{}", newRiskUserInfo.getClient_id(), newCorpRiskLevel, oldCorpRiskLevel, StringUtils.equals(newCorpRiskLevel, oldCorpRiskLevel));
            if (!StringUtils.equals(newCorpRiskLevel, oldCorpRiskLevel)) {
                RiskInfoDetailSub riskInfoDetailSub = new RiskInfoDetailSub();
                riskInfoDetailSub.setNewvalue(newCorpRiskLevel);
                riskInfoDetailSub.setOldvalue(oldCorpRiskLevel);
                riskInfoDetailSub.setColumn("客户风险等级");
                riskInfoDetailSub.setType("user_risk_level");
                resultList.add(riskInfoDetailSub);
            }
            //投资期限 原字典13008
            String newEnInvestTerm = newRiskInfo.getEn_invest_term();
            newEnInvestTerm = deleteSymblo(newEnInvestTerm);
            String oldEnInvestTerm = oldRiskInfo.getEn_invest_term();
            oldEnInvestTerm = deleteSymblo(oldEnInvestTerm);
            boolean enInvestTermFlag = compareStr(newEnInvestTerm, oldEnInvestTerm, "客户:" + newRiskUserInfo.getClient_id() + "，投资期限");
            if (!enInvestTermFlag) {
                RiskInfoDetailSub riskInfoDetailSub = new RiskInfoDetailSub();
                riskInfoDetailSub.setNewvalue(newEnInvestTerm);
                riskInfoDetailSub.setOldvalue(oldEnInvestTerm);
                riskInfoDetailSub.setColumn("投资期限");
                riskInfoDetailSub.setType("invest_term");
                resultList.add(riskInfoDetailSub);
            }
            //投资品种
            String newEnInvestKind = newRiskInfo.getEn_invest_kind();
            newEnInvestKind = deleteSymblo(newEnInvestKind);
            String oldEnInvestKind = oldRiskInfo.getEn_invest_kind();
            oldEnInvestKind = deleteSymblo(oldEnInvestKind);
            boolean enEnInvestKindFlag = compareStr(newEnInvestKind, oldEnInvestKind, "客户:" + newRiskUserInfo.getClient_id() + "，投资品种");
            if (!enEnInvestKindFlag) {
                RiskInfoDetailSub riskInfoDetailSub = new RiskInfoDetailSub();
                riskInfoDetailSub.setNewvalue(newEnInvestKind);
                riskInfoDetailSub.setOldvalue(oldEnInvestKind);
                riskInfoDetailSub.setColumn("投资品种");
                riskInfoDetailSub.setType("invest_kind");
                resultList.add(riskInfoDetailSub);
            }
            //冷静期
            Integer newCoolingPeriod = newRiskInfo.getCooling_period();
            Integer oldCoolingPeriod = oldRiskInfo.getCooling_period();
            log.info("进行比较：客户:{}，冷静期, {}和{}比较，结果:{}", newRiskUserInfo.getClient_id(), newCoolingPeriod, oldCoolingPeriod, newCoolingPeriod == oldCoolingPeriod);
            if (newCoolingPeriod != oldCoolingPeriod) {
                RiskInfoDetailSub riskInfoDetailSub = new RiskInfoDetailSub();
                riskInfoDetailSub.setNewvalue(newCoolingPeriod.toString());
                riskInfoDetailSub.setOldvalue(oldCoolingPeriod.toString());
                riskInfoDetailSub.setColumn("冷静期");
                resultList.add(riskInfoDetailSub);
            }
            riskInfoDetailDto.setResultList(resultList);
        }
        return riskInfoDetailDto;
    }


    /**
     * 比较字符串
     *
     * @param s1
     * @param s2
     * @param print
     * @return
     */
    private boolean compareStr(String s1, String s2, String print) {
        String[] arr1 = s1.split(",");
        String[] arr2 = s2.split(",");
        Set<String> newEnInvestTermset = new HashSet<>(Arrays.asList(arr1));
        Set<String> oldEnInvestTermset = new HashSet<>(Arrays.asList(arr2));
        boolean flag = newEnInvestTermset.equals(oldEnInvestTermset);
        log.info("进行比较：{}, {}和{}比较，结果:{}", print, s1, s2, flag);
        return flag;
    }

    /**
     * 去掉开头和结尾的逗号
     *
     * @param en_str
     * @return
     */
    private String deleteSymblo(String en_str) {
        if (StringUtils.isNotBlank(en_str) && en_str.startsWith(",")) {
            en_str = en_str.replaceFirst(",", "");
        }
        if (StringUtils.isNotBlank(en_str) && en_str.endsWith(",")) {
            en_str = en_str.substring(0, en_str.length() - 1);
        }
        return en_str;
    }
}