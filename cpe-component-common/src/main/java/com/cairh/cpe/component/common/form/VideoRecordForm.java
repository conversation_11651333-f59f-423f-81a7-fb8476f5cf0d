package com.cairh.cpe.component.common.form;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cairh.cpe.component.common.data.entity.VideoRecord;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 视频记录
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class VideoRecordForm extends Page<VideoRecord> {

    //业务类型
    private String busin_type;

    //子系统编号
    private Integer subsys_no;

    //操作员编号
    private String operator_no;

    private String operator_name;

    //操作员营业部
    private String op_branch_no;

    //客户营业部
    private String branch_no;

    //客户姓名
    private String full_name;

    //客户证件类型
    private String id_kind;

    //客户证件号码
    private String id_no;

    //客户号
    private String client_id;

    //资金账号
    private String fund_account;

    //手机号
    private String mobile_tel;

    //操作类型
    private String busin_flag;
    //操作标识
    private String operation_flag;
    //接入方式
    private String app_id;

    //视频结果 0失败 1成功
    private String video_result;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date[] curr_date_range;
}
