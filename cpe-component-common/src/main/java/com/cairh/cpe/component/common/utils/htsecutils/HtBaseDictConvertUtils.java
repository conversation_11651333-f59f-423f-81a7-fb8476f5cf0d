package com.cairh.cpe.component.common.utils.htsecutils;

import com.alibaba.excel.util.StringUtils;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.esb.base.rpc.IVBaseDictConvertDubboService;
import com.cairh.cpe.esb.base.rpc.dto.req.VBaseDictToOutRequest;
import com.cairh.cpe.esb.base.rpc.dto.resp.VBaseDictToOutResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * 海通字典转换工具类
 */
@Slf4j
@Component
public class HtBaseDictConvertUtils {
    public static final String DICT_CODE_ID_KIND = "id_kind";
    private static final String HT_DICT_TRANS_KIND = "10";


    @DubboReference(check = false)
    private IVBaseDictConvertDubboService baseDictConvertDubboService;

    /**
     * 本地证件类型转换为海通证件类型
     *
     * @param localIdKind 本地证件类型
     * @return 海通证件类型
     */
    public String getHtIdKind(String localIdKind) {
        if (StringUtils.isBlank(localIdKind)) {
            log.warn("海通证件类型转换失败：本地证件类型为空，将返回空");
            return localIdKind;
        }
        VBaseDictToOutRequest req = new VBaseDictToOutRequest();
        req.setDict_trans_kind(HT_DICT_TRANS_KIND);
        req.setDict_code(DICT_CODE_ID_KIND);
        req.setSub_code(localIdKind);
        VBaseDictToOutResponse resp = baseDictConvertDubboService.baseDataLocalDictToOut(req);
        if (resp == null) {
            log.warn("海通证件类型转换失败：未查询到对应的转换关系将返回本地证件类型，转换参数：{}", req);
            return localIdKind;
        }
        log.info("海通证件类型转换成功，本地证件类型：{} -> 海通证件类型：{}", localIdKind, resp.getOuter_sub_code());
        return resp.getOuter_sub_code();

    }


    /**
     * 抛异常打印日志
     *
     * @param message 异常信息
     */
    private void throwExPrintLog(String message) {
        log.error(message);
        throw new BizException(message);
    }
}
