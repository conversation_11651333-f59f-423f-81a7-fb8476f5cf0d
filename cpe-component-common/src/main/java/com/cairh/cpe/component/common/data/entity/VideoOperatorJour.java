package com.cairh.cpe.component.common.data.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;

/**
 * 坐席登录流水
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("VIDEOOPERATORJOUR")
public class VideoOperatorJour implements Serializable {
    //序列ID
    @TableId("serial_id")
    private String serial_id;
    //操作员编号
    private String staff_no;
    //操作标志
    private String  business_flag;
    //菜单id
    private String menu_id;
    //时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date curr_datetime;

    // 上线下线的时间差
    private Integer interval_sec;

    //客户号
    private String remark;
}
