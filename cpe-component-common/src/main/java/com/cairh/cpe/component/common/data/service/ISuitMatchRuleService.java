package com.cairh.cpe.component.common.data.service;

import com.cairh.cpe.component.common.data.entity.SuitMatchRule;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 适当性匹配规则 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-23
 */
public interface ISuitMatchRuleService extends IService<SuitMatchRule> {

    /**
     * 批量更新、新增、删除
     * @param entityList
     * @return
     */
    List<String> saveOrUpdateOrDelBatch(List<SuitMatchRule> entityList);

    /**
     * 批量新增
     * @param entityList
     */
    void baseSaveBatch(List<SuitMatchRule> entityList);

    /**
     * 批量写流水
     * @param entityList
     * @param isUpdate
     * @param business_flag
     * @return
     */
    boolean saveJour(List<SuitMatchRule> entityList, boolean isUpdate, Integer business_flag);
}
