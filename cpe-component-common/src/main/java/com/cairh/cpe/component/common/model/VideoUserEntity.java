package com.cairh.cpe.component.common.model;

import com.cairh.cpe.common.backend.annotation.Desensitize;
import com.cairh.cpe.component.common.cache.DataConvert;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

import static com.cairh.cpe.common.backend.masking.mode.DataMaskingMode.ID_NO;
import static com.cairh.cpe.common.backend.masking.mode.DataMaskingMode.MOBILE_TEL;

/**
 * 视频用户实体
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class VideoUserEntity implements Serializable {

    private static final long serialVersionUID = -310267775442007692L;

    //ID
    private String unique_id;

    //名称
    private String user_name;

    //证件类型
    private String id_kind;
    @DataConvert(code_type = DataConvert.DICT, code_dict = "id_kind")
    private String id_kind_tranfer;

    public String getId_kind_tranfer() {
        if(id_kind_tranfer == null){
            return id_kind;
        }
        return id_kind_tranfer;
    }

    //证件号码
    @Desensitize(mode = ID_NO)
    private String id_no;

    //手机号
    @Desensitize(mode = MOBILE_TEL)
    private String mobile_tel;

    //开户营业部
    private String branch_no;
    @DataConvert(code_type = DataConvert.BRANCH)
    private String branch_no_tranfer;

    public String getBranch_no_tranfer() {
        if(branch_no_tranfer == null){
            return branch_no;
        }
        return branch_no_tranfer;
    }

    //经纪人编号
    private String staff_no;

    //跳转页面
    private String page_addr;

    //接入来源
    private String service_vender;

    //入队时间
    private long enqueue_time;

    //业务类型
    private String busin_type;
    @DataConvert(code_type = DataConvert.DICT, code_dict = "busin_type")
    private String busin_type_tranfer;

    public String getBusin_type_tranfer() {
        if(busin_type_tranfer == null){
            return busin_type;
        }
        return busin_type_tranfer;
    }

    //客户见证视频提交时间
    private String last_status_update_time;

    //活跃时间
    private String active_time;

    //子系统
    private String subsys_no;
    @DataConvert(code_type = DataConvert.DICT, code_dict = "subsys_no")
    private String subsys_no_tranfer;

    public String getSubsys_no_tranfer() {
        if(subsys_no_tranfer == null){
            return subsys_no;
        }
        return subsys_no_tranfer;
    }

    //渠道代码
    private String channel;

    //客户端类型
    private String app_id;
    @DataConvert(code_type = DataConvert.DICT, code_dict = "app_id")
    private String app_id_tranfer;

    public String getApp_id_tranfer() {
        if(app_id_tranfer == null){
            return app_id;
        }
        return app_id_tranfer;
    }

    private String request_id;

    /**
     * 智能派单的任务id
     */
    private String task_id;
    /**
     * 是否设置了最高优先级
     */
    private String priority_flag;

    /**
     * 补录
     */
    private String additional;
    /**
     * 经纪人编号
     */
    private String broker_code;

    /**
     * 渠道号
     */
    private String channel_code;
    /**
     * 渠道名称
     */
    private String channel_name;

}
