package com.cairh.cpe.component.common.form;

import com.baomidou.mybatisplus.annotation.TableField;
import com.cairh.cpe.component.common.cache.DataConvert;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2022-06-07
 */
@Data
public class AgreemodelbusinessVo {
    /**
     * 协议业务参数id
     */
    private String serial_id;

    /**
     * 协议模板id
     */
    private String elecagreemodel_id;

    /**
     * 子系统编号
     */
    private Integer subsys_no;
    @DataConvert(code_type = DataConvert.DICT, code_dict = "subsys_no")
    private String subsys_no_tranfer;

    public String getSubsys_no_tranfer() {
        if(subsys_no_tranfer == null){
            return String.valueOf(subsys_no);
        }
        return subsys_no_tranfer;
    }

    /**
     * 业务编号
     */
    private Integer busin_type;
    @DataConvert(code_type = DataConvert.DICT, code_dict = "busin_type")
    private String busin_type_tranfer;

    public String getBusin_type_tranfer() {
        if(busin_type_tranfer == null){
            return String.valueOf(busin_type);
        }
        return busin_type_tranfer;
    }

    /**
     * 机构标识
     */
    private String organ_flag;

    @DataConvert(code_type = DataConvert.DICT, code_dict = "organ_flag")
    private String organ_flag_tranfer;

    public String getOrgan_flag_tranfer() {
        if(organ_flag_tranfer == null){
            return organ_flag;
        }
        return organ_flag_tranfer;
    }

    private String agreement_type;
    @DataConvert(code_type = DataConvert.DICT, code_dict = "agreement_type")
    private String agreement_type_tranfer;

    public String getAgreement_type_tranfer() {
        if(agreement_type_tranfer == null){
            return agreement_type;
        }
        return agreement_type_tranfer;
    }

    /**
     * 强制阅读标识
     */
    private String agreement_read_type;

    /**
     * 强制阅读时间
     */
    private String force_read_time;

    private String create_by;

    @TableField(exist = false)
    @DataConvert(code_type = DataConvert.OPERATORINFO)
    private String create_by_tranfer;

    public String getCreate_by_tranfer() {
        if(create_by_tranfer == null){
            return create_by;
        }
        return create_by_tranfer;
    }

    /**
     * 创建日期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date create_datetime;

    private String modify_by;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date modify_datetime;

    /**
     * 状态
     */
    private String status;
    @DataConvert(code_type = DataConvert.DICT, code_dict = "enable_status")
    private String status_tranfer;

    public String getStatus_tranfer() {
        if(status_tranfer == null){
            return status;
        }
        return status_tranfer;
    }

    /**
     * 规则表达式
     */
    private String regular_expre;

    /**
     * 归历史标志
     */
    private String tohis_flag;

    /**
     * 归历史时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date tohis_datetime;

    /**
     * 协议名称
     */
    private String agreement_name;

    /**
     * 拓展名称
     */
    private String ex_name;

    /**
     * 协议版本
     */
    private String agreement_version;

    /**
     * 协议编号
     */
    private String agreement_no;

    /**
     * 自动阅读模式
     */
    private String auto_read_type;


}

