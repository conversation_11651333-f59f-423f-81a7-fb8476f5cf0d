package com.cairh.cpe.component.common.form;

/**
 * word文本替换键
 * 功能说明: <br>
 * 系统版本: v1.0<br>
 * 开发人员: <AUTHOR>
 * 开发时间: 2016-3-16<br>
 */
public class FileTypeConvert {
	
	private String busin_key ;
	//客户姓名
	private String client_name ;
	
	//客户编号
	private String client_id ;
	
	//资产账户
	private String fund_account ;
	
	//证件类别
	private String id_kind ;
	
	//证件号码
	private String id_no ;
	
	//时间
	private String sysdate ;
	
	//手机号码
	private String mobile_tel ;
	
	//电子信箱
	private String e_mail ;
	
	private String signImg ;

	//签署地点
	private String address ;
	
	private String flag ;
	
	private String client_gender;
	
	private String nationality;
	
	private String birthday;
	
	private String id_adress;
	
	private String replace_str;
	
	
	/**
	 * @return the id_adress
	 */
	public String getId_adress() {
		return id_adress;
	}

	/**
	 * @param id_adress the id_adress to set
	 */
	public void setId_adress(String id_adress) {
		this.id_adress = id_adress;
	}

	//套打模式，是否是替换文本框，1是替换文字，其他是替换文本框
	private String chmode;
	
	/**
	 * @return the chmode
	 */
	public String getChmode() {
		return chmode;
	}

	/**
	 * @param chmode the chmode to set
	 */
	public void setChmode(String chmode) {
		this.chmode = chmode;
	}

	/**
	 * @return the client_gender
	 */
	public String getClient_gender() {
		return client_gender;
	}

	/**
	 * @param client_gender the client_gender to set
	 */
	public void setClient_gender(String client_gender) {
		this.client_gender = client_gender;
	}

	/**
	 * @return the nationality
	 */
	public String getNationality() {
		return nationality;
	}

	/**
	 * @param nationality the nationality to set
	 */
	public void setNationality(String nationality) {
		this.nationality = nationality;
	}

	/**
	 * @return the birthday
	 */
	public String getBirthday() {
		return birthday;
	}

	/**
	 * @param birthday the birthday to set
	 */
	public void setBirthday(String birthday) {
		this.birthday = birthday;
	}

	/**
	 * @return the profession_code
	 */
	public String getProfession_code() {
		return profession_code;
	}

	/**
	 * @param profession_code the profession_code to set
	 */
	public void setProfession_code(String profession_code) {
		this.profession_code = profession_code;
	}

	/**
	 * @return the degree_code
	 */
	public String getDegree_code() {
		return degree_code;
	}

	/**
	 * @param degree_code the degree_code to set
	 */
	public void setDegree_code(String degree_code) {
		this.degree_code = degree_code;
	}

	/**
	 * @return the id_begindate
	 */
	public String getId_begindate() {
		return id_begindate;
	}

	/**
	 * @param id_begindate the id_begindate to set
	 */
	public void setId_begindate(String id_begindate) {
		this.id_begindate = id_begindate;
	}

	/**
	 * @return the id_enddate
	 */
	public String getId_enddate() {
		return id_enddate;
	}

	/**
	 * @param id_enddate the id_enddate to set
	 */
	public void setId_enddate(String id_enddate) {
		this.id_enddate = id_enddate;
	}

	/**
	 * @return the office_tel
	 */
	public String getOffice_tel() {
		return office_tel;
	}

	/**
	 * @param office_tel the office_tel to set
	 */
	public void setOffice_tel(String office_tel) {
		this.office_tel = office_tel;
	}

	/**
	 * @return the zipcode
	 */
	public String getZipcode() {
		return zipcode;
	}

	/**
	 * @param zipcode the zipcode to set
	 */
	public void setZipcode(String zipcode) {
		this.zipcode = zipcode;
	}

	private String profession_code;
	
	private String degree_code;
	
	private String id_begindate;
	
	private String id_enddate;
	
	private String office_tel;
	
	private String zipcode;
	
	private String busin_type;
	
	
	/**
	 * @return the busin_type
	 */
	public String getBusin_type() {
		return busin_type;
	}

	/**
	 * @param busin_type the busin_type to set
	 */
	public void setBusin_type(String busin_type) {
		this.busin_type = busin_type;
	}

	public String getClient_name() {
		return client_name;
	}

	public void setClient_name(String client_name) {
		this.client_name = client_name;
	}

	public String getClient_id() {
		return client_id;
	}

	public void setClient_id(String client_id) {
		this.client_id = client_id;
	}

	public String getFund_account() {
		return fund_account;
	}

	public void setFund_account(String fund_account) {
		this.fund_account = fund_account;
	}

	public String getId_kind() {
		return id_kind;
	}

	public void setId_kind(String id_kind) {
		this.id_kind = id_kind;
	}

	public String getId_no() {
		return id_no;
	}

	public void setId_no(String id_no) {
		this.id_no = id_no;
	}

	public String getSysdate() {
		return sysdate;
	}

	public void setSysdate(String sysdate) {
		this.sysdate = sysdate;
	}

	public String getMobile_tel() {
		return mobile_tel;
	}

	public void setMobile_tel(String mobile_tel) {
		this.mobile_tel = mobile_tel;
	}

	public String getE_mail() {
		return e_mail;
	}

	public void setE_mail(String e_mail) {
		this.e_mail = e_mail;
	}

	public String getSignImg() {
		return signImg;
	}

	public void setSignImg(String signImg) {
		this.signImg = signImg;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getFlag() {
		return flag;
	}

	public void setFlag(String flag) {
		this.flag = flag;
	}

	public String getBusin_key() {
		return busin_key;
	}

	public void setBusin_key(String busin_key) {
		this.busin_key = busin_key;
	}

	public String getReplace_str() {
		return replace_str;
	}

	public void setReplace_str(String replace_str) {
		this.replace_str = replace_str;
	}
}
