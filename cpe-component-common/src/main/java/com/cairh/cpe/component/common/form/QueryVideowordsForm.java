package com.cairh.cpe.component.common.form;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class QueryVideowordsForm  {

	//视频方式 Y
	private String video_type;

	//规则表达式 N
	private String regular_data;

	//业务类型 Y
	private Integer busin_type;

	//业务系统 Y
	private Integer subsys_no;

	//模板渲染数据 以JSON形式传递 N
	private Map<String, String> render_data;

	//用户unique_id Y
	private String unique_id;
}