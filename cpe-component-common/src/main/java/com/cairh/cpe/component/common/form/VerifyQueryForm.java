package com.cairh.cpe.component.common.form;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.ArrayList;
import java.util.Date;

/**
 * 公安认证查询
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ToString(callSuper = true)
public class VerifyQueryForm extends BasePage<VerifyQueryForm> {

    {
        OrderItem orderItem = new OrderItem();
        orderItem.setAsc(false);
        orderItem.setColumn("a.auth_datetime");

        OrderItem orderItem2 = new OrderItem();
        orderItem2.setAsc(true);
        orderItem2.setColumn("a.branch_no");

        ArrayList<OrderItem> orderItems = new ArrayList<>();
        orderItems.add(orderItem);
        orderItems.add(orderItem2);
        super.setOrders(orderItems);
    }

//    private List<String> branch_no_list;

    private String branch_no;
    /**
     * 认证厂商
     */
    private String factory_name;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date begin_time;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date end_time;

}