package com.cairh.cpe.component.common.data.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 视频话术规则配置流水
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("Videowordsbusinessjour")
public class VideoWordsBusinessJour extends Model<VideoWordsBusinessJour> {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "serial_id", type = IdType.ASSIGN_UUID)
    private String serial_id;

    /**
     * 视频话术规则配置id
     */
    @TableField("videowordsbusiness_id")
    private String videowordsbusiness_id;

    /**
     * 视频话术模板id
     */
    @TableField("videowordsmodel_id")
    private String videowordsmodel_id;

    /**
     * 视频方式
     */
    @TableField("video_type")
    private String video_type;

    /**
     * 子系统编号
     */
    @TableField("subsys_no")
    private Integer subsys_no;

    /**
     * 产品代码
     */
    @TableField("prod_code")
    private String prod_code;

    /**
     * 产品TA编号
     */
    @TableField("prodta_no")
    private String prodta_no;

    /**
     * 业务编号
     */
    @TableField("busin_type")
    private Integer busin_type;

    /**
     * 机构标识
     */
    @TableField("organ_flag")
    private String organ_flag;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String create_by;

    /**
     * 创建日期时间
     */
    @TableField(value = "create_datetime")
    private Date create_datetime;

    /**
     * 修改人
     */
    @TableField("modify_by")
    private String modify_by;

    /**
     * 修改时间
     */
    @TableField(value = "modify_datetime")
    private Date modify_datetime;

    /**
     * 状态
     */
    @TableField(value = "status")
    private String status;

    /**
     * 规则表达式
     */
    @TableField("regular_expre")
    private String regular_expre;

    @TableField(value = "business_flag")
    private Integer business_flag;


    @Override
    public Serializable pkVal() {
        return this.serial_id;
    }

}
