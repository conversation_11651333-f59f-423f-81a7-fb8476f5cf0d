package com.cairh.cpe.component.common.form;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cairh.cpe.component.common.data.entity.VideoRecord;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.Date;

/**
 * 视频记录
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class VideoRecordQuery extends Page<VideoRecord> {

    {
        OrderItem orderItem1 = new OrderItem();
        orderItem1.setAsc(false);
        orderItem1.setColumn("create_datetime");
        ArrayList<OrderItem> orderItems = new ArrayList<>();
        orderItems.add(orderItem1);
        super.setOrders(orderItems);
    }

    //序列ID
    private String serial_id;

    //客户营业部
    private String branch_no;

    //客户姓名
    private String full_name;

    //客户证件类型
    private String id_kind;

    //客户证件号码
    private String id_no;

    //客户号
    private String client_id;

    //资金账号
    private String fund_account;

    //手机号
    private String mobile_tel;

    //操作员编号
    private String operator_no;

    //操作员营业部
    private String op_branch_no;

    //操作员姓名
    private String operator_name;

    //业务类型
    private String busin_type;

    //业务名称
    private String busin_name;

    //子系统编号
    private Integer subsys_no;

    //业务流水唯一ID
    private String unique_id;

    //机构标志
    private String organ_flag;

    //APPID
    private String app_id;

    //总排队人数
    private Integer queue_length;

    //操作类型
    private String busin_flag;

    //房间号
    private String room_id;

    //实现厂商
    private String factory_name;

    //文件ID
    private String filerecord_id;

    //驳回原因编号
    private String reject_code;

    //驳回原因
    private String reject_info;

    //备注
    private String remark;

    //创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date create_datetime;

    //归历史标志
    private String tohis_flag;

    //归历史时间
    private Date tohis_datetime;

    //清算日期
    private Integer date_clear;
}
