package com.cairh.cpe.component.common.form;

import com.cairh.cpe.component.common.data.entity.WitnessRejectionInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 见证拒绝表单
 *
 * <AUTHOR>
 * @since 2023/6/7 14:29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ToString(callSuper = true)
public class WitnessRejectionInfoForm extends BasePage<WitnessRejectionInfo> {

    private static final long serialVersionUID = -77220803649536296L;

    /**
     * ID
     */
    private String serial_id;

    private Integer subsys_no;

    private Integer busin_type;

    /**
     * 排序字段
     */
    private String order_no;

    /**
     * 状态
     */
    private String status;

    /**
     * 创建人
     */
    private String create_by;

    /**
     * 修改人
     */
    private String modify_by;

    /**
     * 拒绝名称
     */
    private String cause_name;

}