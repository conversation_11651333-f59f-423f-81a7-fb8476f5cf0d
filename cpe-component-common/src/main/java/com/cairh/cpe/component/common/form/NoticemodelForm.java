package com.cairh.cpe.component.common.form;


import com.cairh.cpe.component.common.data.entity.NoticeModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 * @desc 消息模板分页查询入参
 * @date 2022-05-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ToString(callSuper = true)
public class NoticemodelForm extends BasePage<NoticeModel> {

    private static final long serialVersionUID = -4793203822093570768L;

    /**
     * ID
     */
    private String serial_id;

    /**
     * 模板编号
     */
    private String model_no;

    /**
     * 模板名称
     */
    private String model_name;

    /**
     * 模板内容标题
     */
    private String model_title;

    /**
     * 模板内容
     */
    private String model_content;

    /**
     * 状态
     */
    private String status;

    /**
     * 创建人
     */
    private String create_by;

    /**
     * 创建日期时间
     */
    private Date create_datetime;

    /**
     * 修改人
     */
    private String modify_by;

    /**
     * 修改时间
     */
    private Date modify_datetime;
}