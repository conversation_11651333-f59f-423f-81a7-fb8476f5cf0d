package com.cairh.cpe.component.common.constant;

/**
 * @description：海通类型 常量
 * @create： 2023/8/23 14:49
 */
public class HtSecConstant extends Constant {

    /**
     * 适当性元素类别枚举
     */
    //风险等级
    public static final String CORP_RISK_LEVEL = "0";
    //投资期限
    public static final String EN_INVEST_TERM = "1";
    //投资品种
    public static final String EN_INVEST_KIND = "2";
    //收益类型
    public static final String INCOME_TYPE = "3";
    //最大亏损率
    public static final String MAX_DEFICIT_RATE = "4";


    /**
     * 适当性匹配标志
     */
    public static final String SUIT_FLAG_PASS = "0";
    public static final String SUIT_FLAG_FORBIDEN = "2";

    /**
     * 新意api接口地址
     */
    public static final String AGREEMENT_XY_URL = "comp.agreement.xy.url";

    /**
     * 新意查询接口地址
     */
    public static final String AGREEMENT_XY_API_QUERY_URL = "comp.agreement.xy.api.query.url";

    /**
     * 新意下载接口地址
     */
    public static final String AGREEMENT_XY_API_DOWNLOAD_URL = "comp.agreement.xy.api.download.url";

    /**
     * 文件存储路径配置
     */
    public static final String FILE_LOCAL_PATH = "comp.file.local.path";

    /**
     * 调用获取访问令牌接口需要的APP_ID
     */
    public static final String AGREEMENT_API_500004_APPID = "comp.agreement.api.500004.appId";

    /**
     * 调用获取访问令牌接口需要的ACCESS_ID
     */
    public static final String AGREEMENT_API_500004_ACCESS_ID = "comp.agreement.api.500004.accessId";


    /**
     * 风险揭示提示书下载接口APP_ID
     */
    public static final String AGREEMENT_API_500011_APPID = "comp.agreement.api.500011.appId";

    /**
     * 风险揭示提示书下载接口depCode参数
     */
    public static final String AGREEMENT_API_500011_DEPCODE = "comp.agreement.api.500011.depCode";


    /**
     * 产品协议子系统编号和业务类型配置,配置形式为：
     * SUBSYS_NO,BUSIN_TYPE
     */
    public static final String PRODUCT_AGREEMENT_SUBSYS_NO_AND_BUSIN_TYPE = "comp.agreement.product.subsysNoAndBusinType";

    /**
     * 普通银行协议子系统编号和业务类型配置,配置形式为：
     * SUBSYS_NO1,BUSIN_TYPE1;SUBSYS_NO2,BUSIN_TYPE2
     */
    public static final String GENERAL_BANK_AGREEMENT_SUBSYS_NO_AND_BUSIN_TYPE = "comp.agreement.generalbank.subsysNoAndBusinType";

    /**
     * 信用银行协议子系统编号和业务类型配置,配置形式为：
     * SUBSYS_NO1,BUSIN_TYPE1;SUBSYS_NO2,BUSIN_TYPE2
     */
    public static final String CREDIT_BANK_AGREEMENT_SUBSYS_NO_AND_BUSIN_TYPE = "comp.agreement.creditbank.subsysNoAndBusinType";

}
