package com.cairh.cpe.component.common.data.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 问卷平级规则流水表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("EXAMRESULTCOMMENTjour")
public class ExamResultCommentJour extends Model<ExamResultCommentJour> {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId("serial_id")
    private String serial_id;

    /**
     * 问卷平级规则ID
     */
    @TableField("examresultcomment_id")
    private String examresultcomment_id;

    /**
     * 最大分数
     */
    @TableField("max_score")
    private Integer max_score;

    /**
     * 最低分数
     */
    @TableField("min_score")
    private Integer min_score;

    /**
     * 结果类型
     */
    @TableField("result_comment")
    private String result_comment;

    /**
     * 风险等级
     */
    @TableField("risk_level")
    private String risk_level;

    @TableField("risk_position")
    private Integer risk_position;

    /**
     * 投资建议
     */
    @TableField("invest_advice")
    private String invest_advice;

    /**
     * 数据ID
     */
    @TableField("exampaper_id")
    private String exampaper_id;

    /**
     * 有效期天数
     */
    @TableField("valid_days")
    private Integer valid_days;

    /**
     * 创建日期时间
     */
    @TableField(value = "create_datetime")
    private Date create_datetime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String create_by;

    /**
     * 修改时间
     */
    @TableField(value = "modify_datetime")
    private Date modify_datetime;

    /**
     * 修改人
     */
    @TableField("modify_by")
    private String modify_by;

    /**
     * 归历史标识
     */
    @TableField(value = "tohis_flag")
    private String tohis_flag;

    /**
     * 归历史时间
     */
    @TableField("tohis_datetime")
    private Date tohis_datetime;

    @TableField(value = "business_flag")
    private Integer business_flag;

    /**
     * 清算日期
     */
    @TableField("date_clear")
    private Integer date_clear;

    @Override
    public Serializable pkVal() {
        return this.serial_id;
    }

}
