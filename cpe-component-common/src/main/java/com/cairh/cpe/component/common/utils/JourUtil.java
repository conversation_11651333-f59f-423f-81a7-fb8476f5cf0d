package com.cairh.cpe.component.common.utils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cairh.cpe.component.common.constant.Constant;
import lombok.SneakyThrows;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.io.Serializable;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.function.Supplier;

/**
 * 流水记录工具
 *
 * <AUTHOR>
 * @since 2022-08-02
 */
public class JourUtil {

    private static ThreadPoolTaskExecutor jourExecutor;

    public static void writeJour(Supplier supplier) {
        if (Objects.isNull(jourExecutor)) {
            jourExecutor = SpringUtil.getBean("jourExecutor", ThreadPoolTaskExecutor.class);
        }
        jourExecutor.execute(() -> supplier.get());
    }

    /**
     * 通用写流水
     *
     * @param obj
     * @param target
     * @param business_flag
     * @param ref_key_method_name
     */
    public static void writeJour(Object obj, Class target, Integer business_flag, String ref_key_method_name) {
        if (Objects.isNull(jourExecutor)) {
            jourExecutor = SpringUtil.getBean("jourExecutor", ThreadPoolTaskExecutor.class);
        }
        jourExecutor.execute(() -> saveJour(obj, target, business_flag, null, ref_key_method_name));
    }

    /**
     * 通用写流水
     *
     * @param obj
     * @param target
     * @param business_flag
     * @param source_primary_key
     * @param ref_key_method_name
     */
    public static void writeJour(Object obj, Class target, Integer business_flag, String source_primary_key, String ref_key_method_name) {
        if (Objects.isNull(jourExecutor)) {
            jourExecutor = SpringUtil.getBean("jourExecutor", ThreadPoolTaskExecutor.class);
        }
        jourExecutor.execute(() -> saveJour(obj, target, business_flag, source_primary_key, ref_key_method_name));
    }

    /**
     * 通用批量写流水(只针对主键为serial_id)
     * @param sourceService
     * @param jourService
     * @param list
     * @param target
     * @param business_flag
     * @param ref_key_method_name
     */
    public static void writeJourBatch(IService sourceService, IService jourService, List list, Class target, Integer business_flag, String ref_key_method_name) {
        if (Objects.isNull(jourExecutor)) {
            jourExecutor = SpringUtil.getBean("jourExecutor", ThreadPoolTaskExecutor.class);
        }
        jourExecutor.execute(() -> saveJourBatch(sourceService, jourService, list, target, business_flag, ref_key_method_name));
    }

    @SneakyThrows
    public static boolean saveJour(Object obj, Class target, Integer business_flag, String source_primary_key, String ref_key_method_name) {
        if (Objects.isNull(obj)) {
            return false;
        }
        Method objGetSerial_id = obj.getClass().getDeclaredMethod("get" + (source_primary_key == null ? "Serial_id" : source_primary_key));
        String source_serial_id = (String) objGetSerial_id.invoke(obj);
        if (Constant.BUSINESS_FLAG_MOD == business_flag) {
            Method objGetById = getMethod(obj.getClass(), "selectById", Serializable.class);
            Object o = objGetById.invoke(obj, source_serial_id);
            BeanUtil.copyProperties(o, obj, CopyOptions.create().ignoreNullValue().setOverride(false));
        }
        Object jour = BaseBeanUtil.copyProperties(obj, target);
        Method ref_key_method = target.getDeclaredMethod(ref_key_method_name, String.class);
        ref_key_method.invoke(jour, source_serial_id);
        Method setTargetSerialId = target.getDeclaredMethod("setSerial_id", String.class);
        setTargetSerialId.invoke(jour, new Object[]{null});
        Method setTargetBusiness_flag = target.getDeclaredMethod("setBusiness_flag", Integer.class);
        setTargetBusiness_flag.invoke(jour, business_flag);
        Method insertTarget = getMethod(target, "insert");
        insertTarget.invoke(jour);
        return true;
    }

    @SneakyThrows
    public static boolean saveJourBatch(IService sourceService, IService jourService, List list, Class target, Integer business_flag, String ref_key_method_name) {
        if (CollectionUtils.isEmpty(list)) {
            return false;
        }
        String source_primary_key = "getSerial_id";
        Method objGetSerial_id = list.get(0).getClass().getDeclaredMethod(source_primary_key);
        ArrayList<String> source_id_list = new ArrayList<>();
        for (Object x : list) {
            source_id_list.add((String) objGetSerial_id.invoke(x));
        }
        if (Constant.BUSINESS_FLAG_MOD == business_flag) {

            list = sourceService.listByIds(source_id_list);
        }
        List jour_list = BaseBeanUtil.copyToList(list, target);
        Method setTargetSerialId = target.getDeclaredMethod("setSerial_id", String.class);
        Method getTargetSerialId = target.getDeclaredMethod("getSerial_id");
        Method ref_key_method = target.getDeclaredMethod(ref_key_method_name, String.class);
        for (Object jour : jour_list) {
            ref_key_method.invoke(jour, getTargetSerialId.invoke(jour));
            setTargetSerialId.invoke(jour, new Object[]{null});
            Method setTargetBusiness_flag = target.getDeclaredMethod("setBusiness_flag", Integer.class);
            setTargetBusiness_flag.invoke(jour, business_flag);
        }
        if (CollectionUtils.isNotEmpty(jour_list)) {
            jourService.saveBatch(jour_list);
        }
        return true;
    }


    public static Method getMethod(Class clazz, String methodName, Class<?>... classes) {
        Method method = null;
        try {
            method = clazz.getDeclaredMethod(methodName, classes);
        } catch (NoSuchMethodException e) {
            try {
                method = clazz.getMethod(methodName, classes);
            } catch (NoSuchMethodException ex) {
                if (clazz.getSuperclass() == null) {
                    return method;
                } else {
                    method = getMethod(clazz.getSuperclass(), methodName, classes);
                }
            }
        }
        return method;
    }
}