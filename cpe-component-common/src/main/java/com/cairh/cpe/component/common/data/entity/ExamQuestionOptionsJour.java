package com.cairh.cpe.component.common.data.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 试题选项流水
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("examquestionoptionsjour")
public class ExamQuestionOptionsJour extends Model<ExamQuestionOptionsJour> {

    private static final long serialVersionUID = 1L;

    /**
     * 试题选项流水ID
     */
    @TableId("serial_id")
    private String serial_id;

    /**
     * 试题选项ID
     */
    @TableField("examquestionoptions_id")
    private String examquestionoptions_id;

    /**
     * 柜台试题编号
     */
    @TableField("option_no")
    private Integer option_no;

    /**
     * 选项内容
     */
    @TableField("option_content")
    private String option_content;

    /**
     * 是否需要具体原因
     */
    @TableField("option_need_reason")
    private String option_need_reason;

    /**
     * 问卷编号
     */
    @TableField("exampaper_id")
    private String exampaper_id;

    /**
     * 分数
     */
    @TableField("score")
    private BigDecimal score;

    /**
     * 问卷试题ID
     */
    @TableField("examquestion_id")
    private String examquestion_id;

    /**
     * 选项关联指标值
     */
    @TableField("relation_value")
    private String relation_value;

    /**
     * 最低风险标识
     */
    @TableField("min_risk_level_flag")
    private String min_risk_level_flag;

    /**
     * 菜单排序信息
     */
    @TableField("order_no")
    private Long order_no;

    /**
     * 创建日期时间
     */
    @TableField(value = "create_datetime")
    private Date create_datetime;

    @TableField("create_by")
    private String create_by;

    /**
     * 修改时间
     */
    @TableField(value = "modify_datetime")
    private Date modify_datetime;

    @TableField("modify_by")
    private String modify_by;

    /**
     * 选中提示
     */
    @TableField("selected_tip")
    private String selected_tip;

    /**
     * 归历史标志
     */
    @TableField(value = "tohis_flag")
    private String tohis_flag;

    /**
     * 归历史时间
     */
    @TableField("tohis_datetime")
    private Date tohis_datetime;

    @TableField("status")
    private String status;

    @TableField(value = "business_flag")
    private Integer business_flag;

    /**
     * 清算日期
     */
    @TableField("date_clear")
    private Integer date_clear;

    @Override
    public Serializable pkVal() {
        return this.serial_id;
    }

}
