package com.cairh.cpe.component.common.form;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.cairh.cpe.component.common.data.entity.AuthManufacturerConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 高清人脸比对配置 分页查询入参
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ToString(callSuper = true)
public class AuthManufacturerConfigForm extends BasePage<AuthManufacturerConfig>{
    /**
     * ID
     */
    private Integer serial_no;
    /**
     * 实现名
     */
    private  String interface_impl;
    /**
     * 最低分数
     */
    private BigDecimal score_start;
    /**
     * 最高分数
     */
    private BigDecimal score_end;
    /**
     * 备注
     */
    private  String remark;
}
