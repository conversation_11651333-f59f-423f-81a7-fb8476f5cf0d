package com.cairh.cpe.component.common.form.support;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022-06-07
 */
@Data
public class AgreementRel {

    /**
     * 协议业务参数id  编辑时此字段需传给后端
     */
    private String serial_id;

    /**
     * 协议模板id
     */
    private String elecagreemodel_id;

    /**
     * 协议类型
     */
    private String agreement_type;

    /**
     * 强制阅读标识
     */
    private String agreement_read_type;

    /**
     * 强制阅读时间
     */
    private String force_read_time;

    /**
     * 协议名称
     */
    private String agreement_name;

    /**
     * 拓展名称
     */
    private String ex_name;

    /**
     * 协议版本
     */
    private String agreement_version;

    /**
     * 协议编号
     */
    private String agreement_no;

    /**
     * 规则表达式
     */
    private String regular_expre;

    /**
     * 自动阅读模式
     */
    private String auto_read_type;

    /**
     * 状态
     */
    private String status;

    /**
     * 子系统编号
     */
    @TableField("subsys_no")
    private Integer subsys_no;

    /**
     * 业务编号
     */
    @TableField("busin_type")
    private Integer busin_type;

    /**
     * 机构标识
     */
    @TableField("organ_flag")
    private String organ_flag;

    /**
     * 排序
     */
    private String order_no;

}