package com.cairh.cpe.component.common.form.support;



import com.cairh.cpe.component.common.data.entity.VideoPriorityConfig;

import java.util.Collection;

public class PriorityFilterResult {

    private boolean ambiguous = false;

    private Collection<VideoPriorityConfig> ambiguousCollection;

    private String info;

    public Collection<VideoPriorityConfig> getAmbiguousCollection() {
        return ambiguousCollection;
    }

    public void setAmbiguousCollection(Collection<VideoPriorityConfig> ambiguousCollection) {
        this.ambiguousCollection = ambiguousCollection;
    }

    public String getInfo() {
        return info;
    }

    public void setInfo(String info) {
        this.info = info;
    }

    public boolean isAmbiguous() {
        return ambiguous;
    }

    public void setAmbiguous(boolean ambiguous) {
        this.ambiguous = ambiguous;
    }

}