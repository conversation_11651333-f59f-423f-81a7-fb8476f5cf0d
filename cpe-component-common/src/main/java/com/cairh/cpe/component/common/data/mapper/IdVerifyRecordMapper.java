package com.cairh.cpe.component.common.data.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cairh.cpe.component.common.data.entity.IdVerifyRecord;
import com.cairh.cpe.component.common.form.VerifyDetailQueryForm;
import com.cairh.cpe.component.common.form.VerifyQueryForm;
import com.cairh.cpe.component.common.form.response.VerifyDetailQueryResp;
import com.cairh.cpe.component.common.form.response.VerifyQueryResp;

/**
 * <p>
 * 身份认证记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-13
 */
public interface IdVerifyRecordMapper extends BaseMapper<IdVerifyRecord> {

    /**
     * 公安认证查询
     * @return
     */
    Page<VerifyQueryResp> verifyQuery(VerifyQueryForm param);

    Page<VerifyDetailQueryResp> verifyDetailQuery(VerifyDetailQueryForm param);
}
