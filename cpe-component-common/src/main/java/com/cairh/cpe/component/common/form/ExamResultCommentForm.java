package com.cairh.cpe.component.common.form;

import com.cairh.cpe.component.common.data.entity.ExamResultComment;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;


/**
 * 问卷评级规则 表单
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ToString(callSuper = true)
public class ExamResultCommentForm extends BasePage<ExamResultComment> {

    private String serial_id;

    private Double max_score;

    private Double min_score;

    private String result_comment;

    private String risk_level;

    private Integer risk_position;

    private String invest_advice;

    private String exampaper_id;

    private Integer valid_days;

    private Date create_datetime;

    private String create_by;

    private Date modify_datetime;

    private String modify_by;

    private String tohis_flag;

    private String tohis_datetime;
}
