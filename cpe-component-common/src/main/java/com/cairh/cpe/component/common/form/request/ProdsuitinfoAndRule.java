package com.cairh.cpe.component.common.form.request;

import com.cairh.cpe.component.common.data.entity.ProdSuitInfo;
import com.cairh.cpe.component.common.data.entity.SuitMatchRule;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * 适当性+适当性规则
 * <AUTHOR>
 * @since 2022-10-20
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString(callSuper = true)
public class ProdsuitinfoAndRule  extends ProdSuitInfo {

    /**
     * 适当性规则
     */
    private List<SuitMatchRule> entityList;
}