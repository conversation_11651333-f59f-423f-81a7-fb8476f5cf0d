package com.cairh.cpe.component.common.form.response;

import com.cairh.cpe.common.backend.annotation.Desensitize;
import com.cairh.cpe.component.common.cache.DataConvert;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import static com.cairh.cpe.common.backend.masking.mode.DataMaskingMode.MOBILE_TEL;

@Data
public class VideoRecordStatisticsResp implements Serializable {

    private static final long serialVersionUID = -3050402251519932649L;

    //客户姓名
    private String full_name;
    //手机号
    @Desensitize(mode = MOBILE_TEL)
    private String mobile_tel;
    //客户营业部
    private String branch_no;
    @DataConvert(code_type = DataConvert.BRANCH)
    private String branch_no_tranfer;
    public String getBranch_no_tranfer() {
        if(branch_no_tranfer == null){
            return branch_no;
        }
        return branch_no_tranfer;
    }
    //APPID 接入方式
    private String app_id;

    @DataConvert(code_type = DataConvert.DICT, code_dict = "app_id")
    private String app_id_tranfer;

    public String getApp_id_tranfer() {
        if(app_id_tranfer == null){
            return app_id;
        }
        return app_id_tranfer;
    }

    //操作员编号
    private String operator_no;

    //翻译
    @DataConvert(code_type = DataConvert.OPERATORINFO)
    private String operator_no_tranfer;

    public String getOperator_no_tranfer() {
        if(operator_no_tranfer == null){
            return operator_no;
        }
        return operator_no_tranfer;
    }

    //操作员营业部
    private String op_branch_no;

    @DataConvert(code_type = DataConvert.BRANCH)
    private String op_branch_no_tranfer;

    public String getOp_branch_no_tranfer() {
        if(op_branch_no_tranfer == null){
            return op_branch_no;
        }
        return op_branch_no_tranfer;
    }

    //操作员姓名
    private String operator_name;

    //业务类型
    private String busin_type;

    //业务名称
    private String busin_name;

    //子系统编号
    private Integer subsys_no;

    //业务流水唯一ID
    private String unique_id;

    //机构标志
    private String organ_flag;
    //视频结果
    private String video_result;

    //子系统编号名称
    @DataConvert(code_type = DataConvert.DICT, code_dict = "subsys_no")
    private String subsys_no_tranfer;

    public String getSubsys_no_tranfer() {
        if(subsys_no_tranfer == null){
            return String.valueOf(subsys_no);
        }
        return subsys_no_tranfer;
    }
    //累计发起次数
    private Integer total_num;

    private List<RecordInfo> RecordInfos;

    //视频发起时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date start_datetime;

    //视频见证时间
    private String witness_datetime;

    //视频等待时间
    private String wait_datetime;

    //总间隔时间
    private String total_interval_datetime;
}
