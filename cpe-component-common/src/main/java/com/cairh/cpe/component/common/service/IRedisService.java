package com.cairh.cpe.component.common.service;

import org.springframework.validation.annotation.Validated;

import java.util.List;
import java.util.Set;

/**
 * redis相关操作服务类
 *
 * <AUTHOR>
 * @since SP0-PACK1
 */
@Validated
public interface IRedisService {

    void addToRedisSetById(String type, Double score, String id);

    long getMillisecond();

    Double getScoreById(String type, String id);

    int getSetCount(String type);

    Set<String> getSortedSet(String type);

    Boolean publish(String channel, String message);

    boolean removeFromRedisSetById(String type, String id);

    Long zrank(String setName, String key);

    Boolean zrem(String key, String member);

    Boolean srem(String key, String member);

}
