package com.cairh.cpe.component.common.form;


import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.cairh.cpe.component.common.data.entity.AgreeModelBusiness;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.ArrayList;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper=false)
@ToString(callSuper = true)
public class AgreemodelbusinessForm  extends BasePage<AgreeModelBusiness> {
    {
        OrderItem orderItem1 = new OrderItem();
        OrderItem orderItem2 = new OrderItem();
        orderItem1.setAsc(false);
        orderItem2.setAsc(false);
        orderItem1.setColumn("t1.modify_datetime");
        orderItem2.setColumn("t1.serial_id");
        ArrayList<OrderItem> orderItems = new ArrayList<>();
        orderItems.add(orderItem1);
        orderItems.add(orderItem2);
        super.setOrders(orderItems);
    }

    /**
     * 协议业务参数id
     */
    private String serial_id;

    /**
     * 协议模板id
     */
    private String elecagreemodel_id;

    /**
     * 子系统编号
     */
    private Integer subsys_no;

    /**
     * 业务编号
     */
    private Integer busin_type;

    /**
     * 机构标识
     */
    private String organ_flag;

    private String agreement_type;

    /**
     * 强制阅读标识
     */
    private String force_read_flag;

    /**
     * 强制阅读时间
     */
    private String force_read_time;

    private String create_by;

    /**
     * 创建日期时间
     */
    @JsonFormat(pattern = "yyyyMMdd HHmmss")
    private Date create_datetime;

    private String modify_by;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyyMMdd HHmmss")
    private Date modify_datetime;

    /**
     * 状态
     */
    private String status;

    /**
     * 规则表达式
     */
    private String regular_expre;

    /**
     * 归历史标志
     */
    private String tohis_flag;

    /**
     * 归历史时间
     */
    private Date tohis_datetime;

    /**
     * 强制阅读标识
     */
    private String agreement_read_type;

    /**
     * 协议名称
     */
    private String agreement_name;

    /**
     * 拓展名称
     */
    private String ex_name;

    /**
     * 协议版本
     */
    private String agreement_version;

    /**
     * 协议编号
     */
    private String agreement_no;


}
