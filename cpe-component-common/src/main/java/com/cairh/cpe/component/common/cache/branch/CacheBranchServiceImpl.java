package com.cairh.cpe.component.common.cache.branch;

import com.cairh.cpe.esb.base.rpc.IVBaseAllBranchDubboService;
import com.cairh.cpe.esb.base.rpc.dto.req.VBaseAllBranchQryRequest;
import com.cairh.cpe.esb.base.rpc.dto.resp.VBaseAllBranchQryResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年01月11日 13:47
 */
@Slf4j
@Component
public class CacheBranchServiceImpl implements ICacheBranchService{

	@DubboReference(check = false, lazy = true)
	private IVBaseAllBranchDubboService ivBaseAllBranchDubboService;

	@Override
	@Cacheable(value = "compAllbranch", key = "#root.targetClass.simpleName+'.'+#root.method.name")
	public List<VBaseAllBranchQryResponse> getAllbranchs() {
		VBaseAllBranchQryRequest vBaseAllBranchQryRequest = new VBaseAllBranchQryRequest();
		List<VBaseAllBranchQryResponse> vBaseAllBranchQryResponseList = this.ivBaseAllBranchDubboService.baseDataQryAllBranch(vBaseAllBranchQryRequest);
		return vBaseAllBranchQryResponseList;
	}

	@Override
	@Scheduled(fixedDelay = 10 * 60 * 1000)
	@CacheEvict(value = "compAllbranch", allEntries = true)
	public void refresh() {
		log.debug("Cache[compAllbranch]执行清除.");
	}
}
