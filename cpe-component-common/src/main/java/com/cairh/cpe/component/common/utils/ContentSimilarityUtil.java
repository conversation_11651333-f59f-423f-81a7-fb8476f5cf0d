package com.cairh.cpe.component.common.utils;

import org.apache.commons.lang3.StringUtils;

import java.text.NumberFormat;
import java.util.Locale;

/**
 * 文本对比工具类
 */
public class ContentSimilarityUtil {

    private static int compare(String str, String target) {
        int d[][];              // 矩阵
        int n = str.length();
        int m = target.length();
        int i;                  // 遍历str的
        int j;                  // 遍历target的
        char ch1;               // str的
        char ch2;               // target的
        int temp;               // 记录相同字符,在某个矩阵位置值的增量,不是0就是1
        if (n == 0) {
            return m;
        }
        if (m == 0) {
            return n;
        }
        d = new int[n + 1][m + 1];
        for (i = 0; i <= n; i++) {                       // 初始化第一列
            d[i][0] = i;
        }

        for (j = 0; j <= m; j++) {                       // 初始化第一行
            d[0][j] = j;
        }

        for (i = 1; i <= n; i++) {                       // 遍历str
            ch1 = str.charAt(i - 1);
            // 去匹配target
            for (j = 1; j <= m; j++) {
                ch2 = target.charAt(j - 1);
                if (ch1 == ch2 || ch1 == ch2 + 32 || ch1 + 32 == ch2) {
                    temp = 0;
                } else {
                    temp = 1;
                }
                // 左边+1,上边+1, 左上角+temp取最小
                d[i][j] = min(d[i - 1][j] + 1, d[i][j - 1] + 1, d[i - 1][j - 1] + temp);
            }
        }
        return d[n][m];
    }

    private static int min(int one, int two, int three) {
        return (one = one < two ? one : two) < three ? one : three;
    }

    private static String removeSign(String str) {
        StringBuffer sb = new StringBuffer();
        for (char item : str.toCharArray())
            if (charReg(item)) {
                //System.out.println("--"+item);
                sb.append(item);
            }
        return sb.toString();
    }


    private static boolean charReg(char charValue) {
        return (charValue >= 0x4E00 && charValue <= 0X9FA5)
                || (charValue >= 'a' && charValue <= 'z')
                || (charValue >= 'A' && charValue <= 'Z')
                || (charValue >= '0' && charValue <= '9');
    }

    /**
     * 获取两字符串的相似度
     */

    public static float getSimilarityRatio(String str, String target) {
        String newStr = removeSign(str);
        String newTarget = removeSign(target);
        return 1 - (float) compare(newStr, newTarget) / Math.max(newStr.length(), newTarget.length());
    }

    /**
     * 相似度转百分比
     */
    private static String similarityResult(float result) {
        return NumberFormat.getPercentInstance(new Locale("en ", "US ")).format(result);
    }

    // 比较相识度扩展，不足长度10的，补10位
    public static float contentSimilarityEx(String src, String target) {
        if (src == null || StringUtils.isBlank(src.trim())) {
            return 0;
        }
        if (target == null || StringUtils.isBlank(target.trim())) {
            return 0;
        }
        int tlen = 11;
        if (src.length() > 2 && src.length() < tlen) {
            while (src.length() < tlen) {
                src = src + "0";
            }
        }
        if (target.length() > 2 && target.length() < tlen) {
            while (target.length() < tlen) {
                target = target + "0";
            }
        }
        return ContentSimilarityUtil.getSimilarityRatio(src, target);
    }

}
