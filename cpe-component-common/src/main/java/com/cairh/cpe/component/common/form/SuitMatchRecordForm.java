package com.cairh.cpe.component.common.form;

import com.cairh.cpe.component.common.data.entity.SuitMatchRecord;
import com.cairh.cpe.component.common.data.entity.SuitMatchRule;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;

/**
 * 适当性匹配流水分页查询入参
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ToString(callSuper = true)
public class SuitMatchRecordForm extends BasePage<SuitMatchRecord>{
    /**
     * 操作时间开始
     */
    private String operator_time_start;
    /**
     * 操作时间结束
     */
    private String operator_time_end;
    /**
     * 时间范围 逗号隔开
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date[] curr_date_range;

    private String organ_flag;

    /**
     * 客户编号
     */
    private String client_id;
    /**
     * 客户类型
     */
    private String client_type;
    /**
     * 客户姓名
     */
    private String client_name;
    /**
     * 证件类别
     */
    private String id_kind;
    /**
     * 证件号码
     */
    private String id_no;
    /**
     * 修改结果 暂时不用
     */
    private String modify_flag;
}
