package com.cairh.cpe.component.common.form;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cairh.cpe.component.common.data.entity.AuthorizeInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.ArrayList;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AuthorizeinfoForm extends Page<AuthorizeInfo> {

    {
        ArrayList<OrderItem> orderItems = new ArrayList<>();
        super.setOrders(orderItems);
    }

    /**
     * APP_ID
     */
    private String app_id;

    /**
     * 密钥
     */
    private String secret_key;

    /**
     * 状态
     */
    private String status;

    /**
     * 有效期结束日期
     */
    private String expire_date_str;
}
