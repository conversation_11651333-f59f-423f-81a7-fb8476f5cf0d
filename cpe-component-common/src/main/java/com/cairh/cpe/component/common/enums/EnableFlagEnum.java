package com.cairh.cpe.component.common.enums;

import lombok.Getter;

/**
 * 启用非启用枚举
 *
 * <AUTHOR>
 * @since 2023/7/25 10:38
 */
@Getter
public enum EnableFlagEnum {

    TRUE("1", "启用"),
    FALSE("0", "不启用");

    private final String enableFlag;
    private final String enableFlagDesc;

    EnableFlagEnum(String enableFlag, String enableFlagDesc) {
        this.enableFlag = enableFlag;
        this.enableFlagDesc = enableFlagDesc;
    }

}
