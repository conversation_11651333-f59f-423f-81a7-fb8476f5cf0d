package com.cairh.cpe.component.common.form;

import com.cairh.cpe.component.common.data.entity.SuitMatchRule;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 适当性匹配规则分页查询入参
 * <AUTHOR>
 * @since 2022-05-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ToString(callSuper = true)
public class SuitmatchruleForm extends BasePage<SuitMatchRule> {
    /**
     * ID
     */
    private String serial_id;

    /**
     * 产品TA编号
     */
    private String prodta_no;

    /**
     * 产品代码
     */
    private String prod_code;

    /**
     * 适当性元素类别
     */
    private String suit_prop_type;

    /**
     * 允许客户适当性元素
     */
    private String en_cust_suit_prop;

    /**
     * 适当性匹配标志
     */
    private String suit_flag;

    /**
     * 适当性匹配建议
     */
    private String suit_suggest;

    /**
     * 规则表达式
     */
    private String regular_expre;

    /**
     * 关系类型
     */
    private String rule_logic;
}