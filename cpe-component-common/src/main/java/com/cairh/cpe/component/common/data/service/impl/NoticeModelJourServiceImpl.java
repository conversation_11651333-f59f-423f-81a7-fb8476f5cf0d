package com.cairh.cpe.component.common.data.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.component.common.data.entity.NoticeModelJour;
import com.cairh.cpe.component.common.data.mapper.NoticeModelJourMapper;
import com.cairh.cpe.component.common.data.service.INoticeModelJourService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 消息模板流水表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-07
 */
@Service
public class NoticeModelJourServiceImpl extends ServiceImpl<NoticeModelJourMapper, NoticeModelJour> implements INoticeModelJourService {

}
