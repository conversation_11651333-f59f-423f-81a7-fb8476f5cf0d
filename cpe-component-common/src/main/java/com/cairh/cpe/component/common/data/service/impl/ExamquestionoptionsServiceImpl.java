package com.cairh.cpe.component.common.data.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.component.common.utils.BaseBeanUtil;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.context.constant.ErrorCode;
import com.cairh.cpe.component.common.constant.Constant;
import com.cairh.cpe.component.common.data.entity.ExamPaper;
import com.cairh.cpe.component.common.data.entity.ExamQuestionOptions;
import com.cairh.cpe.component.common.data.entity.ExamQuestionOptionsJour;
import com.cairh.cpe.component.common.data.entity.ExamTestResult;
import com.cairh.cpe.component.common.data.mapper.ExampaperMapper;
import com.cairh.cpe.component.common.data.mapper.ExamquestionoptionsMapper;
import com.cairh.cpe.component.common.data.service.IExamQuestionOptionsService;
import com.cairh.cpe.component.common.data.service.IExamQuestionOptionsJourService;
import com.cairh.cpe.component.common.data.service.IExamTestResultService;
import com.cairh.cpe.component.common.utils.JourUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 试题选项 服务实现类
 * </p>
 */
@Service
public class ExamquestionoptionsServiceImpl extends ServiceImpl<ExamquestionoptionsMapper, ExamQuestionOptions> implements IExamQuestionOptionsService {

    @Autowired
    private IExamQuestionOptionsJourService examquestionoptionsjourService;

    @Autowired
    private ExampaperMapper exampaperMapper;

    @Autowired
    private IExamTestResultService examtestresultService;

    @Override
    public void baseSave(BaseUser baseUser, List<ExamQuestionOptions> entityList) {
        entityList.forEach(entity -> {
            entity.setCreate_by(baseUser.getUser_name());
            entity.setModify_by(baseUser.getStaff_no());
        });
        this.saveBatch(entityList);
        JourUtil.writeJour(() -> saveJour(entityList, false, Constant.BUSINESS_FLAG_ADD));
    }

    public void checkIfDelete(String paper_id) {
        if (StringUtils.isNotBlank(paper_id)) {
            ExamPaper exampaper = exampaperMapper.selectById(paper_id);
            if (Constant.COMMON_VALID_STATUS.equals(exampaper.getStatus())) {
                throw new BizException(ErrorCode.ERR_SYSWARNING, "已经绑定有效问卷，不允许删除！");
            }
        }
        //校验是否可以删除
        Integer count = Math.toIntExact(examtestresultService.lambdaQuery().eq(ExamTestResult::getExampaper_id, paper_id).count());
        if (count > 0) {
            throw new BizException(ErrorCode.ERR_SYSWARNING, "问卷已有作答记录不可以删除");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveOrUpdateOrDelBatch(BaseUser baseUser, List<ExamQuestionOptions> entityList) {
        if (CollectionUtils.isEmpty(entityList)) {
            return true;
        }
        String examquestionId = entityList.get(0).getExamquestion_id();
        List<String> serialIdList = entityList.stream().filter(x -> StringUtils.isNotBlank(x.getSerial_id()))
                .map(ExamQuestionOptions::getSerial_id).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(serialIdList)) {
            List<ExamQuestionOptions> list = lambdaQuery().eq(ExamQuestionOptions::getExamquestion_id, examquestionId)
                    .notIn(ExamQuestionOptions::getSerial_id, serialIdList).list();
            if (CollectionUtils.isNotEmpty(list)) {
                checkIfDelete(list.get(0).getExampaper_id());
                deleteBatch(list);
            }
        } else {
            remove(new QueryWrapper<ExamQuestionOptions>().lambda().eq(ExamQuestionOptions::getExamquestion_id, examquestionId));
        }
        List<String> exist_list = lambdaQuery().eq(ExamQuestionOptions::getExamquestion_id, examquestionId).list().stream().map(ExamQuestionOptions::getSerial_id).collect(Collectors.toList());
        List<ExamQuestionOptions> insertList = new ArrayList<>();
        List<ExamQuestionOptions> updateList = new ArrayList<>();
        entityList.forEach(entity -> {
            if (exist_list.contains(entity.getSerial_id())) {
                entity.setModify_by(baseUser.getStaff_no());
                updateList.add(entity);
            } else {
                entity.setCreate_by(baseUser.getStaff_no());
                insertList.add(entity);
            }

        });
        if (CollectionUtils.isNotEmpty(insertList)) {
            saveBatch(insertList);
            JourUtil.writeJour(() -> saveJour(entityList, true, Constant.BUSINESS_FLAG_ADD));
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            updateBatchById(updateList);
            JourUtil.writeJour(() -> saveJour(entityList, true, Constant.BUSINESS_FLAG_MOD));
        }
        return true;
    }

    public List<ExamQuestionOptions> deleteByPaper(String paper_id) {
        List<ExamQuestionOptions> examquestionoptionsList = lambdaQuery().eq(ExamQuestionOptions::getExampaper_id, paper_id)
                .select(ExamQuestionOptions::getSerial_id)
                .list();
        deleteBatch(examquestionoptionsList);
        return examquestionoptionsList;
    }

    public List<ExamQuestionOptions> deleteByQuestion(String question_id) {
        List<ExamQuestionOptions> examquestionoptionsList = lambdaQuery().eq(ExamQuestionOptions::getExamquestion_id, question_id)
                .select(ExamQuestionOptions::getSerial_id)
                .list();
        deleteBatch(examquestionoptionsList);
        return examquestionoptionsList;
    }

    private void deleteBatch(List<ExamQuestionOptions> examquestionoptionsList) {
        if (CollectionUtils.isNotEmpty(examquestionoptionsList)) {
            List<String> collect = examquestionoptionsList.stream().map(x -> x.getSerial_id()).collect(Collectors.toList());
            removeByIds(collect);
        }
    }

    public boolean saveJour(List<ExamQuestionOptions> entityList, boolean isUpdate, Integer business_flag) {
        List<ExamQuestionOptionsJour> examquestionoptionsjourList = null;
        if (isUpdate) {
            List<String> idList = entityList.stream().map(x -> x.getSerial_id()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(idList)) {
                List<ExamQuestionOptions> examquestionoptionsList = listByIds(idList);
                examquestionoptionsjourList = BaseBeanUtil.copyToList(examquestionoptionsList, ExamQuestionOptionsJour.class);
            }
        } else {
            examquestionoptionsjourList =
                    BaseBeanUtil.copyToList(entityList, ExamQuestionOptionsJour.class);
        }
        if (CollectionUtils.isNotEmpty(examquestionoptionsjourList)) {
            examquestionoptionsjourList.forEach(x -> {
                x.setExamquestionoptions_id(x.getSerial_id());
                x.setSerial_id(null);
                x.setBusiness_flag(business_flag);
            });
            examquestionoptionsjourService.saveBatch(examquestionoptionsjourList);
        }
        return true;
    }

    public boolean saveJour(ExamQuestionOptions examquestionoptions, Integer business_flag) {
        ExamQuestionOptionsJour examquestionoptionsjour = BaseBeanUtil.copyProperties(examquestionoptions, ExamQuestionOptionsJour.class);
        examquestionoptionsjour.setBusiness_flag(business_flag);
        return examquestionoptionsjourService.save(examquestionoptionsjour);
    }
}
