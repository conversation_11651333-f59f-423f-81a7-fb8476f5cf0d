<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cairh.cpe.component.common.data.mapper.ExamtestresultMapper">
    <resultMap id="ExamTestResultMap" type="com.cairh.cpe.component.common.form.ExamTestResultDto">
        <result property="paper_type" column="paper_type"/>
        <result property="organ_flag" column="organ_flag"/>
        <result property="paper_name" column="paper_name"/>
        <result property="serial_id" column="serial_id"/>
        <result property="exampaper_id" column="exampaper_id"/>
        <result property="score" column="score"/>
        <result property="status" column="status"/>
        <result property="risk_begin_date" column="risk_begin_date"/>
        <result property="risk_end_date" column="risk_end_date"/>
        <result property="paper_answer" column="paper_answer"/>
        <result property="submit_datetime" column="submit_datetime"/>
        <result property="confirm_datetime" column="confirm_datetime"/>
        <result property="render_data" column="render_data"/>
        <result property="tohis_flag" column="tohis_flag"/>
        <result property="tohis_datetime" column="tohis_datetime"/>
        <result property="date_clear" column="date_clear"/>
        <result property="client_id" column="client_id"/>
        <result property="client_name" column="client_name"/>
        <result property="id_kind" column="id_kind"/>
        <result property="id_no" column="id_no"/>
    </resultMap>

    <select id="diySelectPageParam" resultMap="ExamTestResultMap">
        select
        e.paper_type,
        rui.organ_flag,
        e.paper_name,
        etr.serial_id,
        etr.exampaper_id,
        etr.score,
        etr.status,
        etr.risk_begin_date,
        etr.risk_end_date,
        etr.paper_answer,
        etr.submit_datetime,
        etr.confirm_datetime,
        etr.render_data,
        etr.tohis_flag,
        etr.tohis_datetime,
        etr.date_clear,
        rui.client_id,
        rui.client_name,
        rui.id_kind,
        rui.id_no
        from
        examtestresult etr
        left join riskuserinfo rui on rui.examtestresult_id = etr.serial_id
        left join exampaper e on e.serial_id = etr.exampaper_id
        where 1 = 1
        <if test="paper_type != null and paper_type != ''">
            and e.paper_type = #{paper_type}
        </if>
        <if test="organ_flag != null and organ_flag != ''">
            and rui.organ_flag = #{organ_flag}
        </if>
        <if test="paper_name != null and paper_name != ''">
            and e.paper_name like concat(concat('%',#{paper_name}),'%')
        </if>
        <if test="client_id != null and client_id != ''">
            and rui.client_id = #{client_id}
        </if>
        <if test="score != null and score != ''">
            and score = #{score}
        </if>
        <if test="branch_no != null and branch_no != ''">
            and branch_no = #{branch_no}
        </if>
        <if test="submit_datetime_start != null and submit_datetime_start != ''">
            and etr.submit_datetime &gt;=  to_date(#{submit_datetime_start},'yyyy-mm-dd HH24:mi:ss')
        </if>
        <if test="submit_datetime_end != null and submit_datetime_end != ''">
            and etr.submit_datetime &lt;= to_date(#{submit_datetime_end},'yyyy-mm-dd HH24:mi:ss')
        </if>
    </select>
</mapper>
