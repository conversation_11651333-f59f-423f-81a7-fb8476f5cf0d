<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cairh.cpe.component.common.data.mapper.ElecAgreeModelMapper">

    <select id="diySelectPage" resultType="com.cairh.cpe.component.common.data.entity.ElecAgreeModel">
        select * from
            (select * from elecagreemodel e where e.serial_id not in
                                                  (select a.serial_id from elecagreemodel a where a.agreement_status = '9' and a.VERIFY_STATUS = '0' UNION all
                                                   select b.serial_id from elecagreemodel b where b.VERIFY_STATUS = ' ')) t
        where 1 = 1
    </select>

    <select id="diySelectPageParam" resultType="com.cairh.cpe.component.common.data.entity.ElecAgreeModel">
        select * from
        (select * from elecagreemodel e where e.serial_id not in
                                             (select a.serial_id from elecagreemodel a where a.agreement_status = '9' and a.VERIFY_STATUS = '0' UNION all
                                              select b.serial_id from elecagreemodel b where b.VERIFY_STATUS = ' ')) t
        where 1 = 1
        <if test="agreement_no != null and agreement_no != ''">
            and agreement_no = #{agreement_no}
        </if>
        <if test="agreement_name != null and agreement_name != ''">
            and agreement_name like concat(concat('%',#{agreement_name}),'%')
        </if>
        <if test="verify_by != null and verify_by != ''">
            and verify_by = #{verify_by}
        </if>
        <if test="create_by != null and create_by != ''">
            and create_by = #{create_by}
        </if>
        <if test="agreement_status != null and agreement_status != ''">
            and agreement_status = #{agreement_status}
        </if>
        <if test="verify_status != null and verify_status != ''">
            and verify_status = #{verify_status}
        </if>
    </select>

</mapper>
