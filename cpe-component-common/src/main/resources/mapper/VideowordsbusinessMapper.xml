<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cairh.cpe.component.common.data.mapper.VideowordsbusinessMapper">

    <select id="diySelectPage" resultType="com.cairh.cpe.component.common.form.VideowordsbusinessDto">
        select
                t1.serial_id,
                t1.videowordsmodel_id,
                t1.subsys_no,
                t1.prod_code,
                t1.prodta_no,
                t1.busin_type,
                t1.organ_flag,
                t1.create_by,
                t1.create_datetime,
                t1.modify_by,
                t1.modify_datetime,
                t1.status,
                t1.regular_expre,
                t2.model_name,
                t2.model_type,
                t2.video_type
        from videowordsbusiness t1,videowordsmodel t2
        where t1.videowordsmodel_id = t2.serial_id
        <if test="serial_id != null and serial_id != ''">
            and t1.serial_id = #{serial_id}
        </if>
        <if test="prod_code != null and prod_code != ''">
            and t1.prod_code = #{prod_code}
        </if>
        <if test="subsys_no != null">
            and t1.subsys_no = #{subsys_no}
        </if>
        <if test="regular_expre != null">
            and t1.regular_expre like concat(concat('%',#{regular_expre}),'%')
        </if>
        <choose>
            <when test="status != null">
                and t1.status = #{status}
            </when>
            <otherwise>
                and t1.status = 8
            </otherwise>
        </choose>
        <if test="busin_type != null">
            and t1.busin_type = #{busin_type}
        </if>
        <if test="model_name != null and model_name != ''">
            and t2.model_name like concat(concat('%',#{model_name}),'%')
        </if>
        <if test="video_type != null">
            and t2.video_type = #{video_type}
        </if>
        <if test="model_type != null">
            and t2.model_type = #{model_type}
        </if>
    </select>
</mapper>
