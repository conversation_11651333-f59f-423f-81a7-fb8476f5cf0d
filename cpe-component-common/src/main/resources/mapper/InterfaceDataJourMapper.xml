<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cairh.cpe.component.common.data.mapper.InterfaceDataJourMapper">

    <select id="diySelectPage" resultType="com.cairh.cpe.component.common.form.InterfaceDataJourVo">
        SELECT JOUR_INITDATE,
            CASE JOUR_KIND
            WHEN '1' THEN '公安验证'
            WHEN '2' THEN '短信接口'
            END AS INTERFACE_TYPE,
            SUM(FAIL_NUM) AS total_fail_num,
            SUM(SUCCESS_NUM) AS total_success_num,
            COUNT(*) AS total_count,
            CONCAT(ROUND(SUM(SUCCESS_NUM)/SUM(FAIL_NUM+SUCCESS_NUM)*100, 2), '%') AS success_percentage
        FROM interfacedatajour
        WHERE JOUR_KIND IN ('1', '2')
            <if test="begin_date != null ">
                and jour_initdate >= #{begin_date}
            </if>
            <if test="end_date != null ">
                and jour_initdate <![CDATA[<=]]> #{end_date}
            </if>
            GROUP BY JOUR_INITDATE, JOUR_KIND
    </select>

    <select id="datadisplay" resultType="com.cairh.cpe.component.common.form.InterfaceDataJourDisplayVo">
        SELECT
            CASE JOUR_KIND
            WHEN '1' THEN '公安验证'
            WHEN '2' THEN '短信接口'
            END AS INTERFACE_TYPE,
            SUM(FAIL_NUM) AS total_fail_num,
            SUM(SUCCESS_NUM) AS total_success_num,
            COUNT(*) AS total_count,
            CONCAT(ROUND(SUM(SUCCESS_NUM)/SUM(FAIL_NUM+SUCCESS_NUM)*100, 2), '%') AS success_percentage
        FROM interfacedatajour
            WHERE JOUR_KIND IN ('1', '2')
            <if test="begin_date != null ">
                and jour_initdate >= #{begin_date}
            </if>
            <if test="end_date != null ">
                and jour_initdate <![CDATA[<=]]> #{end_date}
            </if>
        GROUP BY JOUR_KIND;
    </select>
</mapper>
