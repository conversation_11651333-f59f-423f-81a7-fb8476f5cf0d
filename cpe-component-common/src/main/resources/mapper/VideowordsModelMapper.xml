<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cairh.cpe.component.common.data.mapper.VideowordsmodelMapper">

    <resultMap id="queryModelAndConfig" type="com.cairh.cpe.component.common.form.VideowordsmodelDto">
        <id column="serial_id" property="serial_id" />
        <result column="model_name" property="model_name" />
        <result column="model_type" property="model_type" />
        <result column="video_type" property="video_type" />
        <result column="send_msg_words" property="send_msg_words" />
        <result column="client_tips_words" property="client_tips_words" />
        <result column="sex_apply" property="sex_apply" />
        <collection property="videowordsconfigList" ofType="com.cairh.cpe.component.common.data.entity.VideoWordsConfig">
            <result column="words_type" property="words_type" />
            <result column="words_content" property="words_content" />
            <result column="correct_answer" property="correct_answer" />
            <result column="error_answer" property="error_answer" />
            <result column="status" property="status" />
            <result column="voice_fileid" property="voice_fileid" />
            <result column="order_no" property="order_no" />
        </collection>
    </resultMap>

    <select id="queryModelAndConfig" resultMap="queryModelAndConfig">
        select
               t1.words_type,
               t1.words_content,
               t1.correct_answer,
               t1.error_answer,
               t1.voice_fileid,
               t1.order_no,
               t2.serial_id,
               t2.model_name,
               t2.model_type,
               t2.video_type,
               t2.send_msg_words,
               t2.client_tips_words,
               t2.sex_apply
        from  videowordsmodel t2 , videowordsconfig t1
        where t2.serial_id = t1.videowordsmodel_id
        <if test="video_type != null and video_type.trim().length()>0">
            and t2.video_type = #{video_type}
        </if>
        <if test="model_id_list != null and model_id_list.size() > 0">
            and t2.serial_id in
            <foreach collection="model_id_list" separator="," close=")" open="(" index="index" item="item">
                #{item}
            </foreach>
        </if>

    </select>
</mapper>
