<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cairh.cpe.component.common.data.mapper.AgreeModelBusinessMapper">
    <select id="diySelectPage" resultType="com.cairh.cpe.component.common.form.AgreemodelbusinessVo">
        select
               t1.*,
               t2.agreement_name,
               t2.agreement_no,
               t2.agreement_version,
               t2.ex_name
        from agreemodelbusiness t1
        , elecagreemodel t2
        where t1.elecagreemodel_id = t2.serial_id
        <if test="status != null and status != ''">
            and t1.status = #{status}
        </if>
        <if test="serial_id != null and serial_id != ''">
            and t1.serial_id = #{serial_id}
        </if>
        <if test="agreement_type != null and agreement_type != ''">
            and t2.agreement_type = #{agreement_type}
        </if>
        <if test="agreement_name != null and agreement_name != ''">
            and t2.agreement_name LIKE CONCAT(CONCAT('%', #{agreement_name}), '%')
        </if>
        <if test="agreement_no != null and agreement_no != ''">
            and t2.agreement_no = #{agreement_no}
        </if>
        <if test="subsys_no != null">
            and t1.subsys_no = #{subsys_no}
        </if>
        <if test="busin_type != null">
            and t1.busin_type = #{busin_type}
        </if>
        <if test="create_by != null and create_by != ''">
            and t1.create_by = #{create_by}
        </if>
        <if test="create_datetime != null ">
            and t1.create_datetime >= #{create_datetime}
        </if>
        <if test="modify_datetime != null ">
            and t1.modify_datetime >= #{modify_datetime}
        </if>
        <if test="organ_flag != null ">
            and t1.organ_flag = #{organ_flag}
        </if>
    </select>

    <update id="offAbandonElecagreemodel">
        UPDATE agreemodelbusiness SET status = '9'
        <where>
            elecagreemodel_id IN
            <foreach collection="elecagreemodelIdSet" item="emId" separator="," open="(" close=")">
                #{emId}
            </foreach>
        </where>
    </update>

    <update id="relevanceAgreeModel">
        UPDATE agreemodelbusiness SET status = #{newModel.agreement_status}, elecagreemodel_id = #{newModel.serial_id}
        <where>
            elecagreemodel_id IN
            <foreach collection="oldVersionIds" item="oldId" separator="," open="(" close=")">
                #{oldId}
            </foreach>
        </where>
    </update>
</mapper>
