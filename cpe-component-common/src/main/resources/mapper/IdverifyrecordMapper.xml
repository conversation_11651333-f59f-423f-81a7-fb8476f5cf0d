<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cairh.cpe.component.common.data.mapper.IdVerifyRecordMapper">
    <select id="verifyQuery" resultType="com.cairh.cpe.component.common.form.response.VerifyQueryResp">

        select a.factory_name factory_name,
        a.branch_no branch_no,
        to_char(a.auth_datetime, 'yyyy-MM-dd') auth_datetime,
        total_num,
        a.total_success total_success
        from (select af.branch_no branch_no,
        trunc(af.create_datetime) auth_datetime,
        sum(case af.status
        when '1' then 1
        when '0' then 1
        when '2' then 1
        end) as total_num,
        sum(case af.status
        when '1' then 1
        when '0' then 1
        end) as total_success,
        af.factory_name
        from crh_comp.idverifyrecord af where 1=1

        <if test="branch_no != null">
            and af.branch_no = #{branch_no}
        </if>

        <if test="factory_name != null">
            and af.factory_name = #{factory_name}
        </if>
        <if test="begin_time != null ">
            and af.create_datetime >= #{begin_time}
        </if>
        <if test="end_time != null ">
            and af.create_datetime &lt; #{end_time}
        </if>
        group by af.branch_no, af.factory_name, trunc(af.create_datetime)) a

    </select>

    <select id="verifyDetailQuery" resultType="com.cairh.cpe.component.common.form.response.VerifyDetailQueryResp">
        select af.SERIAL_ID,af.ID_KIND,af.ID_NO,af.FULL_NAME,af.MOBILE_TEL,af.VERIFY_TYPE,
               af.STATUS,af.RESULT_INFO,af.FILERECORD_ID,af.OPERATOR,af.FACTORY_NAME,af.TOHIS_FLAG,
               af.TOHIS_DATETIME,af.CSDC_BUSI_KIND,af.DATE_CLEAR,af.OP_BRANCH_NO,af.OP_BRANCH_NAME,af.BRANCH_NAME,
               af.OPERATOR_NO,af.OPERATOR_NAME,af.BRANCH_NO,to_char(af.create_datetime, 'yyyy-MM-dd HH24:mi:ss') create_datetime
        from idverifyrecord af
        where 1=1 and status in ('0','1','2')


        <if test="branch_no != null">
            and af.branch_no = #{branch_no}
        </if>

        <if test="op_branch_no != null">
            and af.op_branch_no = #{op_branch_no}
        </if>

        <if test="factory_name != null">
            and af.factory_name = #{factory_name}
        </if>

        <if test="begin_time != null ">
            and af.create_datetime >= #{begin_time}
        </if>
        <if test="end_time != null ">
            and af.create_datetime &lt; #{end_time}
        </if>

        <if test="id_no != null and id_no != ''">
            and af.id_no LIKE CONCAT(CONCAT('%', #{id_no}), '%')
        </if>

        <if test="verify_type != null">
            and af.verify_type = #{verify_type}
        </if>

        <if test="status != null">
            and af.status = #{status}
        </if>

        <if test="full_name != null and full_name != ''">
            and af.full_name LIKE CONCAT(CONCAT('%', #{full_name}), '%')
        </if>

    </select></mapper>
